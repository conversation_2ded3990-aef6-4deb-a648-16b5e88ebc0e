export type Environment = 'development' | 'staging' | 'production' | 'test';

export interface AppConfig {
  extensionId: string;
  translationPrefix: string;
}

export interface SliderDomConfig {
  containerId: string; // e.g., 'swiperAdContainer'
  swiperClass: string; // e.g., 'adSwiper'
}

export interface SwiperCoverflowConfig {
  rotate: number;
  depth: number;
  modifier: number;
  slideShadows: boolean;
  stretch: number;
}

export interface SwiperPaginationConfig {
  el: string;
  type: 'bullets' | 'fraction' | 'progressbar' | 'custom';
}

export interface SwiperNavigationConfig {
  nextEl: string;
  prevEl: string;
}

export interface SwiperOptionsConfig {
  spaceBetween: number;
  effect: 'slide' | 'fade' | 'cube' | 'coverflow' | 'flip' | string;
  centeredSlides: boolean;
  slidesPerView: number | 'auto';
  pagination: SwiperPaginationConfig;
  navigation: SwiperNavigationConfig;
}

export interface SliderConfig {
  maxSlides: number;
  defaultTransitionTime: number; // ms
  checkTime: number; // ms, small polling interval
  dataCheckInterval: number; // ms, UI/data polling
  dom: SliderDomConfig;
  swiper: SwiperOptionsConfig;
}

export interface UIConfig {
  headerIconId: string;
  headerIconUrl: string;
}

export interface RootConfig {
  env: Environment;
  app: AppConfig;
  slider: SliderConfig;
  ui: UIConfig;
}

// Additional types for better type safety
export interface SlideData {
  slideNumber: number;
  image: string;
  link: string;
}

export interface ErrorLogEntry {
  timestamp: Date;
  error: Error;
  context: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface MobileConfig {
  spaceBetween: number;
  slidesPerView: number;
}

export type EventType = 'touchend' | 'click';

export type NotificationType = 'error' | 'warning' | 'info';

// Flarum-specific types
export interface FlarumVnode {
  dom?: HTMLElement;
  [key: string]: unknown;
}

export type FlarumComponentAttrs = Record<string, unknown>;

export interface FlarumApp {
  forum: {
    attribute: (key: string) => unknown;
  };
  session: {
    user?: unknown;
  };
  translator: {
    trans: (key: string, params?: Record<string, unknown>) => string;
  };
}

// DOM utility types
export interface DOMElementOptions {
  className?: string;
  id?: string;
  [key: string]: unknown;
}

export interface StylesObject {
  [property: string]: string | number;
}

// Swiper-related types
export interface SwiperInstance {
  destroy: (deleteInstance?: boolean, cleanStyles?: boolean) => void;
  [key: string]: unknown;
}

// Enhanced Swiper configuration types
export interface SwiperBreakpointConfig {
  slidesPerView: number;
  spaceBetween: number;
}

export type SwiperBreakpoints = Record<number, SwiperBreakpointConfig>;

export interface SwiperAutoplayConfig {
  delay: number;
  disableOnInteraction: boolean;
}

export interface SwiperFullConfig {
  loop: boolean;
  autoplay: SwiperAutoplayConfig;
  spaceBetween: number;
  effect: string;
  centeredSlides: boolean;
  slidesPerView: number;
  breakpoints: SwiperBreakpoints;
  pagination: SwiperPaginationConfig & { clickable: boolean };
  navigation: SwiperNavigationConfig;
  initialSlide: number;
  modules: unknown[];
}

// Slide data types
export interface SlideDataRaw {
  imageSrc: string;
  imageLink: string;
  slideIndex: number;
}

export interface ProcessedSlideData {
  imageSrc: string;
  imageLink: string;
  slideIndex: number;
  isValid: boolean;
}

// Configuration calculation types
export interface ResponsiveConfig {
  mobile: SwiperBreakpointConfig;
  tablet: SwiperBreakpointConfig;
  desktop: SwiperBreakpointConfig;
}

export interface ConfigCalculationResult {
  enableLoop: boolean;
  responsiveConfig: ResponsiveConfig;
  finalConfig: SwiperFullConfig;
}

// Module interfaces
export interface ISwiperConfigManager {
  calculateConfiguration(slideCount: number, transitionTime: number): ConfigCalculationResult;
  validateConfiguration(config: SwiperFullConfig): ValidationResult;
}

export interface ISwiperDOMBuilder {
  createContainer(): HTMLElement;
  createSwiperElement(container: HTMLElement): HTMLElement;
  createSwiperWrapper(swiper: HTMLElement): HTMLElement;
  createSlide(imageSrc: string, imageLink: string): HTMLElement;
  createPagination(swiper: HTMLElement): void;
  createNavigation(swiper: HTMLElement): void;
  appendToDOM(container: HTMLElement): void;
  cleanup(): void;
}

export interface ISlideDataManager {
  fetchSlideData(): ProcessedSlideData[];
  validateSlideData(data: SlideDataRaw[]): ValidationResult;
  getTransitionTime(): number;
}

export interface ISwiperLifecycleManager {
  initialize(config: SwiperFullConfig): SwiperInstance | undefined;
  destroy(): void;
  isInitialized(): boolean;
  getInstance(): SwiperInstance | undefined;
}

// Admin types
export interface ExtensionData {
  registerSetting: (config: SettingConfig | (() => unknown)) => void;
}

export interface SettingConfig {
  setting: string;
  type: string;
  label: string;
  help?: string;
}

export interface SlideDataInternal {
  id: number;
  link: string;
  image: string;
}

// Component-specific types
export interface DynamicSlideSettingsComponentAttrs extends FlarumComponentAttrs {
  extensionId: string;
  maxSlides?: number;
}
