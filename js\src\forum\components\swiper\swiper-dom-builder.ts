import * as DOMUtils from '../../utils/dom-utils';
import { isMobileDevice } from '../../utils/mobile-detection';
import { defaultConfig } from '../../../common/config';
import type { ISwiperDOMBuilder } from '../../../common/config/types';

/**
 * Swiper DOM builder
 * Handles all DOM creation and manipulation for Swiper components
 */
export class SwiperDOM<PERSON>uilder implements ISwiperDOMBuilder {
  private container: HTMLElement | undefined;

  /**
   * Create main container element
   */
  createContainer(): HTMLElement {
    this.removeExistingNavigation();

    const container = DOMUtils.createElement('div', {
      id: defaultConfig.slider.dom.containerId,
      className: 'adContainer adContainer--forced'
    });

    this.container = container;
    return container;
  }

  /**
   * Create Swiper element
   */
  createSwiperElement(container: HTMLElement): HTMLElement {
    let className = `swiper ${defaultConfig.slider.dom.swiperClass} adSwiper--forced`;

    // Add mobile-specific class if on mobile device
    if (isMobileDevice()) {
      className += ' adSwiper--mobile';
    }

    const swiper = DOMUtils.createElement('div', {
      className
    });

    DOMUtils.appendChild(container, swiper);
    return swiper;
  }

  /**
   * Create Swiper wrapper
   */
  createSwiperWrapper(swiper: HTMLElement): HTMLElement {
    const wrapper = DOMUtils.createElement('div', {
      className: 'swiper-wrapper swiper-wrapper--forced'
    });

    DOMUtils.appendChild(swiper, wrapper);
    return wrapper;
  }

  /**
   * Create individual slide
   */
  createSlide(imageSrc: string, imageLink: string): HTMLElement {
    const slide = DOMUtils.createElement('div', {
      className: 'swiper-slide swiper-slide--forced'
    });

    let clickHandler = '';
    if (imageLink) {
      clickHandler = `window.location.href="${imageLink}"`;
    }

    // Create image with CSS class instead of inline styles
    slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' class='swiper-slide__image' />`;

    return slide;
  }

  /**
   * Create pagination element
   */
  createPagination(swiper: HTMLElement): void {
    const pagination = DOMUtils.createElement('div', {
      className: 'swiper-pagination'
    });
    DOMUtils.appendChild(swiper, pagination);
  }

  /**
   * Create navigation elements
   */
  createNavigation(swiper: HTMLElement): void {
    const prevButton = DOMUtils.createElement('div', {
      className: 'swiper-button-prev swiper-navigation-button swiper-navigation-button--prev'
    });
    const nextButton = DOMUtils.createElement('div', {
      className: 'swiper-button-next swiper-navigation-button swiper-navigation-button--next'
    });

    DOMUtils.appendChild(swiper, prevButton);
    DOMUtils.appendChild(swiper, nextButton);
  }

  /**
   * Append slideshow to DOM
   */
  appendToDOM(container: HTMLElement): void {
    const contentContainer = DOMUtils.querySelector("#content .container");
    if (contentContainer) {
      DOMUtils.prependChild(contentContainer, container);
    }
  }

  /**
   * Clean up DOM elements
   */
  cleanup(): void {
    if (this.container) {
      DOMUtils.removeElement(this.container);
      delete this.container;
    }
  }

  /**
   * Get the current container element
   */
  getContainer(): HTMLElement | undefined {
    return this.container;
  }

  /**
   * Remove existing navigation elements
   */
  private removeExistingNavigation(): void {
    const existingContainer = DOMUtils.querySelector(`#${defaultConfig.slider.dom.containerId}`);
    if (existingContainer) {
      DOMUtils.removeElement(existingContainer);
    }

    const navElements = DOMUtils.querySelectorAll(".item-nav");
    for (const element of navElements) {
      DOMUtils.removeElement(element);
    }
  }
}
