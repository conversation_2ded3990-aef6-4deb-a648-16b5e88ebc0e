import app from 'flarum/forum/app';
import { defaultConfig } from '../../../common/config';
import { SLIDESHOW_CONSTANTS, ARRAY_CONSTANTS } from '../../../common/config/constants';
import type {
  ISlideDataManager,
  ProcessedSlideData,
  SlideDataRaw,
  ValidationResult
} from '../../../common/config/types';

/**
 * Slide data manager
 * Handles fetching, processing, and validation of slide data from Flarum settings
 */
export class SlideDataManager implements ISlideDataManager {
  private readonly maxSlides = defaultConfig.slider.maxSlides;

  /**
   * Fetch and process slide data from forum settings
   */
  fetchSlideData(): ProcessedSlideData[] {
    const rawData: SlideDataRaw[] = [];

    // Collect raw slide data from forum settings
    for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= this.maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {
      const imageSrc = this.getForumAttribute(`wusong8899-header-advertisement.Image${slideIndex}`);
      const imageLink = this.getForumAttribute(`wusong8899-header-advertisement.Link${slideIndex}`);

      if (imageSrc) {
        rawData.push({
          imageSrc: String(imageSrc),
          imageLink: String(imageLink || ''),
          slideIndex
        });
      }
    }

    // Process and validate the data
    return this.processSlideData(rawData);
  }

  /**
   * Validate slide data
   */
  validateSlideData(data: SlideDataRaw[]): ValidationResult {
    const errors: string[] = [];

    if (!Array.isArray(data)) {
      errors.push('Slide data must be an array');
      return { isValid: false, errors };
    }

    if (data.length === ARRAY_CONSTANTS.EMPTY_LENGTH) {
      errors.push('No slide data provided');
      return { isValid: false, errors };
    }

    for (const slide of data) {
      if (!slide.imageSrc || typeof slide.imageSrc !== 'string') {
        errors.push(`Invalid image source for slide ${slide.slideIndex}`);
      }

      if (slide.imageLink && typeof slide.imageLink !== 'string') {
        errors.push(`Invalid image link for slide ${slide.slideIndex}`);
      }

      if (typeof slide.slideIndex !== 'number' || slide.slideIndex < SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX) {
        errors.push(`Invalid slide index: ${slide.slideIndex}`);
      }

      // Basic URL validation for image source
      if (slide.imageSrc && !this.isValidUrl(slide.imageSrc)) {
        errors.push(`Invalid image URL format for slide ${slide.slideIndex}`);
      }

      // Basic URL validation for image link (if provided)
      if (slide.imageLink && !this.isValidUrl(slide.imageLink)) {
        errors.push(`Invalid link URL format for slide ${slide.slideIndex}`);
      }
    }

    return {
      isValid: errors.length === ARRAY_CONSTANTS.EMPTY_LENGTH,
      errors
    };
  }

  /**
   * Get transition time from forum settings
   */
  getTransitionTime(): number {
    const transitionTime = this.getForumAttribute('wusong8899-header-advertisement.TransitionTime');
    if (transitionTime) {
      const parsedTime = Number.parseInt(String(transitionTime), 10);
      if (!Number.isNaN(parsedTime) && parsedTime > ARRAY_CONSTANTS.EMPTY_LENGTH) {
        return parsedTime;
      }
    }
    return defaultConfig.slider.defaultTransitionTime;
  }

  /**
   * Process raw slide data into processed format
   */
  private processSlideData(rawData: SlideDataRaw[]): ProcessedSlideData[] {
    return rawData.map((slide) => ({
      imageSrc: slide.imageSrc,
      imageLink: slide.imageLink,
      slideIndex: slide.slideIndex,
      isValid: this.isSlideValid(slide)
    })).filter((slide) => slide.isValid);
  }

  /**
   * Check if a single slide is valid
   */
  private isSlideValid(slide: SlideDataRaw): boolean {
    return Boolean(
      slide.imageSrc &&
      typeof slide.imageSrc === 'string' &&
      this.isValidUrl(slide.imageSrc) &&
      typeof slide.slideIndex === 'number' &&
      slide.slideIndex > ARRAY_CONSTANTS.EMPTY_LENGTH
    );
  }

  /**
   * Basic URL validation
   */
  private isValidUrl(url: string): boolean {
    try {
      const urlObject = new URL(url);
      return Boolean(urlObject);
    } catch {
      // Check for relative URLs or data URLs
      return url.startsWith('/') || url.startsWith('./') || url.startsWith('data:');
    }
  }

  /**
   * Safely read a forum attribute if available
   */
  private getForumAttribute(key: string): unknown {
    try {
      const forum = app && app.forum;
      const attrFn = forum && forum.attribute;
      if (typeof attrFn === 'function') {
        return attrFn.call(forum, key);
      }
      return;
    } catch {
      return;
    }
  }
}
