{"version": 3, "file": "forum.js", "sources": ["../src/forum/utils/dom-utils.ts", "../src/common/config/constants.ts", "../src/common/config/defaults.ts", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/ssr-window.esm.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/utils.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/create-element-if-not-defined.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/navigation.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/classes-to-selector.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/pagination.mjs", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/modules/autoplay.mjs", "../src/forum/components/swiper/swiper-config-manager.ts", "../src/forum/utils/mobile-detection.ts", "../src/forum/components/swiper/swiper-dom-builder.ts", "../src/forum/components/swiper/slide-data-manager.ts", "../node_modules/.pnpm/swiper@11.2.10/node_modules/swiper/shared/swiper-core.mjs", "../src/forum/components/swiper/swiper-lifecycle-manager.ts", "../src/forum/components/slideshow-manager.ts", "../src/forum/utils/error-handler.ts", "../src/forum/utils/config-manager.ts", "../src/forum/index.ts"], "sourcesContent": ["import type { DOMElementOptions, StylesObject } from '../../common/config/types';\n\n/**\n * DOM utility functions for safe DOM manipulation\n */\n\n/**\n * Safely query a single element\n */\nexport const querySelector = (selector: string): Element | false => {\n    try {\n        return document.querySelector(selector) || false;\n    } catch {\n        return false;\n    }\n};\n\n/**\n * Safely query multiple elements\n */\nexport const querySelectorAll = (selector: string): NodeListOf<Element> => {\n    try {\n        return document.querySelectorAll(selector);\n    } catch {\n        return document.querySelectorAll(''); // Returns empty NodeList\n    }\n};\n\n/**\n * Safely get element by ID\n */\nexport const getElementById = (id: string): HTMLElement | false => {\n    try {\n        return document.getElementById(id) || false;\n    } catch {\n        return false;\n    }\n};\n\n/**\n * Safely create element with options\n */\nexport const createElement = (\n    tagName: string,\n    options: DOMElementOptions = {},\n    innerHTML = ''\n): HTMLElement => {\n    try {\n        const element = document.createElement(tagName);\n\n        // Set attributes\n        for (const [key, value] of Object.entries(options)) {\n            if (key === 'className') {\n                element.className = String(value);\n            } else if (key === 'id') {\n                element.id = String(value);\n            } else {\n                element.setAttribute(key, String(value));\n            }\n        }\n\n        if (innerHTML) {\n            element.innerHTML = innerHTML;\n        }\n\n        return element;\n    } catch {\n        return document.createElement('div'); // Fallback\n    }\n};\n\n/**\n * Safely append child element\n */\nexport const appendChild = (parent: Element, child: Element): void => {\n    try {\n        parent.appendChild(child);\n    } catch {\n        // Silently handle append errors\n    }\n};\n\n/**\n * Safely prepend child element\n */\nexport const prependChild = (parent: Element, child: Element): void => {\n    try {\n        parent.prepend(child);\n    } catch {\n        // Silently handle prepend errors\n    }\n};\n\n/**\n * Safely remove element\n */\nexport const removeElement = (element: Element): void => {\n    try {\n        element.remove();\n    } catch {\n        // Silently handle removal errors\n    }\n};\n\n/**\n * Safely set styles on element\n */\nexport const setStyles = (element: HTMLElement, styles: StylesObject): void => {\n    try {\n        for (const [property, value] of Object.entries(styles)) {\n            element.style.setProperty(property, String(value));\n        }\n    } catch {\n        // Silently handle style errors\n    }\n};\n", "/**\n * Application constants for Header Advertisement extension\n */\n\n// Mobile detection constants\nexport const MO<PERSON>LE_DETECTION = {\n  USER_AGENT_SUBSTR_START: 0,\n  USER_AGENT_SUBSTR_LENGTH: 4,\n} as const;\n\n// Error handling constants\nexport const ERROR_HANDLING = {\n  MAX_ERROR_LOG_ENTRIES: 50,\n  DOM_READY_TIMEOUT: 5000,\n  SLIDE_NUMBER_MIN: 1,\n  SLIDE_NUMBER_MAX: 30,\n  TRANSITION_TIME_MIN: 1000,\n  TRANSITION_TIME_MAX: 30_000,\n  CONFIG_MAX_SLIDES_MIN: 1,\n  CONFIG_MAX_SLIDES_MAX: 50,\n} as const;\n\n// Admin component constants\nexport const ADMIN_CONSTANTS = {\n  SAVE_DEBOUNCE_DELAY: 500,\n  DEFAULT_MAX_SLIDES: 30,\n  EMPTY_SLIDES_COUNT: 0,\n} as const;\n\n// UI styling constants\nexport const UI_STYLES = {\n  HEADER_ICON_HEIGHT: 24,\n  HEADER_ICON_MARGIN_TOP: 8,\n} as const;\n\n// Mobile layout constants\nexport const MOBILE_LAYOUT = {\n  SCREEN_WIDTH_MULTIPLIER: 2,\n  SCREEN_WIDTH_OFFSET: 50,\n  CONTAINER_MARGIN_MULTIPLIER: 0.254,\n} as const;\n\n// Slideshow constants\nexport const SLIDESHOW_CONSTANTS = {\n  SLIDE_INCREMENT: 1,\n  INITIAL_SLIDE_INDEX: 1,\n  VALIDATION_ERRORS_EMPTY: 0,\n} as const;\n\n// Array and index constants\nexport const ARRAY_CONSTANTS = {\n  EMPTY_LENGTH: 0,\n  FIRST_INDEX: 0,\n  NOT_FOUND_INDEX: -1,\n  NEXT_ITEM_OFFSET: 1,\n  LAST_ITEM_OFFSET: -1,\n} as const;\n\n// Timing constants\nexport const TIMING = {\n  CHECK_INTERVAL: 10,\n  DATA_CHECK_INTERVAL: 100,\n  DEFAULT_TRANSITION_TIME: 5000,\n} as const;\n\n// DOM element constants\nexport const DOM_ELEMENTS = {\n  SWIPER_AD_CONTAINER_ID: 'swiperAdContainer',\n  HEADER_ICON_ID: 'wusong8899HeaderAdvIcon',\n} as const;\n\n// CSS class constants\nexport const CSS_CLASSES = {\n  SWIPER: 'swiper',\n  SWIPER_WRAPPER: 'swiper-wrapper',\n  SWIPER_SLIDE: 'swiper-slide',\n  SWIPER_BUTTON_NEXT: 'swiper-button-next',\n  SWIPER_BUTTON_PREV: 'swiper-button-prev',\n  SWIPER_PAGINATION: 'swiper-pagination',\n  AD_SWIPER: 'adSwiper',\n} as const;\n\n// CSS selector constants\nexport const CSS_SELECTORS = {\n  APP_NAVIGATION_BACK_CONTROL: '#app-navigation .App-backControl',\n  CONTENT_CONTAINER: '#content .container',\n  NAV_ITEMS: '.item-nav',\n  SWIPER_PAGINATION_EL: '.swiper-pagination',\n  SWIPER_BUTTON_NEXT_EL: '.swiper-button-next',\n  SWIPER_BUTTON_PREV_EL: '.swiper-button-prev',\n} as const;\n\n\n\n// Extension configuration constants\nexport const EXTENSION_CONFIG = {\n  ID: 'wusong8899-header-advertisement',\n  TRANSLATION_PREFIX: 'wusong8899-header-advertisement',\n  MAX_SLIDES: 30,\n  HEADER_ICON_URL: 'https://ex.cc/assets/files/date/test.png',\n} as const;\n", "import type { RootConfig, Environment } from './types';\r\nimport {\r\n  EXTENSION_CONFIG,\r\n  TIMING,\r\n  DOM_ELEMENTS,\r\n  CSS_CLASSES,\r\n  CSS_SELECTORS\r\n} from './constants';\r\n\r\nexport const defaultConfig: RootConfig = {\r\n  env: (process.env.NODE_ENV as Environment) || 'production',\r\n  app: {\r\n    extensionId: EXTENSION_CONFIG.ID,\r\n    translationPrefix: EXTENSION_CONFIG.TRANSLATION_PREFIX,\r\n  },\r\n  slider: {\r\n    maxSlides: EXTENSION_CONFIG.MAX_SLIDES,\r\n    defaultTransitionTime: TIMING.DEFAULT_TRANSITION_TIME,\r\n    checkTime: TIMING.CHECK_INTERVAL,\r\n    dataCheckInterval: TIMING.DATA_CHECK_INTERVAL,\r\n    dom: {\r\n      containerId: DOM_ELEMENTS.SWIPER_AD_CONTAINER_ID,\r\n      swiperClass: CSS_CLASSES.AD_SWIPER,\r\n    },\r\n    swiper: {\r\n      spaceBetween: 15,\r\n      effect: 'slide',\r\n      centeredSlides: true,\r\n      slidesPerView: 1.2, // 显示1.2个幻灯片，让前后幻灯片露出一部分\r\n      pagination: {\r\n        el: CSS_SELECTORS.SWIPER_PAGINATION_EL,\r\n        type: 'bullets',\r\n      },\r\n      navigation: {\r\n        nextEl: CSS_SELECTORS.SWIPER_BUTTON_NEXT_EL,\r\n        prevEl: CSS_SELECTORS.SWIPER_BUTTON_PREV_EL,\r\n      },\r\n    },\r\n  },\r\n  ui: {\r\n    headerIconId: DOM_ELEMENTS.HEADER_ICON_ID,\r\n    headerIconUrl: EXTENSION_CONFIG.HEADER_ICON_URL,\r\n  },\r\n};\r\n", "/**\n * SSR Window 5.0.1\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2025, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: June 27, 2025\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n  return obj !== null && typeof obj === 'object' && 'constructor' in obj && obj.constructor === Object;\n}\nfunction extend(target, src) {\n  if (target === void 0) {\n    target = {};\n  }\n  if (src === void 0) {\n    src = {};\n  }\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  Object.keys(src).filter(key => noExtend.indexOf(key) < 0).forEach(key => {\n    if (typeof target[key] === 'undefined') target[key] = src[key];else if (isObject(src[key]) && isObject(target[key]) && Object.keys(src[key]).length > 0) {\n      extend(target[key], src[key]);\n    }\n  });\n}\nconst ssrDocument = {\n  body: {},\n  addEventListener() {},\n  removeEventListener() {},\n  activeElement: {\n    blur() {},\n    nodeName: ''\n  },\n  querySelector() {\n    return null;\n  },\n  querySelectorAll() {\n    return [];\n  },\n  getElementById() {\n    return null;\n  },\n  createEvent() {\n    return {\n      initEvent() {}\n    };\n  },\n  createElement() {\n    return {\n      children: [],\n      childNodes: [],\n      style: {},\n      setAttribute() {},\n      getElementsByTagName() {\n        return [];\n      }\n    };\n  },\n  createElementNS() {\n    return {};\n  },\n  importNode() {\n    return null;\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  }\n};\nfunction getDocument() {\n  const doc = typeof document !== 'undefined' ? document : {};\n  extend(doc, ssrDocument);\n  return doc;\n}\nconst ssrWindow = {\n  document: ssrDocument,\n  navigator: {\n    userAgent: ''\n  },\n  location: {\n    hash: '',\n    host: '',\n    hostname: '',\n    href: '',\n    origin: '',\n    pathname: '',\n    protocol: '',\n    search: ''\n  },\n  history: {\n    replaceState() {},\n    pushState() {},\n    go() {},\n    back() {}\n  },\n  CustomEvent: function CustomEvent() {\n    return this;\n  },\n  addEventListener() {},\n  removeEventListener() {},\n  getComputedStyle() {\n    return {\n      getPropertyValue() {\n        return '';\n      }\n    };\n  },\n  Image() {},\n  Date() {},\n  screen: {},\n  setTimeout() {},\n  clearTimeout() {},\n  matchMedia() {\n    return {};\n  },\n  requestAnimationFrame(callback) {\n    if (typeof setTimeout === 'undefined') {\n      callback();\n      return null;\n    }\n    return setTimeout(callback, 0);\n  },\n  cancelAnimationFrame(id) {\n    if (typeof setTimeout === 'undefined') {\n      return;\n    }\n    clearTimeout(id);\n  }\n};\nfunction getWindow() {\n  const win = typeof window !== 'undefined' ? window : {};\n  extend(win, ssrWindow);\n  return win;\n}\n\nexport { getWindow as a, getDocument as g };\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\n\nfunction classesToTokens(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return classes.trim().split(' ').filter(c => !!c.trim());\n}\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {\n      // no getter for object\n    }\n    try {\n      delete object[key];\n    } catch (e) {\n      // something got wrong\n    }\n  });\n}\nfunction nextTick(callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  return setTimeout(callback, delay);\n}\nfunction now() {\n  return Date.now();\n}\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n  if (!style) {\n    style = el.style;\n  }\n  return style;\n}\nfunction getTranslate(el, axis) {\n  if (axis === void 0) {\n    axis = 'x';\n  }\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el);\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    }\n    // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42;\n    // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]);\n    // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n  return curTransform || 0;\n}\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\nfunction extend() {\n  const to = Object(arguments.length <= 0 ? undefined : arguments[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n  for (let i = 1; i < arguments.length; i += 1) {\n    const nextSource = i < 0 || arguments.length <= i ? undefined : arguments[i];\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n  return to;\n}\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\nfunction animateCSSModeScroll(_ref) {\n  let {\n    swiper,\n    targetPosition,\n    side\n  } = _ref;\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n  const animate = () => {\n    time = new Date().getTime();\n    if (startTime === null) {\n      startTime = time;\n    }\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n  animate();\n}\nfunction getSlideTransformEl(slideEl) {\n  return slideEl.querySelector('.swiper-slide-transform') || slideEl.shadowRoot && slideEl.shadowRoot.querySelector('.swiper-slide-transform') || slideEl;\n}\nfunction elementChildren(element, selector) {\n  if (selector === void 0) {\n    selector = '';\n  }\n  const window = getWindow();\n  const children = [...element.children];\n  if (window.HTMLSlotElement && element instanceof HTMLSlotElement) {\n    children.push(...element.assignedElements());\n  }\n  if (!selector) {\n    return children;\n  }\n  return children.filter(el => el.matches(selector));\n}\nfunction elementIsChildOfSlot(el, slot) {\n  // Breadth-first search through all parent's children and assigned elements\n  const elementsQueue = [slot];\n  while (elementsQueue.length > 0) {\n    const elementToCheck = elementsQueue.shift();\n    if (el === elementToCheck) {\n      return true;\n    }\n    elementsQueue.push(...elementToCheck.children, ...(elementToCheck.shadowRoot ? elementToCheck.shadowRoot.children : []), ...(elementToCheck.assignedElements ? elementToCheck.assignedElements() : []));\n  }\n}\nfunction elementIsChildOf(el, parent) {\n  const window = getWindow();\n  let isChild = parent.contains(el);\n  if (!isChild && window.HTMLSlotElement && parent instanceof HTMLSlotElement) {\n    const children = [...parent.assignedElements()];\n    isChild = children.includes(el);\n    if (!isChild) {\n      isChild = elementIsChildOfSlot(el, parent);\n    }\n  }\n  return isChild;\n}\nfunction showWarning(text) {\n  try {\n    console.warn(text);\n    return;\n  } catch (err) {\n    // err\n  }\n}\nfunction createElement(tag, classes) {\n  if (classes === void 0) {\n    classes = [];\n  }\n  const el = document.createElement(tag);\n  el.classList.add(...(Array.isArray(classes) ? classes : classesToTokens(classes)));\n  return el;\n}\nfunction elementOffset(el) {\n  const window = getWindow();\n  const document = getDocument();\n  const box = el.getBoundingClientRect();\n  const body = document.body;\n  const clientTop = el.clientTop || body.clientTop || 0;\n  const clientLeft = el.clientLeft || body.clientLeft || 0;\n  const scrollTop = el === window ? window.scrollY : el.scrollTop;\n  const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n  return {\n    top: box.top + scrollTop - clientTop,\n    left: box.left + scrollLeft - clientLeft\n  };\n}\nfunction elementPrevAll(el, selector) {\n  const prevEls = [];\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n    if (selector) {\n      if (prev.matches(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n    el = prev;\n  }\n  return prevEls;\n}\nfunction elementNextAll(el, selector) {\n  const nextEls = [];\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n    if (selector) {\n      if (next.matches(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n    el = next;\n  }\n  return nextEls;\n}\nfunction elementStyle(el, prop) {\n  const window = getWindow();\n  return window.getComputedStyle(el, null).getPropertyValue(prop);\n}\nfunction elementIndex(el) {\n  let child = el;\n  let i;\n  if (child) {\n    i = 0;\n    // eslint-disable-next-line\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n    return i;\n  }\n  return undefined;\n}\nfunction elementParents(el, selector) {\n  const parents = []; // eslint-disable-line\n  let parent = el.parentElement; // eslint-disable-line\n  while (parent) {\n    if (selector) {\n      if (parent.matches(selector)) parents.push(parent);\n    } else {\n      parents.push(parent);\n    }\n    parent = parent.parentElement;\n  }\n  return parents;\n}\nfunction elementTransitionEnd(el, callback) {\n  function fireCallBack(e) {\n    if (e.target !== el) return;\n    callback.call(el, e);\n    el.removeEventListener('transitionend', fireCallBack);\n  }\n  if (callback) {\n    el.addEventListener('transitionend', fireCallBack);\n  }\n}\nfunction elementOuterSize(el, size, includeMargins) {\n  const window = getWindow();\n  if (includeMargins) {\n    return el[size === 'width' ? 'offsetWidth' : 'offsetHeight'] + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-right' : 'margin-top')) + parseFloat(window.getComputedStyle(el, null).getPropertyValue(size === 'width' ? 'margin-left' : 'margin-bottom'));\n  }\n  return el.offsetWidth;\n}\nfunction makeElementsArray(el) {\n  return (Array.isArray(el) ? el : [el]).filter(e => !!e);\n}\nfunction getRotateFix(swiper) {\n  return v => {\n    if (Math.abs(v) > 0 && swiper.browser && swiper.browser.need3dFix && Math.abs(v) % 90 === 0) {\n      return v + 0.001;\n    }\n    return v;\n  };\n}\nfunction setInnerHTML(el, html) {\n  if (html === void 0) {\n    html = '';\n  }\n  if (typeof trustedTypes !== 'undefined') {\n    el.innerHTML = trustedTypes.createPolicy('html', {\n      createHTML: s => s\n    }).createHTML(html);\n  } else {\n    el.innerHTML = html;\n  }\n}\n\nexport { setCSSProperty as a, elementParents as b, createElement as c, elementOffset as d, elementChildren as e, now as f, getSlideTransformEl as g, elementOuterSize as h, elementIndex as i, classesToTokens as j, getTranslate as k, elementTransitionEnd as l, makeElementsArray as m, nextTick as n, isObject as o, getRotateFix as p, elementStyle as q, elementNextAll as r, setInnerHTML as s, elementPrevAll as t, animateCSSModeScroll as u, showWarning as v, elementIsChildOf as w, extend as x, deleteProps as y };\n", "import { e as elementChildren, c as createElement } from './utils.mjs';\n\nfunction createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = elementChildren(swiper.el, `.${checkProps[key]}`)[0];\n        if (!element) {\n          element = createElement('div', checkProps[key]);\n          element.className = checkProps[key];\n          swiper.el.append(element);\n        }\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n  return params;\n}\n\nexport { createElementIfNotDefined as c };\n", "import { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray } from '../shared/utils.mjs';\n\nfunction Navigation(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  extendParams({\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n      navigationDisabledClass: 'swiper-navigation-disabled'\n    }\n  });\n  swiper.navigation = {\n    nextEl: null,\n    prevEl: null\n  };\n  function getEl(el) {\n    let res;\n    if (el && typeof el === 'string' && swiper.isElement) {\n      res = swiper.el.querySelector(el) || swiper.hostEl.querySelector(el);\n      if (res) return res;\n    }\n    if (el) {\n      if (typeof el === 'string') res = [...document.querySelectorAll(el)];\n      if (swiper.params.uniqueNavElements && typeof el === 'string' && res && res.length > 1 && swiper.el.querySelectorAll(el).length === 1) {\n        res = swiper.el.querySelector(el);\n      } else if (res && res.length === 1) {\n        res = res[0];\n      }\n    }\n    if (el && !res) return el;\n    // if (Array.isArray(res) && res.length === 1) res = res[0];\n    return res;\n  }\n  function toggleEl(el, disabled) {\n    const params = swiper.params.navigation;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (subEl) {\n        subEl.classList[disabled ? 'add' : 'remove'](...params.disabledClass.split(' '));\n        if (subEl.tagName === 'BUTTON') subEl.disabled = disabled;\n        if (swiper.params.watchOverflow && swiper.enabled) {\n          subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n        }\n      }\n    });\n  }\n  function update() {\n    // Update Navigation Buttons\n    const {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    if (swiper.params.loop) {\n      toggleEl(prevEl, false);\n      toggleEl(nextEl, false);\n      return;\n    }\n    toggleEl(prevEl, swiper.isBeginning && !swiper.params.rewind);\n    toggleEl(nextEl, swiper.isEnd && !swiper.params.rewind);\n  }\n  function onPrevClick(e) {\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slidePrev();\n    emit('navigationPrev');\n  }\n  function onNextClick(e) {\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slideNext();\n    emit('navigationNext');\n  }\n  function init() {\n    const params = swiper.params.navigation;\n    swiper.params.navigation = createElementIfNotDefined(swiper, swiper.originalParams.navigation, swiper.params.navigation, {\n      nextEl: 'swiper-button-next',\n      prevEl: 'swiper-button-prev'\n    });\n    if (!(params.nextEl || params.prevEl)) return;\n    let nextEl = getEl(params.nextEl);\n    let prevEl = getEl(params.prevEl);\n    Object.assign(swiper.navigation, {\n      nextEl,\n      prevEl\n    });\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const initButton = (el, dir) => {\n      if (el) {\n        el.addEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      }\n      if (!swiper.enabled && el) {\n        el.classList.add(...params.lockClass.split(' '));\n      }\n    };\n    nextEl.forEach(el => initButton(el, 'next'));\n    prevEl.forEach(el => initButton(el, 'prev'));\n  }\n  function destroy() {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const destroyButton = (el, dir) => {\n      el.removeEventListener('click', dir === 'next' ? onNextClick : onPrevClick);\n      el.classList.remove(...swiper.params.navigation.disabledClass.split(' '));\n    };\n    nextEl.forEach(el => destroyButton(el, 'next'));\n    prevEl.forEach(el => destroyButton(el, 'prev'));\n  }\n  on('init', () => {\n    if (swiper.params.navigation.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      update();\n    }\n  });\n  on('toEdge fromEdge lock unlock', () => {\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    if (swiper.enabled) {\n      update();\n      return;\n    }\n    [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.add(swiper.params.navigation.lockClass));\n  });\n  on('click', (_s, e) => {\n    let {\n      nextEl,\n      prevEl\n    } = swiper.navigation;\n    nextEl = makeElementsArray(nextEl);\n    prevEl = makeElementsArray(prevEl);\n    const targetEl = e.target;\n    let targetIsButton = prevEl.includes(targetEl) || nextEl.includes(targetEl);\n    if (swiper.isElement && !targetIsButton) {\n      const path = e.path || e.composedPath && e.composedPath();\n      if (path) {\n        targetIsButton = path.find(pathEl => nextEl.includes(pathEl) || prevEl.includes(pathEl));\n      }\n    }\n    if (swiper.params.navigation.hideOnClick && !targetIsButton) {\n      if (swiper.pagination && swiper.params.pagination && swiper.params.pagination.clickable && (swiper.pagination.el === targetEl || swiper.pagination.el.contains(targetEl))) return;\n      let isHidden;\n      if (nextEl.length) {\n        isHidden = nextEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      } else if (prevEl.length) {\n        isHidden = prevEl[0].classList.contains(swiper.params.navigation.hiddenClass);\n      }\n      if (isHidden === true) {\n        emit('navigationShow');\n      } else {\n        emit('navigationHide');\n      }\n      [...nextEl, ...prevEl].filter(el => !!el).forEach(el => el.classList.toggle(swiper.params.navigation.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    init();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(...swiper.params.navigation.navigationDisabledClass.split(' '));\n    destroy();\n  };\n  Object.assign(swiper.navigation, {\n    enable,\n    disable,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Navigation as default };\n", "function classesToSelector(classes) {\n  if (classes === void 0) {\n    classes = '';\n  }\n  return `.${classes.trim().replace(/([\\.:!+\\/()[\\]])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}\n\nexport { classesToSelector as c };\n", "import { c as classesToSelector } from '../shared/classes-to-selector.mjs';\nimport { c as createElementIfNotDefined } from '../shared/create-element-if-not-defined.mjs';\nimport { m as makeElementsArray, h as elementOuterSize, i as elementIndex, s as setInnerHTML, b as elementParents } from '../shared/utils.mjs';\n\nfunction Pagination(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || Array.isArray(swiper.pagination.el) && swiper.pagination.el.length === 0;\n  }\n  function setSideBullets(bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    if (!bulletEl) return;\n    bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n    if (bulletEl) {\n      bulletEl.classList.add(`${bulletActiveClass}-${position}`);\n      bulletEl = bulletEl[`${position === 'prev' ? 'previous' : 'next'}ElementSibling`];\n      if (bulletEl) {\n        bulletEl.classList.add(`${bulletActiveClass}-${position}-${position}`);\n      }\n    }\n  }\n  function getMoveDirection(prevIndex, nextIndex, length) {\n    prevIndex = prevIndex % length;\n    nextIndex = nextIndex % length;\n    if (nextIndex === prevIndex + 1) {\n      return 'next';\n    } else if (nextIndex === prevIndex - 1) {\n      return 'previous';\n    }\n    return;\n  }\n  function onBulletClick(e) {\n    const bulletEl = e.target.closest(classesToSelector(swiper.params.pagination.bulletClass));\n    if (!bulletEl) {\n      return;\n    }\n    e.preventDefault();\n    const index = elementIndex(bulletEl) * swiper.params.slidesPerGroup;\n    if (swiper.params.loop) {\n      if (swiper.realIndex === index) return;\n      const moveDirection = getMoveDirection(swiper.realIndex, index, swiper.slides.length);\n      if (moveDirection === 'next') {\n        swiper.slideNext();\n      } else if (moveDirection === 'previous') {\n        swiper.slidePrev();\n      } else {\n        swiper.slideToLoop(index);\n      }\n    } else {\n      swiper.slideTo(index);\n    }\n  }\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    // Current/Total\n    let current;\n    let previousIndex;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const total = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n    if (swiper.params.loop) {\n      previousIndex = swiper.previousRealIndex || 0;\n      current = swiper.params.slidesPerGroup > 1 ? Math.floor(swiper.realIndex / swiper.params.slidesPerGroup) : swiper.realIndex;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n      previousIndex = swiper.previousSnapIndex;\n    } else {\n      previousIndex = swiper.previousIndex || 0;\n      current = swiper.activeIndex || 0;\n    }\n    // Types\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n      if (params.dynamicBullets) {\n        bulletSize = elementOuterSize(bullets[0], swiper.isHorizontal() ? 'width' : 'height', true);\n        el.forEach(subEl => {\n          subEl.style[swiper.isHorizontal() ? 'width' : 'height'] = `${bulletSize * (params.dynamicMainBullets + 4)}px`;\n        });\n        if (params.dynamicMainBullets > 1 && previousIndex !== undefined) {\n          dynamicBulletIndex += current - (previousIndex || 0);\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n      bullets.forEach(bulletEl => {\n        const classesToRemove = [...['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`)].map(s => typeof s === 'string' && s.includes(' ') ? s.split(' ') : s).flat();\n        bulletEl.classList.remove(...classesToRemove);\n      });\n      if (el.length > 1) {\n        bullets.forEach(bullet => {\n          const bulletIndex = elementIndex(bullet);\n          if (bulletIndex === current) {\n            bullet.classList.add(...params.bulletActiveClass.split(' '));\n          } else if (swiper.isElement) {\n            bullet.setAttribute('part', 'bullet');\n          }\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              bullet.classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n            if (bulletIndex === firstIndex) {\n              setSideBullets(bullet, 'prev');\n            }\n            if (bulletIndex === lastIndex) {\n              setSideBullets(bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const bullet = bullets[current];\n        if (bullet) {\n          bullet.classList.add(...params.bulletActiveClass.split(' '));\n        }\n        if (swiper.isElement) {\n          bullets.forEach((bulletEl, bulletIndex) => {\n            bulletEl.setAttribute('part', bulletIndex === current ? 'bullet-active' : 'bullet');\n          });\n        }\n        if (params.dynamicBullets) {\n          const firstDisplayedBullet = bullets[firstIndex];\n          const lastDisplayedBullet = bullets[lastIndex];\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            if (bullets[i]) {\n              bullets[i].classList.add(...`${params.bulletActiveClass}-main`.split(' '));\n            }\n          }\n          setSideBullets(firstDisplayedBullet, 'prev');\n          setSideBullets(lastDisplayedBullet, 'next');\n        }\n      }\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.forEach(bullet => {\n          bullet.style[swiper.isHorizontal() ? offsetProp : 'top'] = `${bulletsOffset}px`;\n        });\n      }\n    }\n    el.forEach((subEl, subElIndex) => {\n      if (params.type === 'fraction') {\n        subEl.querySelectorAll(classesToSelector(params.currentClass)).forEach(fractionEl => {\n          fractionEl.textContent = params.formatFractionCurrent(current + 1);\n        });\n        subEl.querySelectorAll(classesToSelector(params.totalClass)).forEach(totalEl => {\n          totalEl.textContent = params.formatFractionTotal(total);\n        });\n      }\n      if (params.type === 'progressbar') {\n        let progressbarDirection;\n        if (params.progressbarOpposite) {\n          progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n        } else {\n          progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n        }\n        const scale = (current + 1) / total;\n        let scaleX = 1;\n        let scaleY = 1;\n        if (progressbarDirection === 'horizontal') {\n          scaleX = scale;\n        } else {\n          scaleY = scale;\n        }\n        subEl.querySelectorAll(classesToSelector(params.progressbarFillClass)).forEach(progressEl => {\n          progressEl.style.transform = `translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`;\n          progressEl.style.transitionDuration = `${swiper.params.speed}ms`;\n        });\n      }\n      if (params.type === 'custom' && params.renderCustom) {\n        setInnerHTML(subEl, params.renderCustom(swiper, current + 1, total));\n        if (subElIndex === 0) emit('paginationRender', subEl);\n      } else {\n        if (subElIndex === 0) emit('paginationRender', subEl);\n        emit('paginationUpdate', subEl);\n      }\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        subEl.classList[swiper.isLocked ? 'add' : 'remove'](params.lockClass);\n      }\n    });\n  }\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.grid && swiper.params.grid.rows > 1 ? swiper.slides.length / Math.ceil(swiper.params.grid.rows) : swiper.slides.length;\n    let el = swiper.pagination.el;\n    el = makeElementsArray(el);\n    let paginationHTML = '';\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil(slidesLength / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          // prettier-ignore\n          paginationHTML += `<${params.bulletElement} ${swiper.isElement ? 'part=\"bullet\"' : ''} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n    }\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n    }\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n    }\n    swiper.pagination.bullets = [];\n    el.forEach(subEl => {\n      if (params.type !== 'custom') {\n        setInnerHTML(subEl, paginationHTML || '');\n      }\n      if (params.type === 'bullets') {\n        swiper.pagination.bullets.push(...subEl.querySelectorAll(classesToSelector(params.bulletClass)));\n      }\n    });\n    if (params.type !== 'custom') {\n      emit('paginationRender', el[0]);\n    }\n  }\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let el;\n    if (typeof params.el === 'string' && swiper.isElement) {\n      el = swiper.el.querySelector(params.el);\n    }\n    if (!el && typeof params.el === 'string') {\n      el = [...document.querySelectorAll(params.el)];\n    }\n    if (!el) {\n      el = params.el;\n    }\n    if (!el || el.length === 0) return;\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && Array.isArray(el) && el.length > 1) {\n      el = [...swiper.el.querySelectorAll(params.el)];\n      // check if it belongs to another nested Swiper\n      if (el.length > 1) {\n        el = el.find(subEl => {\n          if (elementParents(subEl, '.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n    if (Array.isArray(el) && el.length === 1) el = el[0];\n    Object.assign(swiper.pagination, {\n      el\n    });\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      if (params.type === 'bullets' && params.clickable) {\n        subEl.classList.add(...(params.clickableClass || '').split(' '));\n      }\n      subEl.classList.add(params.modifierClass + params.type);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n      if (params.type === 'bullets' && params.dynamicBullets) {\n        subEl.classList.add(`${params.modifierClass}${params.type}-dynamic`);\n        dynamicBulletIndex = 0;\n        if (params.dynamicMainBullets < 1) {\n          params.dynamicMainBullets = 1;\n        }\n      }\n      if (params.type === 'progressbar' && params.progressbarOpposite) {\n        subEl.classList.add(params.progressbarOppositeClass);\n      }\n      if (params.clickable) {\n        subEl.addEventListener('click', onBulletClick);\n      }\n      if (!swiper.enabled) {\n        subEl.classList.add(params.lockClass);\n      }\n    });\n  }\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    let el = swiper.pagination.el;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => {\n        subEl.classList.remove(params.hiddenClass);\n        subEl.classList.remove(params.modifierClass + params.type);\n        subEl.classList.remove(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n        if (params.clickable) {\n          subEl.classList.remove(...(params.clickableClass || '').split(' '));\n          subEl.removeEventListener('click', onBulletClick);\n        }\n      });\n    }\n    if (swiper.pagination.bullets) swiper.pagination.bullets.forEach(subEl => subEl.classList.remove(...params.bulletActiveClass.split(' ')));\n  }\n  on('changeDirection', () => {\n    if (!swiper.pagination || !swiper.pagination.el) return;\n    const params = swiper.params.pagination;\n    let {\n      el\n    } = swiper.pagination;\n    el = makeElementsArray(el);\n    el.forEach(subEl => {\n      subEl.classList.remove(params.horizontalClass, params.verticalClass);\n      subEl.classList.add(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    });\n  });\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    update();\n  });\n  on('snapGridLengthChange', () => {\n    render();\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList[swiper.enabled ? 'remove' : 'add'](swiper.params.pagination.lockClass));\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const el = makeElementsArray(swiper.pagination.el);\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && el && el.length > 0 && !targetEl.classList.contains(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = el[0].classList.contains(swiper.params.pagination.hiddenClass);\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n      el.forEach(subEl => subEl.classList.toggle(swiper.params.pagination.hiddenClass));\n    }\n  });\n  const enable = () => {\n    swiper.el.classList.remove(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.remove(swiper.params.pagination.paginationDisabledClass));\n    }\n    init();\n    render();\n    update();\n  };\n  const disable = () => {\n    swiper.el.classList.add(swiper.params.pagination.paginationDisabledClass);\n    let {\n      el\n    } = swiper.pagination;\n    if (el) {\n      el = makeElementsArray(el);\n      el.forEach(subEl => subEl.classList.add(swiper.params.pagination.paginationDisabledClass));\n    }\n    destroy();\n  };\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}\n\nexport { Pagination as default };\n", "import { g as getDocument } from '../shared/ssr-window.esm.mjs';\n\n/* eslint no-underscore-dangle: \"off\" */\n/* eslint no-use-before-define: \"off\" */\nfunction Autoplay(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit,\n    params\n  } = _ref;\n  swiper.autoplay = {\n    running: false,\n    paused: false,\n    timeLeft: 0\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: false,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n  let timeout;\n  let raf;\n  let autoplayDelayTotal = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayDelayCurrent = params && params.autoplay ? params.autoplay.delay : 3000;\n  let autoplayTimeLeft;\n  let autoplayStartTime = new Date().getTime();\n  let wasPaused;\n  let isTouched;\n  let pausedByTouch;\n  let touchStartTimeout;\n  let slideChanged;\n  let pausedByInteraction;\n  let pausedByPointerEnter;\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.wrapperEl) return;\n    if (e.target !== swiper.wrapperEl) return;\n    swiper.wrapperEl.removeEventListener('transitionend', onTransitionEnd);\n    if (pausedByPointerEnter || e.detail && e.detail.bySwiperTouchMove) {\n      return;\n    }\n    resume();\n  }\n  const calcTimeLeft = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) {\n      wasPaused = true;\n    } else if (wasPaused) {\n      autoplayDelayCurrent = autoplayTimeLeft;\n      wasPaused = false;\n    }\n    const timeLeft = swiper.autoplay.paused ? autoplayTimeLeft : autoplayStartTime + autoplayDelayCurrent - new Date().getTime();\n    swiper.autoplay.timeLeft = timeLeft;\n    emit('autoplayTimeLeft', timeLeft, timeLeft / autoplayDelayTotal);\n    raf = requestAnimationFrame(() => {\n      calcTimeLeft();\n    });\n  };\n  const getSlideDelay = () => {\n    let activeSlideEl;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      activeSlideEl = swiper.slides.find(slideEl => slideEl.classList.contains('swiper-slide-active'));\n    } else {\n      activeSlideEl = swiper.slides[swiper.activeIndex];\n    }\n    if (!activeSlideEl) return undefined;\n    const currentSlideDelay = parseInt(activeSlideEl.getAttribute('data-swiper-autoplay'), 10);\n    return currentSlideDelay;\n  };\n  const run = delayForce => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    cancelAnimationFrame(raf);\n    calcTimeLeft();\n    let delay = typeof delayForce === 'undefined' ? swiper.params.autoplay.delay : delayForce;\n    autoplayDelayTotal = swiper.params.autoplay.delay;\n    autoplayDelayCurrent = swiper.params.autoplay.delay;\n    const currentSlideDelay = getSlideDelay();\n    if (!Number.isNaN(currentSlideDelay) && currentSlideDelay > 0 && typeof delayForce === 'undefined') {\n      delay = currentSlideDelay;\n      autoplayDelayTotal = currentSlideDelay;\n      autoplayDelayCurrent = currentSlideDelay;\n    }\n    autoplayTimeLeft = delay;\n    const speed = swiper.params.speed;\n    const proceed = () => {\n      if (!swiper || swiper.destroyed) return;\n      if (swiper.params.autoplay.reverseDirection) {\n        if (!swiper.isBeginning || swiper.params.loop || swiper.params.rewind) {\n          swiper.slidePrev(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(swiper.slides.length - 1, speed, true, true);\n          emit('autoplay');\n        }\n      } else {\n        if (!swiper.isEnd || swiper.params.loop || swiper.params.rewind) {\n          swiper.slideNext(speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          swiper.slideTo(0, speed, true, true);\n          emit('autoplay');\n        }\n      }\n      if (swiper.params.cssMode) {\n        autoplayStartTime = new Date().getTime();\n        requestAnimationFrame(() => {\n          run();\n        });\n      }\n    };\n    if (delay > 0) {\n      clearTimeout(timeout);\n      timeout = setTimeout(() => {\n        proceed();\n      }, delay);\n    } else {\n      requestAnimationFrame(() => {\n        proceed();\n      });\n    }\n\n    // eslint-disable-next-line\n    return delay;\n  };\n  const start = () => {\n    autoplayStartTime = new Date().getTime();\n    swiper.autoplay.running = true;\n    run();\n    emit('autoplayStart');\n  };\n  const stop = () => {\n    swiper.autoplay.running = false;\n    clearTimeout(timeout);\n    cancelAnimationFrame(raf);\n    emit('autoplayStop');\n  };\n  const pause = (internal, reset) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    clearTimeout(timeout);\n    if (!internal) {\n      pausedByInteraction = true;\n    }\n    const proceed = () => {\n      emit('autoplayPause');\n      if (swiper.params.autoplay.waitForTransition) {\n        swiper.wrapperEl.addEventListener('transitionend', onTransitionEnd);\n      } else {\n        resume();\n      }\n    };\n    swiper.autoplay.paused = true;\n    if (reset) {\n      if (slideChanged) {\n        autoplayTimeLeft = swiper.params.autoplay.delay;\n      }\n      slideChanged = false;\n      proceed();\n      return;\n    }\n    const delay = autoplayTimeLeft || swiper.params.autoplay.delay;\n    autoplayTimeLeft = delay - (new Date().getTime() - autoplayStartTime);\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop) return;\n    if (autoplayTimeLeft < 0) autoplayTimeLeft = 0;\n    proceed();\n  };\n  const resume = () => {\n    if (swiper.isEnd && autoplayTimeLeft < 0 && !swiper.params.loop || swiper.destroyed || !swiper.autoplay.running) return;\n    autoplayStartTime = new Date().getTime();\n    if (pausedByInteraction) {\n      pausedByInteraction = false;\n      run(autoplayTimeLeft);\n    } else {\n      run();\n    }\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n  };\n  const onVisibilityChange = () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    const document = getDocument();\n    if (document.visibilityState === 'hidden') {\n      pausedByInteraction = true;\n      pause(true);\n    }\n    if (document.visibilityState === 'visible') {\n      resume();\n    }\n  };\n  const onPointerEnter = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByInteraction = true;\n    pausedByPointerEnter = true;\n    if (swiper.animating || swiper.autoplay.paused) return;\n    pause(true);\n  };\n  const onPointerLeave = e => {\n    if (e.pointerType !== 'mouse') return;\n    pausedByPointerEnter = false;\n    if (swiper.autoplay.paused) {\n      resume();\n    }\n  };\n  const attachMouseEvents = () => {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.el.addEventListener('pointerenter', onPointerEnter);\n      swiper.el.addEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const detachMouseEvents = () => {\n    if (swiper.el && typeof swiper.el !== 'string') {\n      swiper.el.removeEventListener('pointerenter', onPointerEnter);\n      swiper.el.removeEventListener('pointerleave', onPointerLeave);\n    }\n  };\n  const attachDocumentEvents = () => {\n    const document = getDocument();\n    document.addEventListener('visibilitychange', onVisibilityChange);\n  };\n  const detachDocumentEvents = () => {\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  };\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      attachMouseEvents();\n      attachDocumentEvents();\n      start();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n    detachDocumentEvents();\n    if (swiper.autoplay.running) {\n      stop();\n    }\n  });\n  on('_freeModeStaticRelease', () => {\n    if (pausedByTouch || pausedByInteraction) {\n      resume();\n    }\n  });\n  on('_freeModeNoMomentumRelease', () => {\n    if (!swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (internal || !swiper.params.autoplay.disableOnInteraction) {\n      pause(true, true);\n    } else {\n      stop();\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n      return;\n    }\n    isTouched = true;\n    pausedByTouch = false;\n    pausedByInteraction = false;\n    touchStartTimeout = setTimeout(() => {\n      pausedByInteraction = true;\n      pausedByTouch = true;\n      pause(true);\n    }, 200);\n  });\n  on('touchEnd', () => {\n    if (swiper.destroyed || !swiper.autoplay.running || !isTouched) return;\n    clearTimeout(touchStartTimeout);\n    clearTimeout(timeout);\n    if (swiper.params.autoplay.disableOnInteraction) {\n      pausedByTouch = false;\n      isTouched = false;\n      return;\n    }\n    if (pausedByTouch && swiper.params.cssMode) resume();\n    pausedByTouch = false;\n    isTouched = false;\n  });\n  on('slideChange', () => {\n    if (swiper.destroyed || !swiper.autoplay.running) return;\n    slideChanged = true;\n  });\n  Object.assign(swiper.autoplay, {\n    start,\n    stop,\n    pause,\n    resume\n  });\n}\n\nexport { Autoplay as default };\n", "import { Navigation, Pagination, Autoplay } from 'swiper/modules';\nimport { defaultConfig } from '../../../common/config';\nimport { ARRAY_CONSTANTS } from '../../../common/config/constants';\nimport type {\n  ISwiperConfigManager,\n  ConfigCalculationResult,\n  SwiperFullConfig,\n  ResponsiveConfig,\n  SwiperBreakpoints,\n  ValidationResult\n} from '../../../common/config/types';\n\n/**\n * Swiper configuration constants\n * Based on Swiper documentation requirements for loop mode\n */\nconst SWIPER_CONFIG_CONSTANTS = {\n  MIN_SLIDES_FOR_DUAL_VIEW: 8,\n  // For slidesPerView: 1.2 with centeredSlides, need at least 8 slides for stable loop\n  // This is more conservative to avoid the loop warning\n  MIN_SLIDES_FOR_PARTIAL_VIEW_LOOP: 8,\n  // For slidesPerView: 1 with centeredSlides, need at least 3 slides\n  MIN_SLIDES_FOR_SINGLE_VIEW_LOOP: 3,\n  // Responsive slidesPerView values\n  MOBILE_SLIDES_PER_VIEW: 1,\n  TABLET_SLIDES_PER_VIEW_WITH_LOOP: 1.1,\n  TABLET_SLIDES_PER_VIEW_NO_LOOP: 1,\n  FALLBACK_SLIDES_PER_VIEW: 1,\n} as const;\n\n/**\n * Swiper configuration manager\n * Handles all Swiper configuration generation, validation, and responsive calculations\n */\nexport class SwiperConfigManager implements ISwiperConfigManager {\n  /**\n   * Calculate complete Swiper configuration based on slide count and transition time\n   */\n  calculateConfiguration(slideCount: number, transitionTime: number): ConfigCalculationResult {\n    const enableLoop = this.shouldEnableLoop(slideCount);\n    const responsiveConfig = this.calculateResponsiveConfig(enableLoop);\n    const finalConfig = this.buildFinalConfig(responsiveConfig, transitionTime, enableLoop);\n\n    return {\n      enableLoop,\n      responsiveConfig,\n      finalConfig\n    };\n  }\n\n  /**\n   * Validate Swiper configuration\n   */\n  validateConfiguration(config: SwiperFullConfig): ValidationResult {\n    const errors: string[] = [];\n\n    // Validate required properties\n    if (!config.autoplay || typeof config.autoplay.delay !== 'number' || config.autoplay.delay <= ARRAY_CONSTANTS.EMPTY_LENGTH) {\n      errors.push('Invalid autoplay delay configuration');\n    }\n\n    if (typeof config.spaceBetween !== 'number' || config.spaceBetween < ARRAY_CONSTANTS.EMPTY_LENGTH) {\n      errors.push('Invalid spaceBetween configuration');\n    }\n\n    if (typeof config.slidesPerView !== 'number' || config.slidesPerView <= ARRAY_CONSTANTS.EMPTY_LENGTH) {\n      errors.push('Invalid slidesPerView configuration');\n    }\n\n    // Validate breakpoints\n    if (!config.breakpoints || typeof config.breakpoints !== 'object') {\n      errors.push('Invalid breakpoints configuration');\n    } else {\n      for (const [width, breakpointConfig] of Object.entries(config.breakpoints)) {\n        const widthNum = Number(width);\n        if (Number.isNaN(widthNum) || widthNum <= ARRAY_CONSTANTS.EMPTY_LENGTH) {\n          errors.push(`Invalid breakpoint width: ${width}`);\n        }\n        if (typeof breakpointConfig.slidesPerView !== 'number' || breakpointConfig.slidesPerView <= ARRAY_CONSTANTS.EMPTY_LENGTH) {\n          errors.push(`Invalid slidesPerView for breakpoint ${width}`);\n        }\n        if (typeof breakpointConfig.spaceBetween !== 'number' || breakpointConfig.spaceBetween < ARRAY_CONSTANTS.EMPTY_LENGTH) {\n          errors.push(`Invalid spaceBetween for breakpoint ${width}`);\n        }\n      }\n    }\n\n    // Validate pagination\n    if (!config.pagination || !config.pagination.el || typeof config.pagination.el !== 'string') {\n      errors.push('Invalid pagination element selector');\n    }\n\n    // Validate navigation\n    if (!config.navigation || !config.navigation.nextEl || !config.navigation.prevEl) {\n      errors.push('Invalid navigation element selectors');\n    }\n\n    return {\n      isValid: errors.length === ARRAY_CONSTANTS.EMPTY_LENGTH,\n      errors\n    };\n  }\n\n  /**\n   * Determine if loop mode should be enabled based on slide count\n   */\n  private shouldEnableLoop(_slideCount: number): boolean {\n    // Disable loop mode to avoid conflicts with other Swiper instances on the page\n    // This prevents the transform calculation errors and slide positioning issues\n    return false;\n\n    // Future implementation when loop issues are resolved:\n    // const defaultSlidesPerView = defaultConfig.slider.swiper.slidesPerView as number;\n    // if (defaultSlidesPerView > 1) {\n    //   return slideCount >= SWIPER_CONFIG_CONSTANTS.MIN_SLIDES_FOR_PARTIAL_VIEW_LOOP;\n    // }\n    // return slideCount >= SWIPER_CONFIG_CONSTANTS.MIN_SLIDES_FOR_SINGLE_VIEW_LOOP;\n  }\n\n  /**\n   * Calculate responsive configuration based on loop capability\n   */\n  private calculateResponsiveConfig(enableLoop: boolean): ResponsiveConfig {\n    const defaultSlidesPerView = defaultConfig.slider.swiper.slidesPerView as number;\n\n    let tabletSlidesPerView = SWIPER_CONFIG_CONSTANTS.TABLET_SLIDES_PER_VIEW_NO_LOOP;\n    let desktopSlidesPerView = SWIPER_CONFIG_CONSTANTS.FALLBACK_SLIDES_PER_VIEW;\n\n    if (enableLoop) {\n      tabletSlidesPerView = SWIPER_CONFIG_CONSTANTS.TABLET_SLIDES_PER_VIEW_WITH_LOOP;\n      desktopSlidesPerView = defaultSlidesPerView;\n    }\n\n    return {\n      mobile: {\n        slidesPerView: SWIPER_CONFIG_CONSTANTS.MOBILE_SLIDES_PER_VIEW,\n        spaceBetween: 10\n      },\n      tablet: {\n        slidesPerView: tabletSlidesPerView,\n        spaceBetween: 12\n      },\n      desktop: {\n        slidesPerView: desktopSlidesPerView,\n        spaceBetween: defaultConfig.slider.swiper.spaceBetween\n      }\n    };\n  }\n\n  /**\n   * Build final Swiper configuration object\n   */\n  private buildFinalConfig(\n    responsiveConfig: ResponsiveConfig,\n    transitionTime: number,\n    enableLoop: boolean\n  ): SwiperFullConfig {\n    const breakpoints: SwiperBreakpoints = {\n      320: responsiveConfig.mobile,\n      768: responsiveConfig.tablet,\n      1024: responsiveConfig.desktop\n    };\n\n    return {\n      loop: enableLoop,\n      autoplay: {\n        delay: transitionTime,\n        disableOnInteraction: false\n      },\n      spaceBetween: defaultConfig.slider.swiper.spaceBetween,\n      effect: defaultConfig.slider.swiper.effect,\n      centeredSlides: defaultConfig.slider.swiper.centeredSlides,\n      slidesPerView: responsiveConfig.desktop.slidesPerView,\n      breakpoints,\n      pagination: {\n        el: defaultConfig.slider.swiper.pagination.el,\n        type: defaultConfig.slider.swiper.pagination.type,\n        clickable: true\n      },\n      navigation: {\n        nextEl: defaultConfig.slider.swiper.navigation.nextEl,\n        prevEl: defaultConfig.slider.swiper.navigation.prevEl\n      },\n      initialSlide: 0,\n      modules: [Navigation, Pagination, Autoplay]\n    };\n  }\n}\n", "import { MOBILE_DETECTION } from '../../common/config/constants';\n\n/**\n * Mobile detection utility functions\n */\n\n/**\n * Check if the current device is mobile\n */\nexport const isMobileDevice = (): boolean => {\n    try {\n        const { userAgent } = navigator;\n        const mobileIndicator = userAgent.substring(\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_START,\n            MOBILE_DETECTION.USER_AGENT_SUBSTR_LENGTH\n        );\n        return mobileIndicator === 'Mobi';\n    } catch {\n        return false;\n    }\n};\n", "import * as DOMUtils from '../../utils/dom-utils';\nimport { isMobileDevice } from '../../utils/mobile-detection';\nimport { defaultConfig } from '../../../common/config';\nimport type { ISwiperDOMBuilder } from '../../../common/config/types';\n\n/**\n * Swiper DOM builder\n * Handles all DOM creation and manipulation for Swiper components\n */\nexport class SwiperDOM<PERSON>uilder implements ISwiperDOMBuilder {\n  private container: HTMLElement | undefined;\n\n  /**\n   * Create main container element\n   */\n  createContainer(): HTMLElement {\n    this.removeExistingNavigation();\n\n    const container = DOMUtils.createElement('div', {\n      id: defaultConfig.slider.dom.containerId,\n      className: 'adContainer adContainer--forced'\n    });\n\n    this.container = container;\n    return container;\n  }\n\n  /**\n   * Create Swiper element\n   */\n  createSwiperElement(container: HTMLElement): HTMLElement {\n    let className = `swiper ${defaultConfig.slider.dom.swiperClass} adSwiper--forced`;\n\n    // Add mobile-specific class if on mobile device\n    if (isMobileDevice()) {\n      className += ' adSwiper--mobile';\n    }\n\n    const swiper = DOMUtils.createElement('div', {\n      className\n    });\n\n    DOMUtils.appendChild(container, swiper);\n    return swiper;\n  }\n\n  /**\n   * Create Swiper wrapper\n   */\n  createSwiperWrapper(swiper: HTMLElement): HTMLElement {\n    const wrapper = DOMUtils.createElement('div', {\n      className: 'swiper-wrapper swiper-wrapper--forced'\n    });\n\n    DOMUtils.appendChild(swiper, wrapper);\n    return wrapper;\n  }\n\n  /**\n   * Create individual slide\n   */\n  createSlide(imageSrc: string, imageLink: string): HTMLElement {\n    const slide = DOMUtils.createElement('div', {\n      className: 'swiper-slide swiper-slide--forced'\n    });\n\n    let clickHandler = '';\n    if (imageLink) {\n      clickHandler = `window.location.href=\"${imageLink}\"`;\n    }\n\n    // Create image with CSS class instead of inline styles\n    slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' class='swiper-slide__image' />`;\n\n    return slide;\n  }\n\n  /**\n   * Create pagination element\n   */\n  createPagination(swiper: HTMLElement): void {\n    const pagination = DOMUtils.createElement('div', {\n      className: 'swiper-pagination'\n    });\n    DOMUtils.appendChild(swiper, pagination);\n  }\n\n  /**\n   * Create navigation elements\n   */\n  createNavigation(swiper: HTMLElement): void {\n    const prevButton = DOMUtils.createElement('div', {\n      className: 'swiper-button-prev swiper-navigation-button swiper-navigation-button--prev'\n    });\n    const nextButton = DOMUtils.createElement('div', {\n      className: 'swiper-button-next swiper-navigation-button swiper-navigation-button--next'\n    });\n\n    DOMUtils.appendChild(swiper, prevButton);\n    DOMUtils.appendChild(swiper, nextButton);\n  }\n\n  /**\n   * Append slideshow to DOM\n   */\n  appendToDOM(container: HTMLElement): void {\n    const contentContainer = DOMUtils.querySelector(\"#content .container\");\n    if (contentContainer) {\n      DOMUtils.prependChild(contentContainer, container);\n    }\n  }\n\n  /**\n   * Clean up DOM elements\n   */\n  cleanup(): void {\n    if (this.container) {\n      DOMUtils.removeElement(this.container);\n      delete this.container;\n    }\n  }\n\n  /**\n   * Get the current container element\n   */\n  getContainer(): HTMLElement | undefined {\n    return this.container;\n  }\n\n  /**\n   * Remove existing navigation elements\n   */\n  private removeExistingNavigation(): void {\n    const existingContainer = DOMUtils.querySelector(`#${defaultConfig.slider.dom.containerId}`);\n    if (existingContainer) {\n      DOMUtils.removeElement(existingContainer);\n    }\n\n    const navElements = DOMUtils.querySelectorAll(\".item-nav\");\n    for (const element of navElements) {\n      DOMUtils.removeElement(element);\n    }\n  }\n}\n", "import app from 'flarum/forum/app';\nimport { defaultConfig } from '../../../common/config';\nimport { SLIDESHOW_CONSTANTS, ARRAY_CONSTANTS } from '../../../common/config/constants';\nimport type {\n  ISlideDataManager,\n  ProcessedSlideData,\n  SlideDataRaw,\n  ValidationResult\n} from '../../../common/config/types';\n\n/**\n * Slide data manager\n * Handles fetching, processing, and validation of slide data from Flarum settings\n */\nexport class SlideDataManager implements ISlideDataManager {\n  private readonly maxSlides = defaultConfig.slider.maxSlides;\n\n  /**\n   * Fetch and process slide data from forum settings\n   */\n  fetchSlideData(): ProcessedSlideData[] {\n    const rawData: SlideDataRaw[] = [];\n\n    // Collect raw slide data from forum settings\n    for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= this.maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {\n      const imageSrc = this.getForumAttribute(`wusong8899-header-advertisement.Image${slideIndex}`);\n      const imageLink = this.getForumAttribute(`wusong8899-header-advertisement.Link${slideIndex}`);\n\n      if (imageSrc) {\n        rawData.push({\n          imageSrc: String(imageSrc),\n          imageLink: String(imageLink || ''),\n          slideIndex\n        });\n      }\n    }\n\n    // Process and validate the data\n    return this.processSlideData(rawData);\n  }\n\n  /**\n   * Validate slide data\n   */\n  validateSlideData(data: SlideDataRaw[]): ValidationResult {\n    const errors: string[] = [];\n\n    if (!Array.isArray(data)) {\n      errors.push('Slide data must be an array');\n      return { isValid: false, errors };\n    }\n\n    if (data.length === ARRAY_CONSTANTS.EMPTY_LENGTH) {\n      errors.push('No slide data provided');\n      return { isValid: false, errors };\n    }\n\n    for (const slide of data) {\n      if (!slide.imageSrc || typeof slide.imageSrc !== 'string') {\n        errors.push(`Invalid image source for slide ${slide.slideIndex}`);\n      }\n\n      if (slide.imageLink && typeof slide.imageLink !== 'string') {\n        errors.push(`Invalid image link for slide ${slide.slideIndex}`);\n      }\n\n      if (typeof slide.slideIndex !== 'number' || slide.slideIndex < SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX) {\n        errors.push(`Invalid slide index: ${slide.slideIndex}`);\n      }\n\n      // Basic URL validation for image source\n      if (slide.imageSrc && !this.isValidUrl(slide.imageSrc)) {\n        errors.push(`Invalid image URL format for slide ${slide.slideIndex}`);\n      }\n\n      // Basic URL validation for image link (if provided)\n      if (slide.imageLink && !this.isValidUrl(slide.imageLink)) {\n        errors.push(`Invalid link URL format for slide ${slide.slideIndex}`);\n      }\n    }\n\n    return {\n      isValid: errors.length === ARRAY_CONSTANTS.EMPTY_LENGTH,\n      errors\n    };\n  }\n\n  /**\n   * Get transition time from forum settings\n   */\n  getTransitionTime(): number {\n    const transitionTime = this.getForumAttribute('wusong8899-header-advertisement.TransitionTime');\n    if (transitionTime) {\n      const parsedTime = Number.parseInt(String(transitionTime), 10);\n      if (!Number.isNaN(parsedTime) && parsedTime > ARRAY_CONSTANTS.EMPTY_LENGTH) {\n        return parsedTime;\n      }\n    }\n    return defaultConfig.slider.defaultTransitionTime;\n  }\n\n  /**\n   * Process raw slide data into processed format\n   */\n  private processSlideData(rawData: SlideDataRaw[]): ProcessedSlideData[] {\n    return rawData.map((slide) => ({\n      imageSrc: slide.imageSrc,\n      imageLink: slide.imageLink,\n      slideIndex: slide.slideIndex,\n      isValid: this.isSlideValid(slide)\n    })).filter((slide) => slide.isValid);\n  }\n\n  /**\n   * Check if a single slide is valid\n   */\n  private isSlideValid(slide: SlideDataRaw): boolean {\n    return Boolean(\n      slide.imageSrc &&\n      typeof slide.imageSrc === 'string' &&\n      this.isValidUrl(slide.imageSrc) &&\n      typeof slide.slideIndex === 'number' &&\n      slide.slideIndex > ARRAY_CONSTANTS.EMPTY_LENGTH\n    );\n  }\n\n  /**\n   * Basic URL validation\n   */\n  private isValidUrl(url: string): boolean {\n    try {\n      const urlObject = new URL(url);\n      return Boolean(urlObject);\n    } catch {\n      // Check for relative URLs or data URLs\n      return url.startsWith('/') || url.startsWith('./') || url.startsWith('data:');\n    }\n  }\n\n  /**\n   * Safely read a forum attribute if available\n   */\n  private getForumAttribute(key: string): unknown {\n    try {\n      const forum = app && app.forum;\n      const attrFn = forum && forum.attribute;\n      if (typeof attrFn === 'function') {\n        return attrFn.call(forum, key);\n      }\n      return;\n    } catch {\n      return;\n    }\n  }\n}\n", "import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\n\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\n\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\n\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\n\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\n\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\n\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\n\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\n\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\n\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\n\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\n\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\n\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\n\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\n\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\n\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\n\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\n\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\n\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\n\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\n\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\n\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\n\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\n\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\n\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\n\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\n\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\n\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\n\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\n\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\n\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.getSlideIndexWhenGrid(swiper.clickedIndex);\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  const isGrid = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      swiper.slideToLoop(realIndex);\n    } else if (slideToIndex > (isGrid ? (swiper.slides.length - slidesPerView) / 2 - (swiper.params.grid.rows - 1) : swiper.slides.length - slidesPerView)) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\n\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\n\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const clearBlankSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideBlankClass}`);\n    slides.forEach(el => {\n      el.remove();\n    });\n    if (slides.length > 0) {\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    }\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (params.loopAddBlankSlides && (params.slidesPerGroup > 1 || gridEnabled)) {\n    clearBlankSlides();\n  }\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\n\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = centeredSlides ? Math.max(slidesPerGroup, Math.ceil(slidesPerView / 2)) : slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\n\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\n\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\n\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\n\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\n\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\n\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\n\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\n\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\n\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\n\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\n\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\n\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\n\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\n\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\n\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\n\nvar classes = {\n  addClasses,\n  removeClasses\n};\n\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\n\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\n\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  getSlideIndexWhenGrid(index) {\n    if (this.grid && this.params.grid && this.params.grid.rows > 1) {\n      if (this.params.grid.fill === 'column') {\n        index = Math.floor(index / this.params.grid.rows);\n      } else if (this.params.grid.fill === 'row') {\n        index = index % Math.ceil(this.slides.length / this.params.grid.rows);\n      }\n    }\n    return index;\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\n\nexport { Swiper as S, defaults as d };\n", "import Swiper from 'swiper';\nimport { defaultConfig } from '../../../common/config';\nimport { TIMING } from '../../../common/config/constants';\nimport type {\n  ISwiperLifecycleManager,\n  SwiperInstance,\n  SwiperFullConfig\n} from '../../../common/config/types';\n\n/**\n * Swiper lifecycle manager\n * Handles Swiper instance creation, destruction, and lifecycle management\n */\nexport class SwiperLifecycleManager implements ISwiperLifecycleManager {\n  private swiperInstance: SwiperInstance | undefined;\n  private isDestroyed = false;\n\n  /**\n   * Initialize Swiper instance with the provided configuration\n   */\n  initialize(config: SwiperFullConfig): SwiperInstance | undefined {\n    try {\n      // Destroy existing instance if any\n      this.destroy();\n\n      // Create new Swiper instance\n      this.swiperInstance = new Swiper(`.${defaultConfig.slider.dom.swiperClass}`, config) as SwiperInstance;\n      this.isDestroyed = false;\n\n      return this.swiperInstance;\n    } catch {\n      // Silently handle initialization errors\n      return;\n    }\n  }\n\n  /**\n   * Destroy the current Swiper instance\n   */\n  destroy(): void {\n    if (this.swiperInstance && !this.isDestroyed) {\n      try {\n        this.swiperInstance.destroy(true, true);\n      } catch {\n        // Silently handle destruction errors\n      } finally {\n        delete this.swiperInstance;\n        this.isDestroyed = true;\n      }\n    }\n  }\n\n  /**\n   * Check if Swiper instance is initialized and not destroyed\n   */\n  isInitialized(): boolean {\n    return this.swiperInstance !== null && !this.isDestroyed;\n  }\n\n  /**\n   * Get the current Swiper instance\n   */\n  getInstance(): SwiperInstance | undefined {\n    if (this.isInitialized()) {\n      return this.swiperInstance;\n    }\n    return;\n  }\n\n  /**\n   * Reinitialize Swiper with new configuration\n   */\n  reinitialize(config: SwiperFullConfig): SwiperInstance | null {\n    this.destroy();\n    return this.initialize(config);\n  }\n\n  /**\n   * Check if the Swiper container exists in the DOM\n   */\n  isContainerAvailable(): boolean {\n    try {\n      const container = document.querySelector(`.${defaultConfig.slider.dom.swiperClass}`);\n      return container !== null;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Initialize with delay to wait for DOM readiness\n   */\n  initializeWithDelay(config: SwiperFullConfig, delay = TIMING.CHECK_INTERVAL): void {\n    setTimeout(() => {\n      if (this.isContainerAvailable()) {\n        this.initialize(config);\n      }\n    }, delay);\n  }\n\n  /**\n   * Get Swiper instance status information\n   */\n  getStatus(): {\n    isInitialized: boolean;\n    isDestroyed: boolean;\n    hasInstance: boolean;\n    containerAvailable: boolean;\n  } {\n    return {\n      isInitialized: this.isInitialized(),\n      isDestroyed: this.isDestroyed,\n      hasInstance: this.swiperInstance !== null,\n      containerAvailable: this.isContainerAvailable()\n    };\n  }\n}\n", "import * as DOMUtils from '../utils/dom-utils';\nimport { defaultConfig } from '../../common/config';\nimport { ARRAY_CONSTANTS, SLIDESHOW_CONSTANTS } from '../../common/config/constants';\nimport { SwiperConfigManager } from './swiper/swiper-config-manager';\nimport { SwiperDOMBuilder } from './swiper/swiper-dom-builder';\nimport { SlideDataManager } from './swiper/slide-data-manager';\nimport { SwiperLifecycleManager } from './swiper/swiper-lifecycle-manager';\nimport type { FlarumVnode, ProcessedSlideData } from '../../common/config/types';\n\n/**\n * Slideshow manager for header advertisements\n * Coordinates between different modules to manage the complete slideshow lifecycle\n */\nexport class SlideshowManager {\n    private readonly configManager: SwiperConfigManager;\n    private readonly domBuilder: SwiperDOMBuilder;\n    private readonly dataManager: SlideDataManager;\n    private readonly lifecycleManager: SwiperLifecycleManager;\n    private readonly checkTime = defaultConfig.slider.checkTime;\n\n    constructor() {\n        this.configManager = new SwiperConfigManager();\n        this.domBuilder = new SwiperDOMBuilder();\n        this.dataManager = new SlideDataManager();\n        this.lifecycleManager = new SwiperLifecycleManager();\n    }\n\n    /**\n     * Initialize and attach slideshow to the DOM\n     */\n    attachAdvertiseHeader(_vdom: FlarumVnode): void {\n        try {\n            this.destroy(); // Clean up any existing instance\n\n            // Fetch slide data\n            const slideData = this.dataManager.fetchSlideData();\n            if (slideData.length === ARRAY_CONSTANTS.EMPTY_LENGTH) {\n                return; // No slides to display\n            }\n\n            // Build DOM structure\n            const container = this.domBuilder.createContainer();\n            const swiper = this.domBuilder.createSwiperElement(container);\n            const wrapper = this.domBuilder.createSwiperWrapper(swiper);\n\n            // Populate slides\n            const slideCount = this.populateSlides(wrapper, slideData);\n            this.domBuilder.createPagination(swiper);\n            this.domBuilder.createNavigation(swiper);\n\n            // Append to DOM\n            this.domBuilder.appendToDOM(container);\n\n            // Initialize Swiper after DOM attachment\n            setTimeout(() => {\n                this.initializeSwiper(slideCount);\n            }, this.checkTime);\n        } catch {\n            // Silently handle slideshow creation errors\n        }\n    }\n\n    /**\n     * Populate slides with processed slide data\n     */\n    private populateSlides(wrapper: HTMLElement, slideData: ProcessedSlideData[]): number {\n        let slideCount = ARRAY_CONSTANTS.EMPTY_LENGTH;\n\n        for (const slide of slideData) {\n            if (slide.isValid) {\n                const slideElement = this.domBuilder.createSlide(slide.imageSrc, slide.imageLink);\n                DOMUtils.appendChild(wrapper, slideElement);\n                slideCount += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT;\n            }\n        }\n\n        return slideCount;\n    }\n\n\n\n    /**\n     * Initialize Swiper with calculated configuration\n     */\n    private initializeSwiper(slideCount: number): void {\n        try {\n            const transitionTime = this.dataManager.getTransitionTime();\n            const configResult = this.configManager.calculateConfiguration(slideCount, transitionTime);\n\n            // Validate configuration\n            const validationResult = this.configManager.validateConfiguration(configResult.finalConfig);\n            if (!validationResult.isValid) {\n                return;\n            }\n\n            // Initialize Swiper instance\n            this.lifecycleManager.initialize(configResult.finalConfig);\n        } catch {\n            // Silently handle Swiper initialization errors\n        }\n    }\n\n    /**\n     * Destroy slideshow instance\n     */\n    destroy(): void {\n        this.lifecycleManager.destroy();\n        this.domBuilder.cleanup();\n    }\n\n}\n", "import { ERROR_HANDLING } from '../../common/config/constants';\nimport type { ErrorLogEntry } from '../../common/config/types';\n\n/**\n * Error handling utility for the Header Advertisement extension\n */\nexport class ErrorHandler {\n    private static instance: ErrorHandler;\n    private errorLog: ErrorLogEntry[] = [];\n    private isInitialized = false;\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ErrorHandler {\n        if (!ErrorHandler.instance) {\n            ErrorHandler.instance = new ErrorHandler();\n        }\n        return ErrorHandler.instance;\n    }\n\n    /**\n     * Initialize error handler\n     */\n    public initialize(): boolean {\n        try {\n            if (this.isInitialized) {\n                return true;\n            }\n\n            // Set up global error handling\n            this.setupGlobalErrorHandling();\n            this.isInitialized = true;\n            return true;\n        } catch {\n            return false;\n        }\n    }\n\n    /**\n     * Handle synchronous errors\n     */\n    public handleSync<TResult>(fn: () => TResult, context: string): TResult | false {\n        try {\n            return fn();\n        } catch (error) {\n            this.logError(error as Error, context);\n            return false;\n        }\n    }\n\n    /**\n     * Handle asynchronous errors\n     */\n    public handleAsync<TResult>(fn: () => Promise<TResult>, context: string): Promise<TResult | false> {\n        return fn().catch((error) => {\n            this.logError(error as Error, context);\n            return false;\n        });\n    }\n\n    /**\n     * Log error with context\n     */\n    private logError(error: Error, context: string): void {\n        try {\n            const entry: ErrorLogEntry = {\n                timestamp: new Date(),\n                error,\n                context,\n            };\n\n            this.errorLog.push(entry);\n\n            // Keep log size manageable\n            if (this.errorLog.length > ERROR_HANDLING.MAX_ERROR_LOG_ENTRIES) {\n                this.errorLog.shift();\n            }\n\n            // Log to console in development\n            if (process.env.NODE_ENV === 'development') {\n                // Development logging would go here\n                // console.warn(`[HeaderAdvertisement] Error in ${context}:`, error);\n            }\n        } catch {\n            // Silently handle logging errors\n        }\n    }\n\n    /**\n     * Set up global error handling\n     */\n    private setupGlobalErrorHandling(): void {\n        try {\n            // Handle unhandled promise rejections\n            globalThis.addEventListener('unhandledrejection', (event) => {\n                this.logError(\n                    new Error(String(event.reason)),\n                    'Unhandled Promise Rejection'\n                );\n            });\n        } catch {\n            // Silently handle setup errors\n        }\n    }\n\n    /**\n     * Get error log (for debugging)\n     */\n    public getErrorLog(): ErrorLogEntry[] {\n        return [...this.errorLog];\n    }\n\n    /**\n     * Clear error log\n     */\n    public clearErrorLog(): void {\n        this.errorLog = [];\n    }\n}\n", "import app from 'flarum/forum/app';\nimport { defaultConfig } from '../../common/config';\n\n/**\n * Configuration manager for the Header Advertisement extension\n */\nexport class ConfigManager {\n    private static instance: ConfigManager;\n\n    private constructor() {\n        // Private constructor for singleton pattern\n    }\n\n    /**\n     * Get singleton instance\n     */\n    public static getInstance(): ConfigManager {\n        if (!ConfigManager.instance) {\n            ConfigManager.instance = new ConfigManager();\n        }\n        return ConfigManager.instance;\n    }\n\n    /**\n     * Check if current page is tags page\n     */\n    public isTagsPage(): boolean {\n        try {\n            const currentRoute = app.current.get('routeName');\n            return currentRoute === 'tags';\n        } catch {\n            // Fallback: check URL\n            try {\n                return globalThis.location.pathname.includes('/tags');\n            } catch {\n                return false;\n            }\n        }\n    }\n\n    /**\n     * Get extension configuration\n     */\n    public getConfig(): typeof defaultConfig {\n        return defaultConfig;\n    }\n\n    /**\n     * Check if slideshow is properly configured\n     */\n    public isSlideshowConfigured(): boolean {\n        try {\n            const FIRST_SLIDE_INDEX = 1;\n            const SLIDE_INCREMENT = 1;\n            // Check if at least one slide is configured\n            for (let slideIndex = FIRST_SLIDE_INDEX; slideIndex <= defaultConfig.slider.maxSlides; slideIndex += SLIDE_INCREMENT) {\n                const image = app.forum.attribute(`wusong8899-header-advertisement.Image${slideIndex}`);\n                if (image) {\n                    return true;\n                }\n            }\n            return false;\n        } catch {\n            return false;\n        }\n    }\n}\n", "import { extend } from 'flarum/common/extend';\nimport app from 'flarum/forum/app';\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\n\nimport { SlideshowManager } from './components/slideshow-manager';\nimport { UIManager } from './components/ui-manager';\nimport { ErrorHandler } from './utils/error-handler';\nimport { ConfigManager } from './utils/config-manager';\nimport { isMobileDevice } from './utils/mobile-detection';\nimport { defaultConfig } from '../common/config';\n\n/**\n * Main extension initializer for Header Advertisement\n */\napp.initializers.add(defaultConfig.app.extensionId, () => {\n    const errorHandler = ErrorHandler.getInstance();\n    const configManager = ConfigManager.getInstance();\n\n    // Initialize error handling\n    if (!errorHandler.initialize()) {\n        return;\n    }\n\n    const slideshowManager = new SlideshowManager();\n    const uiManager = new UIManager();\n\n    extend(HeaderPrimary.prototype, 'view', function headerPrimaryViewExtension(vnode: unknown) {\n        errorHandler.handleSync(() => {\n            if (configManager.isTagsPage()) {\n                initializeExtension(vnode, slideshowManager, uiManager);\n            }\n        }, 'HeaderPrimary view extension');\n    });\n});\n\n/**\n * Initialize extension components\n */\nconst initializeExtension = (\n    vnode: unknown,\n    slideshowManager: SlideshowManager,\n    _uiManager: UIManager\n): void => {\n    try {\n        const configManager = ConfigManager.getInstance();\n\n        // Setup slideshow (only if configured)\n        if (configManager.isSlideshowConfigured()) {\n            try {\n                slideshowManager.attachAdvertiseHeader(vnode);\n            } catch {\n                // Slideshow setup failed, but continue with other features\n            }\n        }\n\n        // UI Manager is available for future use if needed\n\n        // Add header icon for non-logged users on mobile devices only\n        if (!app.session.user && isMobileDevice()) {\n            addHeaderIcon();\n        }\n\n    } catch {\n        // Silently handle initialization errors\n    }\n}\n\n\n\n/**\n * Add header icon for branding (mobile only)\n */\nconst addHeaderIcon = (): void => {\n    let headerIconContainer = document.getElementById(defaultConfig.ui.headerIconId);\n\n    if (headerIconContainer === null) {\n        // Get header icon URL from settings, fallback to default config\n        const headerIconUrl = app.forum.attribute('wusong8899-header-advertisement.HeaderIconUrl') || defaultConfig.ui.headerIconUrl;\n\n        headerIconContainer = document.createElement(\"div\");\n        headerIconContainer.id = defaultConfig.ui.headerIconId;\n        headerIconContainer.className = 'HeaderIcon-container mobile-only';\n        headerIconContainer.innerHTML = `<img src=\"${headerIconUrl}\" alt=\"Header Icon\" class=\"HeaderIcon-image\" />`;\n\n        // Find the navigation container (.App-backControl) and insert the icon as the last child\n        const navigationContainer = document.querySelector(\"#app-navigation .Navigation.ButtonGroup.App-backControl\");\n        if (navigationContainer) {\n            // Insert as the last child of the navigation container (to the right of the drawer button)\n            navigationContainer.appendChild(headerIconContainer);\n        }\n    }\n}\n"], "names": ["querySelector", "selector", "querySelectorAll", "createElement", "tagName", "options", "innerHTML", "element", "key", "value", "append<PERSON><PERSON><PERSON>", "parent", "child", "prepend<PERSON>hild", "removeElement", "MOBILE_DETECTION", "ERROR_HANDLING", "SLIDESHOW_CONSTANTS", "ARRAY_CONSTANTS", "TIMING", "DOM_ELEMENTS", "CSS_CLASSES", "CSS_SELECTORS", "EXTENSION_CONFIG", "defaultConfig", "isObject", "obj", "extend", "target", "src", "noExtend", "ssrDocument", "getDocument", "doc", "ssrWindow", "callback", "id", "getWindow", "win", "classesToTokens", "classes", "c", "deleteProps", "object", "nextTick", "delay", "now", "getComputedStyle", "el", "window", "style", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "o", "isNode", "node", "to", "i", "nextSource", "keysArray", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "setCSSProperty", "varName", "varValue", "animateCSSModeScroll", "_ref", "swiper", "targetPosition", "side", "startPosition", "startTime", "time", "duration", "dir", "isOutOfBound", "current", "animate", "progress", "easeProgress", "currentPosition", "elementChildren", "children", "elementIsChildOfSlot", "slot", "elementsQueue", "elementToCheck", "elementIsChildOf", "<PERSON><PERSON><PERSON><PERSON>", "showWarning", "text", "tag", "elementPrevAll", "prevEls", "prev", "elementNextAll", "nextEls", "next", "elementStyle", "prop", "elementIndex", "elementParents", "parents", "elementOuterSize", "size", "<PERSON><PERSON><PERSON><PERSON>", "makeElementsArray", "setInnerHTML", "html", "s", "createElementIfNotDefined", "originalParams", "params", "checkProps", "Navigation", "extendParams", "on", "emit", "getEl", "res", "toggleEl", "disabled", "subEl", "update", "nextEl", "prevEl", "onPrevClick", "e", "onNextClick", "init", "initButton", "destroy", "destroyButton", "disable", "_s", "targetEl", "targetIsButton", "path", "pathEl", "isHidden", "enable", "classesToSelector", "Pagination", "pfx", "number", "bulletSize", "dynamicBulletIndex", "isPaginationDisabled", "setSideBullets", "bulletEl", "position", "bulletActiveClass", "getMoveDirection", "prevIndex", "length", "onBulletClick", "index", "moveDirection", "rtl", "previousIndex", "<PERSON><PERSON><PERSON><PERSON>", "total", "bullets", "firstIndex", "lastIndex", "midIndex", "classesToRemove", "suffix", "bullet", "bulletIndex", "first<PERSON><PERSON>played<PERSON><PERSON>et", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamicBulletsLength", "bulletsOffset", "offsetProp", "subElIndex", "fractionEl", "totalEl", "progressbarDirection", "scale", "scaleX", "scaleY", "progressEl", "render", "paginationHTML", "numberOfBullets", "Autoplay", "timeout", "raf", "autoplayDelayTotal", "autoplayDelayCurrent", "autoplayTimeLeft", "autoplayStartTime", "wasPaused", "isTouched", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchStartTimeout", "slideChanged", "pausedByInteraction", "pausedByPointerEnter", "onTransitionEnd", "resume", "calcTimeLeft", "timeLeft", "getSlideDelay", "activeSlideEl", "slideEl", "run", "delayForce", "currentSlideDelay", "speed", "proceed", "start", "stop", "pause", "internal", "reset", "onVisibilityChange", "document", "onPointerEnter", "onPointerLeave", "attachMouseEvents", "detachMouseEvents", "attachDocumentEvents", "detachDocumentEvents", "SWIPER_CONFIG_CONSTANTS", "SwiperConfigManager", "slideCount", "transitionTime", "enableLoop", "responsiveConfig", "finalConfig", "config", "errors", "width", "breakpointConfig", "widthNum", "_slideCount", "defaultSlidesPerView", "tabletSlidesPerView", "desktopSlidesPerView", "breakpoints", "isMobileDevice", "userAgent", "SwiperDOMBuilder", "container", "DOMUtils.createElement", "className", "DOMUtils.appendChild", "wrapper", "imageSrc", "imageLink", "slide", "clickHandler", "pagination", "prevButton", "nextButton", "contentContainer", "DOMUtils.querySelector", "DOMUtils.prependChild", "DOMUtils.removeElement", "existingContainer", "navElements", "DOMUtils.querySelectorAll", "SlideDataManager", "rawData", "slideIndex", "data", "parsedTime", "url", "forum", "app", "attrFn", "support", "calcSupport", "getSupport", "deviceCached", "calcDevice", "_temp", "platform", "ua", "device", "screenWidth", "screenHeight", "android", "ipad", "ipod", "iphone", "windows", "macos", "iPadScreens", "getDevice", "overrides", "browser", "calcB<PERSON>er", "needPerspectiveFix", "<PERSON><PERSON><PERSON><PERSON>", "major", "minor", "num", "isWebView", "isSafariB<PERSON><PERSON>", "need3dFix", "<PERSON><PERSON><PERSON><PERSON>", "Resize", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "createObserver", "entries", "height", "newWidth", "newHeight", "_ref2", "contentBoxSize", "contentRect", "removeObserver", "orientationChangeHandler", "Observer", "observers", "attach", "ObserverFunc", "mutations", "observerUpdate", "containerParents", "eventsEmitter", "events", "handler", "priority", "self", "method", "event", "once<PERSON><PERSON><PERSON>", "_len", "args", "_key", "<PERSON><PERSON><PERSON><PERSON>", "context", "_len2", "_key2", "updateSize", "updateSlides", "getDirectionPropertyValue", "label", "wrapperEl", "slidesEl", "swiperSize", "wrongRTL", "isVirtual", "previousSlidesLength", "slides", "snapGrid", "slidesGrid", "slidesSizesGrid", "offsetBefore", "offsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "spaceBetween", "slidePosition", "prevSlideSize", "gridEnabled", "slideSize", "shouldResetSlideSize", "slideStyles", "currentTransform", "currentWebKitTransform", "paddingLeft", "paddingRight", "marginLeft", "marginRight", "boxSizing", "clientWidth", "offsetWidth", "newSlidesGrid", "slidesGridItem", "groups", "groupSize", "_", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "offsetSize", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "v", "backFaceHiddenClass", "hasClassBackfaceClassAdded", "updateAutoHeight", "activeSlides", "getSlideByIndex", "updateSlidesOffset", "minusOffset", "toggleSlideClasses$1", "condition", "updateSlidesProgress", "translate", "offsetCenter", "slideOffset", "slideProgress", "originalSlideProgress", "slideBefore", "slideAfter", "isFullyVisible", "isVisible", "updateProgress", "multiplier", "translatesDiff", "isBeginning", "isEnd", "progressLoop", "wasBeginning", "wasEnd", "isBeginningRounded", "isEndRounded", "firstSlideIndex", "lastSlideIndex", "firstSlideTranslate", "lastSlideTranslate", "translateMax", "translateAbs", "toggleSlideClasses", "updateSlidesClasses", "activeIndex", "getFilteredSlide", "activeSlide", "prevSlide", "nextSlide", "processLazyPreloader", "imageEl", "slideSelector", "lazyEl", "unlazy", "preload", "amount", "<PERSON><PERSON><PERSON><PERSON>iew", "activeColumn", "preloadColumns", "slideIndexLastInView", "realIndex", "getActiveIndexByTranslate", "updateActiveIndex", "newActiveIndex", "previousRealIndex", "previousSnapIndex", "getVirtualRealIndex", "aIndex", "skip", "firstSlideInColumn", "activeSlideIndex", "updateClickedSlide", "slideFound", "getSwiperTranslate", "currentTranslate", "setTranslate", "byController", "x", "y", "z", "newProgress", "minTranslate", "maxTranslate", "translateTo", "runCallbacks", "translateBounds", "newTranslate", "isH", "setTransition", "transitionEmit", "direction", "step", "transitionStart", "transitionEnd", "transition", "slideTo", "initial", "enabled", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "t", "slideToLoop", "newIndex", "targetSlideIndex", "cols", "centeredSlides", "needLoopFix", "slideNext", "animating", "perGroup", "increment", "slidePrev", "rtlTranslate", "normalize", "val", "normalizedSnapGrid", "isFreeMode", "prevSnap", "prevSnapIndex", "slideReset", "slideToClosest", "threshold", "currentSnap", "nextSnap", "slideToClickedSlide", "slideToIndex", "isGrid", "loopCreate", "slideRealIndex", "initSlides", "clearBlankSlides", "slidesPerGroup", "shouldFillGroup", "shouldFillGrid", "addBlankSlides", "amountOfSlides", "slidesToAdd", "loopFix", "byMousewheel", "allowSlidePrev", "allowSlideNext", "initialSlide", "loopedSlides", "prependSlidesIndexes", "appendSlidesIndexes", "isInitialOverflow", "isNext", "isPrev", "slidesPrepended", "slidesAppended", "activeColIndexWithShift", "colIndexToPrepend", "currentSlideTranslate", "diff", "shift", "loopParams", "loop<PERSON><PERSON><PERSON>", "newSlidesOrder", "loop", "setGrabCursor", "moving", "unsetGrabCursor", "grabCursor", "closestElement", "base", "__closestFrom", "found", "preventEdgeSwipe", "startX", "edgeSwipeDetection", "edgeSwipeThreshold", "onTouchStart", "touches", "swipingClassHasValue", "eventPath", "noSwipingSelector", "isTargetShadow", "startY", "preventDefault", "shouldPreventDefault", "onTouchMove", "targetTouch", "pageX", "pageY", "diffX", "diffY", "touchAngle", "touchesDiff", "prevTouchesDirection", "isLoop", "allowLoopFix", "evt", "disableParentSwiper", "resistanceRatio", "onTouchEnd", "touchEndTime", "timeDiff", "pathTree", "currentPos", "swipeToLast", "stopIndex", "rewindFirstIndex", "rewindLastIndex", "ratio", "onResize", "isVirtualLoop", "onClick", "onScroll", "onLoad", "onDocumentTouchStart", "capture", "dom<PERSON>ethod", "swiperMethod", "attachEvents", "detachEvents", "events$1", "isGridEnabled", "setBreakpoint", "initialized", "breakpointsBase", "breakpoint<PERSON><PERSON><PERSON>", "breakpoint", "breakpointP<PERSON>ms", "wasMultiRow", "isMultiRow", "wasGrabCursor", "isGrabCursor", "wasEnabled", "wasModuleEnabled", "isModuleEnabled", "directionChanged", "needsReLoop", "<PERSON><PERSON><PERSON>", "isEnabled", "<PERSON><PERSON><PERSON>", "getBreakpoint", "containerEl", "currentHeight", "points", "point", "minRatio", "b", "prepareClasses", "prefix", "resultClasses", "item", "classNames", "addClasses", "suffixes", "removeClasses", "checkOverflow", "wasLocked", "slidesOffsetBefore", "lastSlideRightEdge", "checkOverflow$1", "defaults", "moduleExtendParams", "allModulesParams", "moduleParamName", "moduleParams", "prototypes", "extendedDefaults", "Swiper", "swipers", "newParams", "mod", "swiperParams", "eventName", "property", "min", "cls", "updates", "view", "exact", "spv", "breakLoop", "translateValue", "translated", "newDirection", "needUpdate", "currentDirection", "getWrapperSelector", "lazyElements", "deleteInstance", "cleanStyles", "newDefaults", "modules", "module", "m", "prototypeGroup", "protoMethod", "SwiperLifecycleManager", "SlideshowManager", "_vdom", "slideData", "slideElement", "config<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fn", "error", "entry", "ConfigManager", "<PERSON><PERSON><PERSON><PERSON>", "config<PERSON><PERSON><PERSON>", "slideshowManager", "HeaderPrimary", "vnode", "initializeExtension", "_uiManager", "addHeaderIcon", "headerIconContainer", "headerIconUrl", "navigationContainer"], "mappings": "gCASO,MAAMA,GAAiBC,GAAsC,CAChE,GAAI,CACA,OAAO,SAAS,cAAcA,CAAQ,GAAK,EAC/C,MAAQ,CACJ,MAAO,EACX,CACJ,EAKaC,GAAoBD,GAA0C,CACvE,GAAI,CACA,OAAO,SAAS,iBAAiBA,CAAQ,CAC7C,MAAQ,CACJ,OAAO,SAAS,iBAAiB,EAAE,CACvC,CACJ,EAgBaE,EAAgB,CACzBC,EACAC,EAA6B,CAAA,EAC7BC,EAAY,KACE,CACd,GAAI,CACA,MAAMC,EAAU,SAAS,cAAcH,CAAO,EAG9C,SAAW,CAACI,EAAKC,CAAK,IAAK,OAAO,QAAQJ,CAAO,EACzCG,IAAQ,YACRD,EAAQ,UAAY,OAAOE,CAAK,EACzBD,IAAQ,KACfD,EAAQ,GAAK,OAAOE,CAAK,EAEzBF,EAAQ,aAAaC,EAAK,OAAOC,CAAK,CAAC,EAI/C,OAAIH,IACAC,EAAQ,UAAYD,GAGjBC,CACX,MAAQ,CACJ,OAAO,SAAS,cAAc,KAAK,CACvC,CACJ,EAKaG,EAAc,CAACC,EAAiBC,IAAyB,CAClE,GAAI,CACAD,EAAO,YAAYC,CAAK,CAC5B,MAAQ,CAER,CACJ,EAKaC,GAAe,CAACF,EAAiBC,IAAyB,CACnE,GAAI,CACAD,EAAO,QAAQC,CAAK,CACxB,MAAQ,CAER,CACJ,EAKaE,GAAiBP,GAA2B,CACrD,GAAI,CACAA,EAAQ,OAAA,CACZ,MAAQ,CAER,CACJ,ECjGaQ,GAAmB,CAC9B,wBAAyB,EACzB,yBAA0B,CAC5B,EAGaC,GAAiB,CAC5B,sBAAuB,GACvB,kBAAmB,IACnB,iBAAkB,EAClB,iBAAkB,GAClB,oBAAqB,IACrB,oBAAqB,IACrB,sBAAuB,EACvB,sBAAuB,EACzB,EAuBaC,EAAsB,CACjC,gBAAiB,EACjB,oBAAqB,CAEvB,EAGaC,EAAkB,CAC7B,aAAc,EACd,YAAa,EACb,gBAAiB,GACjB,iBAAkB,EAClB,iBAAkB,EACpB,EAGaC,GAAS,CACpB,eAAgB,GAChB,oBAAqB,IACrB,wBAAyB,GAC3B,EAGaC,GAAe,CAC1B,uBAAwB,oBACxB,eAAgB,yBAClB,EAGaC,GAAc,CAOzB,UAAW,UACb,EAGaC,GAAgB,CAI3B,qBAAsB,qBACtB,sBAAuB,sBACvB,sBAAuB,qBACzB,EAKaC,GAAmB,CAC9B,GAAI,kCACJ,mBAAoB,kCACpB,WAAY,GACZ,gBAAiB,0CACnB,EC3FaC,EAA4B,CACvC,IAAM,aACN,IAAK,CACH,YAAaD,GAAiB,GAC9B,kBAAmBA,GAAiB,kBAAA,EAEtC,OAAQ,CACN,UAAWA,GAAiB,WAC5B,sBAAuBJ,GAAO,wBAC9B,UAAWA,GAAO,eAClB,kBAAmBA,GAAO,oBAC1B,IAAK,CACH,YAAaC,GAAa,uBAC1B,YAAaC,GAAY,SAAA,EAE3B,OAAQ,CACN,aAAc,GACd,OAAQ,QACR,eAAgB,GAChB,cAAe,IACf,WAAY,CACV,GAAIC,GAAc,qBAClB,KAAM,SAAA,EAER,WAAY,CACV,OAAQA,GAAc,sBACtB,OAAQA,GAAc,qBAAA,CACxB,CACF,EAEF,GAAI,CACF,aAAcF,GAAa,eAC3B,cAAeG,GAAiB,eAAA,CAEpC,EC/BA,SAASE,GAASC,EAAK,CACrB,OAAOA,IAAQ,MAAQ,OAAOA,GAAQ,UAAY,gBAAiBA,GAAOA,EAAI,cAAgB,MAChG,CACA,SAASC,GAAOC,EAAQC,EAAK,CACvBD,IAAW,SACbA,EAAS,CAAA,GAEPC,IAAQ,SACVA,EAAM,CAAA,GAER,MAAMC,EAAW,CAAC,YAAa,cAAe,WAAW,EACzD,OAAO,KAAKD,CAAG,EAAE,OAAOrB,GAAOsB,EAAS,QAAQtB,CAAG,EAAI,CAAC,EAAE,QAAQA,GAAO,CACnE,OAAOoB,EAAOpB,CAAG,EAAM,IAAaoB,EAAOpB,CAAG,EAAIqB,EAAIrB,CAAG,EAAWiB,GAASI,EAAIrB,CAAG,CAAC,GAAKiB,GAASG,EAAOpB,CAAG,CAAC,GAAK,OAAO,KAAKqB,EAAIrB,CAAG,CAAC,EAAE,OAAS,GACpJmB,GAAOC,EAAOpB,CAAG,EAAGqB,EAAIrB,CAAG,CAAC,CAEhC,CAAC,CACH,CACA,MAAMuB,GAAc,CAClB,KAAM,CAAA,EACN,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,EACvB,cAAe,CACb,MAAO,CAAC,EACR,SAAU,EACd,EACE,eAAgB,CACd,OAAO,IACT,EACA,kBAAmB,CACjB,MAAO,CAAA,CACT,EACA,gBAAiB,CACf,OAAO,IACT,EACA,aAAc,CACZ,MAAO,CACL,WAAY,CAAC,CACnB,CACE,EACA,eAAgB,CACd,MAAO,CACL,SAAU,CAAA,EACV,WAAY,CAAA,EACZ,MAAO,CAAA,EACP,cAAe,CAAC,EAChB,sBAAuB,CACrB,MAAO,CAAA,CACT,CACN,CACE,EACA,iBAAkB,CAChB,MAAO,CAAA,CACT,EACA,YAAa,CACX,OAAO,IACT,EACA,SAAU,CACR,KAAM,GACN,KAAM,GACN,SAAU,GACV,KAAM,GACN,OAAQ,GACR,SAAU,GACV,SAAU,GACV,OAAQ,EACZ,CACA,EACA,SAASC,GAAc,CACrB,MAAMC,EAAM,OAAO,SAAa,IAAc,SAAW,CAAA,EACzDN,OAAAA,GAAOM,EAAKF,EAAW,EAChBE,CACT,CACA,MAAMC,GAAY,CAChB,SAAUH,GACV,UAAW,CACT,UAAW,EACf,EACE,SAAU,CACR,KAAM,GACN,KAAM,GACN,SAAU,GACV,KAAM,GACN,OAAQ,GACR,SAAU,GACV,SAAU,GACV,OAAQ,EACZ,EACE,QAAS,CACP,cAAe,CAAC,EAChB,WAAY,CAAC,EACb,IAAK,CAAC,EACN,MAAO,CAAC,CACZ,EACE,YAAa,UAAuB,CAClC,OAAO,IACT,EACA,kBAAmB,CAAC,EACpB,qBAAsB,CAAC,EACvB,kBAAmB,CACjB,MAAO,CACL,kBAAmB,CACjB,MAAO,EACT,CACN,CACE,EACA,OAAQ,CAAC,EACT,MAAO,CAAC,EACR,OAAQ,CAAA,EACR,YAAa,CAAC,EACd,cAAe,CAAC,EAChB,YAAa,CACX,MAAO,CAAA,CACT,EACA,sBAAsBI,EAAU,CAC9B,OAAI,OAAO,WAAe,KACxBA,EAAQ,EACD,MAEF,WAAWA,EAAU,CAAC,CAC/B,EACA,qBAAqBC,EAAI,CACnB,OAAO,WAAe,KAG1B,aAAaA,CAAE,CACjB,CACF,EACA,SAASC,GAAY,CACnB,MAAMC,EAAM,OAAO,OAAW,IAAc,OAAS,CAAA,EACrDX,OAAAA,GAAOW,EAAKJ,EAAS,EACdI,CACT,CC7IA,SAASC,GAAgBC,EAAS,CAChC,OAAIA,IAAY,SACdA,EAAU,IAELA,EAAQ,OAAO,MAAM,GAAG,EAAE,OAAOC,GAAK,CAAC,CAACA,EAAE,KAAI,CAAE,CACzD,CAEA,SAASC,GAAYhB,EAAK,CACxB,MAAMiB,EAASjB,EACf,OAAO,KAAKiB,CAAM,EAAE,QAAQnC,GAAO,CACjC,GAAI,CACFmC,EAAOnC,CAAG,EAAI,IAChB,MAAY,CAEZ,CACA,GAAI,CACF,OAAOmC,EAAOnC,CAAG,CACnB,MAAY,CAEZ,CACF,CAAC,CACH,CACA,SAASoC,GAAST,EAAUU,EAAO,CACjC,OAAIA,IAAU,SACZA,EAAQ,GAEH,WAAWV,EAAUU,CAAK,CACnC,CACA,SAASC,IAAM,CACb,OAAO,KAAK,IAAG,CACjB,CACA,SAASC,GAAiBC,EAAI,CAC5B,MAAMC,EAASZ,EAAS,EACxB,IAAIa,EACJ,OAAID,EAAO,mBACTC,EAAQD,EAAO,iBAAiBD,EAAI,IAAI,GAEtC,CAACE,GAASF,EAAG,eACfE,EAAQF,EAAG,cAERE,IACHA,EAAQF,EAAG,OAENE,CACT,CACA,SAASC,GAAaH,EAAII,EAAM,CAC1BA,IAAS,SACXA,EAAO,KAET,MAAMH,EAASZ,EAAS,EACxB,IAAIgB,EACAC,EACAC,EACJ,MAAMC,EAAWT,GAAiBC,CAAE,EACpC,OAAIC,EAAO,iBACTK,EAAeE,EAAS,WAAaA,EAAS,gBAC1CF,EAAa,MAAM,GAAG,EAAE,OAAS,IACnCA,EAAeA,EAAa,MAAM,IAAI,EAAE,IAAI,GAAK,EAAE,QAAQ,IAAK,GAAG,CAAC,EAAE,KAAK,IAAI,GAIjFC,EAAkB,IAAIN,EAAO,gBAAgBK,IAAiB,OAAS,GAAKA,CAAY,IAExFC,EAAkBC,EAAS,cAAgBA,EAAS,YAAcA,EAAS,aAAeA,EAAS,aAAeA,EAAS,WAAaA,EAAS,iBAAiB,WAAW,EAAE,QAAQ,aAAc,oBAAoB,EACzNH,EAASE,EAAgB,WAAW,MAAM,GAAG,GAE3CH,IAAS,MAEPH,EAAO,gBAAiBK,EAAeC,EAAgB,IAElDF,EAAO,SAAW,GAAIC,EAAe,WAAWD,EAAO,EAAE,CAAC,EAE9DC,EAAe,WAAWD,EAAO,CAAC,CAAC,GAEtCD,IAAS,MAEPH,EAAO,gBAAiBK,EAAeC,EAAgB,IAElDF,EAAO,SAAW,GAAIC,EAAe,WAAWD,EAAO,EAAE,CAAC,EAE9DC,EAAe,WAAWD,EAAO,CAAC,CAAC,GAEnCC,GAAgB,CACzB,CACA,SAAS7B,GAASgC,EAAG,CACnB,OAAO,OAAOA,GAAM,UAAYA,IAAM,MAAQA,EAAE,aAAe,OAAO,UAAU,SAAS,KAAKA,CAAC,EAAE,MAAM,EAAG,EAAE,IAAM,QACpH,CACA,SAASC,GAAOC,EAAM,CAEpB,OAAI,OAAO,OAAW,KAAe,OAAO,OAAO,YAAgB,IAC1DA,aAAgB,YAElBA,IAASA,EAAK,WAAa,GAAKA,EAAK,WAAa,GAC3D,CACA,SAAShC,GAAS,CAChB,MAAMiC,EAAK,OAAO,UAAU,QAAU,EAAI,OAAY,UAAU,CAAC,CAAC,EAC5D9B,EAAW,CAAC,YAAa,cAAe,WAAW,EACzD,QAAS+B,EAAI,EAAGA,EAAI,UAAU,OAAQA,GAAK,EAAG,CAC5C,MAAMC,EAAaD,EAAI,GAAK,UAAU,QAAUA,EAAI,OAAY,UAAUA,CAAC,EAC3E,GAAgCC,GAAe,MAAQ,CAACJ,GAAOI,CAAU,EAAG,CAC1E,MAAMC,EAAY,OAAO,KAAK,OAAOD,CAAU,CAAC,EAAE,OAAOtD,GAAOsB,EAAS,QAAQtB,CAAG,EAAI,CAAC,EACzF,QAASwD,EAAY,EAAGC,EAAMF,EAAU,OAAQC,EAAYC,EAAKD,GAAa,EAAG,CAC/E,MAAME,EAAUH,EAAUC,CAAS,EAC7BG,EAAO,OAAO,yBAAyBL,EAAYI,CAAO,EAC5DC,IAAS,QAAaA,EAAK,aACzB1C,GAASmC,EAAGM,CAAO,CAAC,GAAKzC,GAASqC,EAAWI,CAAO,CAAC,EACnDJ,EAAWI,CAAO,EAAE,WACtBN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAEhCvC,EAAOiC,EAAGM,CAAO,EAAGJ,EAAWI,CAAO,CAAC,EAEhC,CAACzC,GAASmC,EAAGM,CAAO,CAAC,GAAKzC,GAASqC,EAAWI,CAAO,CAAC,GAC/DN,EAAGM,CAAO,EAAI,CAAA,EACVJ,EAAWI,CAAO,EAAE,WACtBN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAEhCvC,EAAOiC,EAAGM,CAAO,EAAGJ,EAAWI,CAAO,CAAC,GAGzCN,EAAGM,CAAO,EAAIJ,EAAWI,CAAO,EAGtC,CACF,CACF,CACA,OAAON,CACT,CACA,SAASQ,GAAepB,EAAIqB,EAASC,EAAU,CAC7CtB,EAAG,MAAM,YAAYqB,EAASC,CAAQ,CACxC,CACA,SAASC,GAAqBC,EAAM,CAClC,GAAI,CACF,OAAAC,EACA,eAAAC,EACA,KAAAC,CACJ,EAAMH,EACJ,MAAMvB,EAASZ,EAAS,EAClBuC,EAAgB,CAACH,EAAO,UAC9B,IAAII,EAAY,KACZC,EACJ,MAAMC,EAAWN,EAAO,OAAO,MAC/BA,EAAO,UAAU,MAAM,eAAiB,OACxCxB,EAAO,qBAAqBwB,EAAO,cAAc,EACjD,MAAMO,EAAMN,EAAiBE,EAAgB,OAAS,OAChDK,EAAe,CAACC,EAAStD,IACtBoD,IAAQ,QAAUE,GAAWtD,GAAUoD,IAAQ,QAAUE,GAAWtD,EAEvEuD,EAAU,IAAM,CACpBL,EAAO,IAAI,KAAI,EAAG,QAAO,EACrBD,IAAc,OAChBA,EAAYC,GAEd,MAAMM,EAAW,KAAK,IAAI,KAAK,KAAKN,EAAOD,GAAaE,EAAU,CAAC,EAAG,CAAC,EACjEM,EAAe,GAAM,KAAK,IAAID,EAAW,KAAK,EAAE,EAAI,EAC1D,IAAIE,EAAkBV,EAAgBS,GAAgBX,EAAiBE,GAOvE,GANIK,EAAaK,EAAiBZ,CAAc,IAC9CY,EAAkBZ,GAEpBD,EAAO,UAAU,SAAS,CACxB,CAACE,CAAI,EAAGW,CACd,CAAK,EACGL,EAAaK,EAAiBZ,CAAc,EAAG,CACjDD,EAAO,UAAU,MAAM,SAAW,SAClCA,EAAO,UAAU,MAAM,eAAiB,GACxC,WAAW,IAAM,CACfA,EAAO,UAAU,MAAM,SAAW,GAClCA,EAAO,UAAU,SAAS,CACxB,CAACE,CAAI,EAAGW,CAClB,CAAS,CACH,CAAC,EACDrC,EAAO,qBAAqBwB,EAAO,cAAc,EACjD,MACF,CACAA,EAAO,eAAiBxB,EAAO,sBAAsBkC,CAAO,CAC9D,EACAA,EAAO,CACT,CAIA,SAASI,EAAgBhF,EAASN,EAAU,CACtCA,IAAa,SACfA,EAAW,IAEb,MAAMgD,EAASZ,EAAS,EAClBmD,EAAW,CAAC,GAAGjF,EAAQ,QAAQ,EAIrC,OAHI0C,EAAO,iBAAmB1C,aAAmB,iBAC/CiF,EAAS,KAAK,GAAGjF,EAAQ,iBAAgB,CAAE,EAExCN,EAGEuF,EAAS,OAAOxC,GAAMA,EAAG,QAAQ/C,CAAQ,CAAC,EAFxCuF,CAGX,CACA,SAASC,GAAqBzC,EAAI0C,EAAM,CAEtC,MAAMC,EAAgB,CAACD,CAAI,EAC3B,KAAOC,EAAc,OAAS,GAAG,CAC/B,MAAMC,EAAiBD,EAAc,MAAK,EAC1C,GAAI3C,IAAO4C,EACT,MAAO,GAETD,EAAc,KAAK,GAAGC,EAAe,SAAU,GAAIA,EAAe,WAAaA,EAAe,WAAW,SAAW,CAAA,EAAK,GAAIA,EAAe,iBAAmBA,EAAe,iBAAgB,EAAK,CAAA,CAAG,CACxM,CACF,CACA,SAASC,GAAiB7C,EAAIrC,EAAQ,CACpC,MAAMsC,EAASZ,EAAS,EACxB,IAAIyD,EAAUnF,EAAO,SAASqC,CAAE,EAChC,MAAI,CAAC8C,GAAW7C,EAAO,iBAAmBtC,aAAkB,kBAE1DmF,EADiB,CAAC,GAAGnF,EAAO,iBAAgB,CAAE,EAC3B,SAASqC,CAAE,EACzB8C,IACHA,EAAUL,GAAqBzC,EAAIrC,CAAM,IAGtCmF,CACT,CACA,SAASC,GAAYC,EAAM,CACzB,GAAI,CACF,QAAQ,KAAKA,CAAI,EACjB,MACF,MAAc,CAEd,CACF,CACA,SAAS7F,GAAc8F,EAAKzD,EAAS,CAC/BA,IAAY,SACdA,EAAU,CAAA,GAEZ,MAAMQ,EAAK,SAAS,cAAciD,CAAG,EACrC,OAAAjD,EAAG,UAAU,IAAI,GAAI,MAAM,QAAQR,CAAO,EAAIA,EAAUD,GAAgBC,CAAO,CAAE,EAC1EQ,CACT,CAeA,SAASkD,GAAelD,EAAI/C,EAAU,CACpC,MAAMkG,EAAU,CAAA,EAChB,KAAOnD,EAAG,wBAAwB,CAChC,MAAMoD,EAAOpD,EAAG,uBACZ/C,EACEmG,EAAK,QAAQnG,CAAQ,GAAGkG,EAAQ,KAAKC,CAAI,EACxCD,EAAQ,KAAKC,CAAI,EACxBpD,EAAKoD,CACP,CACA,OAAOD,CACT,CACA,SAASE,GAAerD,EAAI/C,EAAU,CACpC,MAAMqG,EAAU,CAAA,EAChB,KAAOtD,EAAG,oBAAoB,CAC5B,MAAMuD,EAAOvD,EAAG,mBACZ/C,EACEsG,EAAK,QAAQtG,CAAQ,GAAGqG,EAAQ,KAAKC,CAAI,EACxCD,EAAQ,KAAKC,CAAI,EACxBvD,EAAKuD,CACP,CACA,OAAOD,CACT,CACA,SAASE,EAAaxD,EAAIyD,EAAM,CAE9B,OADepE,EAAS,EACV,iBAAiBW,EAAI,IAAI,EAAE,iBAAiByD,CAAI,CAChE,CACA,SAASC,GAAa1D,EAAI,CACxB,IAAIpC,EAAQoC,EACRa,EACJ,GAAIjD,EAAO,CAGT,IAFAiD,EAAI,GAEIjD,EAAQA,EAAM,mBAAqB,MACrCA,EAAM,WAAa,IAAGiD,GAAK,GAEjC,OAAOA,CACT,CAEF,CACA,SAAS8C,GAAe3D,EAAI/C,EAAU,CACpC,MAAM2G,EAAU,CAAA,EAChB,IAAIjG,EAASqC,EAAG,cAChB,KAAOrC,GACDV,EACEU,EAAO,QAAQV,CAAQ,GAAG2G,EAAQ,KAAKjG,CAAM,EAEjDiG,EAAQ,KAAKjG,CAAM,EAErBA,EAASA,EAAO,cAElB,OAAOiG,CACT,CAWA,SAASC,GAAiB7D,EAAI8D,EAAMC,EAAgB,CAClD,MAAM9D,EAASZ,EAAS,EAEtB,OAAOW,EAAG8D,IAAS,QAAU,cAAgB,cAAc,EAAI,WAAW7D,EAAO,iBAAiBD,EAAI,IAAI,EAAE,iBAAiB8D,IAAS,QAAU,eAAiB,YAAY,CAAC,EAAI,WAAW7D,EAAO,iBAAiBD,EAAI,IAAI,EAAE,iBAAiB8D,IAAS,QAAU,cAAgB,eAAe,CAAC,CAGvS,CACA,SAASE,EAAkBhE,EAAI,CAC7B,OAAQ,MAAM,QAAQA,CAAE,EAAIA,EAAK,CAACA,CAAE,GAAG,OAAO,GAAK,CAAC,CAAC,CAAC,CACxD,CASA,SAASiE,GAAajE,EAAIkE,EAAM,CAC1BA,IAAS,SACXA,EAAO,IAEL,OAAO,aAAiB,IAC1BlE,EAAG,UAAY,aAAa,aAAa,OAAQ,CAC/C,WAAYmE,GAAKA,CACvB,CAAK,EAAE,WAAWD,CAAI,EAElBlE,EAAG,UAAYkE,CAEnB,CClVA,SAASE,GAA0B3C,EAAQ4C,EAAgBC,EAAQC,EAAY,CAC7E,OAAI9C,EAAO,OAAO,gBAChB,OAAO,KAAK8C,CAAU,EAAE,QAAQ/G,GAAO,CACrC,GAAI,CAAC8G,EAAO9G,CAAG,GAAK8G,EAAO,OAAS,GAAM,CACxC,IAAI/G,EAAUgF,EAAgBd,EAAO,GAAI,IAAI8C,EAAW/G,CAAG,CAAC,EAAE,EAAE,CAAC,EAC5DD,IACHA,EAAUJ,GAAc,MAAOoH,EAAW/G,CAAG,CAAC,EAC9CD,EAAQ,UAAYgH,EAAW/G,CAAG,EAClCiE,EAAO,GAAG,OAAOlE,CAAO,GAE1B+G,EAAO9G,CAAG,EAAID,EACd8G,EAAe7G,CAAG,EAAID,CACxB,CACF,CAAC,EAEI+G,CACT,CCfA,SAASE,GAAWhD,EAAM,CACxB,GAAI,CACF,OAAAC,EACA,aAAAgD,EACA,GAAAC,EACA,KAAAC,CACJ,EAAMnD,EACJiD,EAAa,CACX,WAAY,CACV,OAAQ,KACR,OAAQ,KACR,YAAa,GACb,cAAe,yBACf,YAAa,uBACb,UAAW,qBACX,wBAAyB,4BAC/B,CACA,CAAG,EACDhD,EAAO,WAAa,CAClB,OAAQ,KACR,OAAQ,IACZ,EACE,SAASmD,EAAM5E,EAAI,CACjB,IAAI6E,EACJ,OAAI7E,GAAM,OAAOA,GAAO,UAAYyB,EAAO,YACzCoD,EAAMpD,EAAO,GAAG,cAAczB,CAAE,GAAKyB,EAAO,OAAO,cAAczB,CAAE,EAC/D6E,GAAYA,GAEd7E,IACE,OAAOA,GAAO,WAAU6E,EAAM,CAAC,GAAG,SAAS,iBAAiB7E,CAAE,CAAC,GAC/DyB,EAAO,OAAO,mBAAqB,OAAOzB,GAAO,UAAY6E,GAAOA,EAAI,OAAS,GAAKpD,EAAO,GAAG,iBAAiBzB,CAAE,EAAE,SAAW,EAClI6E,EAAMpD,EAAO,GAAG,cAAczB,CAAE,EACvB6E,GAAOA,EAAI,SAAW,IAC/BA,EAAMA,EAAI,CAAC,IAGX7E,GAAM,CAAC6E,EAAY7E,EAEhB6E,EACT,CACA,SAASC,EAAS9E,EAAI+E,EAAU,CAC9B,MAAMT,EAAS7C,EAAO,OAAO,WAC7BzB,EAAKgE,EAAkBhE,CAAE,EACzBA,EAAG,QAAQgF,GAAS,CACdA,IACFA,EAAM,UAAUD,EAAW,MAAQ,QAAQ,EAAE,GAAGT,EAAO,cAAc,MAAM,GAAG,CAAC,EAC3EU,EAAM,UAAY,WAAUA,EAAM,SAAWD,GAC7CtD,EAAO,OAAO,eAAiBA,EAAO,SACxCuD,EAAM,UAAUvD,EAAO,SAAW,MAAQ,QAAQ,EAAE6C,EAAO,SAAS,EAG1E,CAAC,CACH,CACA,SAASW,GAAS,CAEhB,KAAM,CACJ,OAAAC,EACA,OAAAC,CACN,EAAQ1D,EAAO,WACX,GAAIA,EAAO,OAAO,KAAM,CACtBqD,EAASK,EAAQ,EAAK,EACtBL,EAASI,EAAQ,EAAK,EACtB,MACF,CACAJ,EAASK,EAAQ1D,EAAO,aAAe,CAACA,EAAO,OAAO,MAAM,EAC5DqD,EAASI,EAAQzD,EAAO,OAAS,CAACA,EAAO,OAAO,MAAM,CACxD,CACA,SAAS2D,EAAYC,EAAG,CACtBA,EAAE,eAAc,EACZ,EAAA5D,EAAO,aAAe,CAACA,EAAO,OAAO,MAAQ,CAACA,EAAO,OAAO,UAChEA,EAAO,UAAS,EAChBkD,EAAK,gBAAgB,EACvB,CACA,SAASW,EAAYD,EAAG,CACtBA,EAAE,eAAc,EACZ,EAAA5D,EAAO,OAAS,CAACA,EAAO,OAAO,MAAQ,CAACA,EAAO,OAAO,UAC1DA,EAAO,UAAS,EAChBkD,EAAK,gBAAgB,EACvB,CACA,SAASY,GAAO,CACd,MAAMjB,EAAS7C,EAAO,OAAO,WAK7B,GAJAA,EAAO,OAAO,WAAa2C,GAA0B3C,EAAQA,EAAO,eAAe,WAAYA,EAAO,OAAO,WAAY,CACvH,OAAQ,qBACR,OAAQ,oBACd,CAAK,EACG,EAAE6C,EAAO,QAAUA,EAAO,QAAS,OACvC,IAAIY,EAASN,EAAMN,EAAO,MAAM,EAC5Ba,EAASP,EAAMN,EAAO,MAAM,EAChC,OAAO,OAAO7C,EAAO,WAAY,CAC/B,OAAAyD,EACA,OAAAC,CACN,CAAK,EACDD,EAASlB,EAAkBkB,CAAM,EACjCC,EAASnB,EAAkBmB,CAAM,EACjC,MAAMK,EAAa,CAACxF,EAAIgC,IAAQ,CAC1BhC,GACFA,EAAG,iBAAiB,QAASgC,IAAQ,OAASsD,EAAcF,CAAW,EAErE,CAAC3D,EAAO,SAAWzB,GACrBA,EAAG,UAAU,IAAI,GAAGsE,EAAO,UAAU,MAAM,GAAG,CAAC,CAEnD,EACAY,EAAO,QAAQlF,GAAMwF,EAAWxF,EAAI,MAAM,CAAC,EAC3CmF,EAAO,QAAQnF,GAAMwF,EAAWxF,EAAI,MAAM,CAAC,CAC7C,CACA,SAASyF,GAAU,CACjB,GAAI,CACF,OAAAP,EACA,OAAAC,CACN,EAAQ1D,EAAO,WACXyD,EAASlB,EAAkBkB,CAAM,EACjCC,EAASnB,EAAkBmB,CAAM,EACjC,MAAMO,EAAgB,CAAC1F,EAAIgC,IAAQ,CACjChC,EAAG,oBAAoB,QAASgC,IAAQ,OAASsD,EAAcF,CAAW,EAC1EpF,EAAG,UAAU,OAAO,GAAGyB,EAAO,OAAO,WAAW,cAAc,MAAM,GAAG,CAAC,CAC1E,EACAyD,EAAO,QAAQlF,GAAM0F,EAAc1F,EAAI,MAAM,CAAC,EAC9CmF,EAAO,QAAQnF,GAAM0F,EAAc1F,EAAI,MAAM,CAAC,CAChD,CACA0E,EAAG,OAAQ,IAAM,CACXjD,EAAO,OAAO,WAAW,UAAY,GAEvCkE,EAAO,GAEPJ,EAAI,EACJN,EAAM,EAEV,CAAC,EACDP,EAAG,8BAA+B,IAAM,CACtCO,EAAM,CACR,CAAC,EACDP,EAAG,UAAW,IAAM,CAClBe,EAAO,CACT,CAAC,EACDf,EAAG,iBAAkB,IAAM,CACzB,GAAI,CACF,OAAAQ,EACA,OAAAC,CACN,EAAQ1D,EAAO,WAGX,GAFAyD,EAASlB,EAAkBkB,CAAM,EACjCC,EAASnB,EAAkBmB,CAAM,EAC7B1D,EAAO,QAAS,CAClBwD,EAAM,EACN,MACF,CACA,CAAC,GAAGC,EAAQ,GAAGC,CAAM,EAAE,OAAOnF,GAAM,CAAC,CAACA,CAAE,EAAE,QAAQA,GAAMA,EAAG,UAAU,IAAIyB,EAAO,OAAO,WAAW,SAAS,CAAC,CAC9G,CAAC,EACDiD,EAAG,QAAS,CAACkB,EAAIP,IAAM,CACrB,GAAI,CACF,OAAAH,EACA,OAAAC,CACN,EAAQ1D,EAAO,WACXyD,EAASlB,EAAkBkB,CAAM,EACjCC,EAASnB,EAAkBmB,CAAM,EACjC,MAAMU,EAAWR,EAAE,OACnB,IAAIS,EAAiBX,EAAO,SAASU,CAAQ,GAAKX,EAAO,SAASW,CAAQ,EAC1E,GAAIpE,EAAO,WAAa,CAACqE,EAAgB,CACvC,MAAMC,EAAOV,EAAE,MAAQA,EAAE,cAAgBA,EAAE,aAAY,EACnDU,IACFD,EAAiBC,EAAK,KAAKC,GAAUd,EAAO,SAASc,CAAM,GAAKb,EAAO,SAASa,CAAM,CAAC,EAE3F,CACA,GAAIvE,EAAO,OAAO,WAAW,aAAe,CAACqE,EAAgB,CAC3D,GAAIrE,EAAO,YAAcA,EAAO,OAAO,YAAcA,EAAO,OAAO,WAAW,YAAcA,EAAO,WAAW,KAAOoE,GAAYpE,EAAO,WAAW,GAAG,SAASoE,CAAQ,GAAI,OAC3K,IAAII,EACAf,EAAO,OACTe,EAAWf,EAAO,CAAC,EAAE,UAAU,SAASzD,EAAO,OAAO,WAAW,WAAW,EACnE0D,EAAO,SAChBc,EAAWd,EAAO,CAAC,EAAE,UAAU,SAAS1D,EAAO,OAAO,WAAW,WAAW,GAG5EkD,EADEsB,IAAa,GACV,iBAEA,gBAFgB,EAIvB,CAAC,GAAGf,EAAQ,GAAGC,CAAM,EAAE,OAAOnF,GAAM,CAAC,CAACA,CAAE,EAAE,QAAQA,GAAMA,EAAG,UAAU,OAAOyB,EAAO,OAAO,WAAW,WAAW,CAAC,CACnH,CACF,CAAC,EACD,MAAMyE,EAAS,IAAM,CACnBzE,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC,EACzF8D,EAAI,EACJN,EAAM,CACR,EACMU,EAAU,IAAM,CACpBlE,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,WAAW,wBAAwB,MAAM,GAAG,CAAC,EACtFgE,EAAO,CACT,EACA,OAAO,OAAOhE,EAAO,WAAY,CAC/B,OAAAyE,EACA,QAAAP,EACA,OAAAV,EACA,KAAAM,EACA,QAAAE,CACJ,CAAG,CACH,CCrMA,SAASU,EAAkB3G,EAAS,CAClC,OAAIA,IAAY,SACdA,EAAU,IAEL,IAAIA,EAAQ,KAAI,EAAG,QAAQ,oBAAqB,MAAM,EAC5D,QAAQ,KAAM,GAAG,CAAC,EACrB,CCFA,SAAS4G,GAAW5E,EAAM,CACxB,GAAI,CACF,OAAAC,EACA,aAAAgD,EACA,GAAAC,EACA,KAAAC,CACJ,EAAMnD,EACJ,MAAM6E,EAAM,oBACZ5B,EAAa,CACX,WAAY,CACV,GAAI,KACJ,cAAe,OACf,UAAW,GACX,YAAa,GACb,aAAc,KACd,kBAAmB,KACnB,eAAgB,KAChB,aAAc,KACd,oBAAqB,GACrB,KAAM,UAEN,eAAgB,GAChB,mBAAoB,EACpB,sBAAuB6B,GAAUA,EACjC,oBAAqBA,GAAUA,EAC/B,YAAa,GAAGD,CAAG,UACnB,kBAAmB,GAAGA,CAAG,iBACzB,cAAe,GAAGA,CAAG,IACrB,aAAc,GAAGA,CAAG,WACpB,WAAY,GAAGA,CAAG,SAClB,YAAa,GAAGA,CAAG,UACnB,qBAAsB,GAAGA,CAAG,oBAC5B,yBAA0B,GAAGA,CAAG,wBAChC,eAAgB,GAAGA,CAAG,aACtB,UAAW,GAAGA,CAAG,QACjB,gBAAiB,GAAGA,CAAG,cACvB,cAAe,GAAGA,CAAG,YACrB,wBAAyB,GAAGA,CAAG,WACrC,CACA,CAAG,EACD5E,EAAO,WAAa,CAClB,GAAI,KACJ,QAAS,CAAA,CACb,EACE,IAAI8E,EACAC,EAAqB,EACzB,SAASC,GAAuB,CAC9B,MAAO,CAAChF,EAAO,OAAO,WAAW,IAAM,CAACA,EAAO,WAAW,IAAM,MAAM,QAAQA,EAAO,WAAW,EAAE,GAAKA,EAAO,WAAW,GAAG,SAAW,CACzI,CACA,SAASiF,EAAeC,EAAUC,EAAU,CAC1C,KAAM,CACJ,kBAAAC,CACN,EAAQpF,EAAO,OAAO,WACbkF,IACLA,EAAWA,EAAS,GAAGC,IAAa,OAAS,WAAa,MAAM,gBAAgB,EAC5ED,IACFA,EAAS,UAAU,IAAI,GAAGE,CAAiB,IAAID,CAAQ,EAAE,EACzDD,EAAWA,EAAS,GAAGC,IAAa,OAAS,WAAa,MAAM,gBAAgB,EAC5ED,GACFA,EAAS,UAAU,IAAI,GAAGE,CAAiB,IAAID,CAAQ,IAAIA,CAAQ,EAAE,GAG3E,CACA,SAASE,EAAiBC,EAAW/F,EAAWgG,EAAQ,CAGtD,GAFAD,EAAYA,EAAYC,EACxBhG,EAAYA,EAAYgG,EACpBhG,IAAc+F,EAAY,EAC5B,MAAO,OACF,GAAI/F,IAAc+F,EAAY,EACnC,MAAO,UAGX,CACA,SAASE,EAAc5B,EAAG,CACxB,MAAMsB,EAAWtB,EAAE,OAAO,QAAQc,EAAkB1E,EAAO,OAAO,WAAW,WAAW,CAAC,EACzF,GAAI,CAACkF,EACH,OAEFtB,EAAE,eAAc,EAChB,MAAM6B,EAAQxD,GAAaiD,CAAQ,EAAIlF,EAAO,OAAO,eACrD,GAAIA,EAAO,OAAO,KAAM,CACtB,GAAIA,EAAO,YAAcyF,EAAO,OAChC,MAAMC,EAAgBL,EAAiBrF,EAAO,UAAWyF,EAAOzF,EAAO,OAAO,MAAM,EAChF0F,IAAkB,OACpB1F,EAAO,UAAS,EACP0F,IAAkB,WAC3B1F,EAAO,UAAS,EAEhBA,EAAO,YAAYyF,CAAK,CAE5B,MACEzF,EAAO,QAAQyF,CAAK,CAExB,CACA,SAASjC,GAAS,CAEhB,MAAMmC,EAAM3F,EAAO,IACb6C,EAAS7C,EAAO,OAAO,WAC7B,GAAIgF,EAAoB,EAAI,OAC5B,IAAIzG,EAAKyB,EAAO,WAAW,GAC3BzB,EAAKgE,EAAkBhE,CAAE,EAEzB,IAAIkC,EACAmF,EACJ,MAAMC,EAAe7F,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAUA,EAAO,QAAQ,OAAO,OAASA,EAAO,OAAO,OAC9G8F,EAAQ9F,EAAO,OAAO,KAAO,KAAK,KAAK6F,EAAe7F,EAAO,OAAO,cAAc,EAAIA,EAAO,SAAS,OAY5G,GAXIA,EAAO,OAAO,MAChB4F,EAAgB5F,EAAO,mBAAqB,EAC5CS,EAAUT,EAAO,OAAO,eAAiB,EAAI,KAAK,MAAMA,EAAO,UAAYA,EAAO,OAAO,cAAc,EAAIA,EAAO,WACzG,OAAOA,EAAO,UAAc,KACrCS,EAAUT,EAAO,UACjB4F,EAAgB5F,EAAO,oBAEvB4F,EAAgB5F,EAAO,eAAiB,EACxCS,EAAUT,EAAO,aAAe,GAG9B6C,EAAO,OAAS,WAAa7C,EAAO,WAAW,SAAWA,EAAO,WAAW,QAAQ,OAAS,EAAG,CAClG,MAAM+F,EAAU/F,EAAO,WAAW,QAClC,IAAIgG,EACAC,EACAC,EAsBJ,GArBIrD,EAAO,iBACTiC,EAAa1C,GAAiB2D,EAAQ,CAAC,EAAG/F,EAAO,aAAY,EAAK,QAAU,QAAc,EAC1FzB,EAAG,QAAQgF,GAAS,CAClBA,EAAM,MAAMvD,EAAO,aAAY,EAAK,QAAU,QAAQ,EAAI,GAAG8E,GAAcjC,EAAO,mBAAqB,EAAE,IAC3G,CAAC,EACGA,EAAO,mBAAqB,GAAK+C,IAAkB,SACrDb,GAAsBtE,GAAWmF,GAAiB,GAC9Cb,EAAqBlC,EAAO,mBAAqB,EACnDkC,EAAqBlC,EAAO,mBAAqB,EACxCkC,EAAqB,IAC9BA,EAAqB,IAGzBiB,EAAa,KAAK,IAAIvF,EAAUsE,EAAoB,CAAC,EACrDkB,EAAYD,GAAc,KAAK,IAAID,EAAQ,OAAQlD,EAAO,kBAAkB,EAAI,GAChFqD,GAAYD,EAAYD,GAAc,GAExCD,EAAQ,QAAQb,GAAY,CAC1B,MAAMiB,EAAkB,CAAC,GAAG,CAAC,GAAI,QAAS,aAAc,QAAS,aAAc,OAAO,EAAE,IAAIC,GAAU,GAAGvD,EAAO,iBAAiB,GAAGuD,CAAM,EAAE,CAAC,EAAE,IAAI1D,GAAK,OAAOA,GAAM,UAAYA,EAAE,SAAS,GAAG,EAAIA,EAAE,MAAM,GAAG,EAAIA,CAAC,EAAE,KAAI,EACzNwC,EAAS,UAAU,OAAO,GAAGiB,CAAe,CAC9C,CAAC,EACG5H,EAAG,OAAS,EACdwH,EAAQ,QAAQM,GAAU,CACxB,MAAMC,EAAcrE,GAAaoE,CAAM,EACnCC,IAAgB7F,EAClB4F,EAAO,UAAU,IAAI,GAAGxD,EAAO,kBAAkB,MAAM,GAAG,CAAC,EAClD7C,EAAO,WAChBqG,EAAO,aAAa,OAAQ,QAAQ,EAElCxD,EAAO,iBACLyD,GAAeN,GAAcM,GAAeL,GAC9CI,EAAO,UAAU,IAAI,GAAG,GAAGxD,EAAO,iBAAiB,QAAQ,MAAM,GAAG,CAAC,EAEnEyD,IAAgBN,GAClBf,EAAeoB,EAAQ,MAAM,EAE3BC,IAAgBL,GAClBhB,EAAeoB,EAAQ,MAAM,EAGnC,CAAC,MACI,CACL,MAAMA,EAASN,EAAQtF,CAAO,EAS9B,GARI4F,GACFA,EAAO,UAAU,IAAI,GAAGxD,EAAO,kBAAkB,MAAM,GAAG,CAAC,EAEzD7C,EAAO,WACT+F,EAAQ,QAAQ,CAACb,EAAUoB,IAAgB,CACzCpB,EAAS,aAAa,OAAQoB,IAAgB7F,EAAU,gBAAkB,QAAQ,CACpF,CAAC,EAECoC,EAAO,eAAgB,CACzB,MAAM0D,EAAuBR,EAAQC,CAAU,EACzCQ,EAAsBT,EAAQE,CAAS,EAC7C,QAAS7G,EAAI4G,EAAY5G,GAAK6G,EAAW7G,GAAK,EACxC2G,EAAQ3G,CAAC,GACX2G,EAAQ3G,CAAC,EAAE,UAAU,IAAI,GAAG,GAAGyD,EAAO,iBAAiB,QAAQ,MAAM,GAAG,CAAC,EAG7EoC,EAAesB,EAAsB,MAAM,EAC3CtB,EAAeuB,EAAqB,MAAM,CAC5C,CACF,CACA,GAAI3D,EAAO,eAAgB,CACzB,MAAM4D,EAAuB,KAAK,IAAIV,EAAQ,OAAQlD,EAAO,mBAAqB,CAAC,EAC7E6D,GAAiB5B,EAAa2B,EAAuB3B,GAAc,EAAIoB,EAAWpB,EAClF6B,EAAahB,EAAM,QAAU,OACnCI,EAAQ,QAAQM,GAAU,CACxBA,EAAO,MAAMrG,EAAO,aAAY,EAAK2G,EAAa,KAAK,EAAI,GAAGD,CAAa,IAC7E,CAAC,CACH,CACF,CACAnI,EAAG,QAAQ,CAACgF,EAAOqD,IAAe,CAShC,GARI/D,EAAO,OAAS,aAClBU,EAAM,iBAAiBmB,EAAkB7B,EAAO,YAAY,CAAC,EAAE,QAAQgE,GAAc,CACnFA,EAAW,YAAchE,EAAO,sBAAsBpC,EAAU,CAAC,CACnE,CAAC,EACD8C,EAAM,iBAAiBmB,EAAkB7B,EAAO,UAAU,CAAC,EAAE,QAAQiE,GAAW,CAC9EA,EAAQ,YAAcjE,EAAO,oBAAoBiD,CAAK,CACxD,CAAC,GAECjD,EAAO,OAAS,cAAe,CACjC,IAAIkE,EACAlE,EAAO,oBACTkE,EAAuB/G,EAAO,aAAY,EAAK,WAAa,aAE5D+G,EAAuB/G,EAAO,aAAY,EAAK,aAAe,WAEhE,MAAMgH,GAASvG,EAAU,GAAKqF,EAC9B,IAAImB,EAAS,EACTC,EAAS,EACTH,IAAyB,aAC3BE,EAASD,EAETE,EAASF,EAEXzD,EAAM,iBAAiBmB,EAAkB7B,EAAO,oBAAoB,CAAC,EAAE,QAAQsE,GAAc,CAC3FA,EAAW,MAAM,UAAY,6BAA6BF,CAAM,YAAYC,CAAM,IAClFC,EAAW,MAAM,mBAAqB,GAAGnH,EAAO,OAAO,KAAK,IAC9D,CAAC,CACH,CACI6C,EAAO,OAAS,UAAYA,EAAO,cACrCL,GAAae,EAAOV,EAAO,aAAa7C,EAAQS,EAAU,EAAGqF,CAAK,CAAC,EAC/Dc,IAAe,GAAG1D,EAAK,mBAAoBK,CAAK,IAEhDqD,IAAe,GAAG1D,EAAK,mBAAoBK,CAAK,EACpDL,EAAK,mBAAoBK,CAAK,GAE5BvD,EAAO,OAAO,eAAiBA,EAAO,SACxCuD,EAAM,UAAUvD,EAAO,SAAW,MAAQ,QAAQ,EAAE6C,EAAO,SAAS,CAExE,CAAC,CACH,CACA,SAASuE,GAAS,CAEhB,MAAMvE,EAAS7C,EAAO,OAAO,WAC7B,GAAIgF,EAAoB,EAAI,OAC5B,MAAMa,EAAe7F,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAUA,EAAO,QAAQ,OAAO,OAASA,EAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAAIA,EAAO,OAAO,OAAS,KAAK,KAAKA,EAAO,OAAO,KAAK,IAAI,EAAIA,EAAO,OAAO,OAC7N,IAAIzB,EAAKyB,EAAO,WAAW,GAC3BzB,EAAKgE,EAAkBhE,CAAE,EACzB,IAAI8I,EAAiB,GACrB,GAAIxE,EAAO,OAAS,UAAW,CAC7B,IAAIyE,EAAkBtH,EAAO,OAAO,KAAO,KAAK,KAAK6F,EAAe7F,EAAO,OAAO,cAAc,EAAIA,EAAO,SAAS,OAChHA,EAAO,OAAO,UAAYA,EAAO,OAAO,SAAS,SAAWsH,EAAkBzB,IAChFyB,EAAkBzB,GAEpB,QAASzG,EAAI,EAAGA,EAAIkI,EAAiBlI,GAAK,EACpCyD,EAAO,aACTwE,GAAkBxE,EAAO,aAAa,KAAK7C,EAAQZ,EAAGyD,EAAO,WAAW,EAGxEwE,GAAkB,IAAIxE,EAAO,aAAa,IAAI7C,EAAO,UAAY,gBAAkB,EAAE,WAAW6C,EAAO,WAAW,OAAOA,EAAO,aAAa,GAGnJ,CACIA,EAAO,OAAS,aACdA,EAAO,eACTwE,EAAiBxE,EAAO,eAAe,KAAK7C,EAAQ6C,EAAO,aAAcA,EAAO,UAAU,EAE1FwE,EAAiB,gBAAgBxE,EAAO,YAAY,4BAAsCA,EAAO,UAAU,aAG3GA,EAAO,OAAS,gBACdA,EAAO,kBACTwE,EAAiBxE,EAAO,kBAAkB,KAAK7C,EAAQ6C,EAAO,oBAAoB,EAElFwE,EAAiB,gBAAgBxE,EAAO,oBAAoB,aAGhE7C,EAAO,WAAW,QAAU,CAAA,EAC5BzB,EAAG,QAAQgF,GAAS,CACdV,EAAO,OAAS,UAClBL,GAAae,EAAO8D,GAAkB,EAAE,EAEtCxE,EAAO,OAAS,WAClB7C,EAAO,WAAW,QAAQ,KAAK,GAAGuD,EAAM,iBAAiBmB,EAAkB7B,EAAO,WAAW,CAAC,CAAC,CAEnG,CAAC,EACGA,EAAO,OAAS,UAClBK,EAAK,mBAAoB3E,EAAG,CAAC,CAAC,CAElC,CACA,SAASuF,GAAO,CACd9D,EAAO,OAAO,WAAa2C,GAA0B3C,EAAQA,EAAO,eAAe,WAAYA,EAAO,OAAO,WAAY,CACvH,GAAI,mBACV,CAAK,EACD,MAAM6C,EAAS7C,EAAO,OAAO,WAC7B,GAAI,CAAC6C,EAAO,GAAI,OAChB,IAAItE,EACA,OAAOsE,EAAO,IAAO,UAAY7C,EAAO,YAC1CzB,EAAKyB,EAAO,GAAG,cAAc6C,EAAO,EAAE,GAEpC,CAACtE,GAAM,OAAOsE,EAAO,IAAO,WAC9BtE,EAAK,CAAC,GAAG,SAAS,iBAAiBsE,EAAO,EAAE,CAAC,GAE1CtE,IACHA,EAAKsE,EAAO,IAEV,GAACtE,GAAMA,EAAG,SAAW,KACrByB,EAAO,OAAO,mBAAqB,OAAO6C,EAAO,IAAO,UAAY,MAAM,QAAQtE,CAAE,GAAKA,EAAG,OAAS,IACvGA,EAAK,CAAC,GAAGyB,EAAO,GAAG,iBAAiB6C,EAAO,EAAE,CAAC,EAE1CtE,EAAG,OAAS,IACdA,EAAKA,EAAG,KAAKgF,GACPrB,GAAeqB,EAAO,SAAS,EAAE,CAAC,IAAMvD,EAAO,EAEpD,IAGD,MAAM,QAAQzB,CAAE,GAAKA,EAAG,SAAW,IAAGA,EAAKA,EAAG,CAAC,GACnD,OAAO,OAAOyB,EAAO,WAAY,CAC/B,GAAAzB,CACN,CAAK,EACDA,EAAKgE,EAAkBhE,CAAE,EACzBA,EAAG,QAAQgF,GAAS,CACdV,EAAO,OAAS,WAAaA,EAAO,WACtCU,EAAM,UAAU,IAAI,IAAIV,EAAO,gBAAkB,IAAI,MAAM,GAAG,CAAC,EAEjEU,EAAM,UAAU,IAAIV,EAAO,cAAgBA,EAAO,IAAI,EACtDU,EAAM,UAAU,IAAIvD,EAAO,aAAY,EAAK6C,EAAO,gBAAkBA,EAAO,aAAa,EACrFA,EAAO,OAAS,WAAaA,EAAO,iBACtCU,EAAM,UAAU,IAAI,GAAGV,EAAO,aAAa,GAAGA,EAAO,IAAI,UAAU,EACnEkC,EAAqB,EACjBlC,EAAO,mBAAqB,IAC9BA,EAAO,mBAAqB,IAG5BA,EAAO,OAAS,eAAiBA,EAAO,qBAC1CU,EAAM,UAAU,IAAIV,EAAO,wBAAwB,EAEjDA,EAAO,WACTU,EAAM,iBAAiB,QAASiC,CAAa,EAE1CxF,EAAO,SACVuD,EAAM,UAAU,IAAIV,EAAO,SAAS,CAExC,CAAC,EACH,CACA,SAASmB,GAAU,CACjB,MAAMnB,EAAS7C,EAAO,OAAO,WAC7B,GAAIgF,EAAoB,EAAI,OAC5B,IAAIzG,EAAKyB,EAAO,WAAW,GACvBzB,IACFA,EAAKgE,EAAkBhE,CAAE,EACzBA,EAAG,QAAQgF,GAAS,CAClBA,EAAM,UAAU,OAAOV,EAAO,WAAW,EACzCU,EAAM,UAAU,OAAOV,EAAO,cAAgBA,EAAO,IAAI,EACzDU,EAAM,UAAU,OAAOvD,EAAO,aAAY,EAAK6C,EAAO,gBAAkBA,EAAO,aAAa,EACxFA,EAAO,YACTU,EAAM,UAAU,OAAO,IAAIV,EAAO,gBAAkB,IAAI,MAAM,GAAG,CAAC,EAClEU,EAAM,oBAAoB,QAASiC,CAAa,EAEpD,CAAC,GAECxF,EAAO,WAAW,SAASA,EAAO,WAAW,QAAQ,QAAQuD,GAASA,EAAM,UAAU,OAAO,GAAGV,EAAO,kBAAkB,MAAM,GAAG,CAAC,CAAC,CAC1I,CACAI,EAAG,kBAAmB,IAAM,CAC1B,GAAI,CAACjD,EAAO,YAAc,CAACA,EAAO,WAAW,GAAI,OACjD,MAAM6C,EAAS7C,EAAO,OAAO,WAC7B,GAAI,CACF,GAAAzB,CACN,EAAQyB,EAAO,WACXzB,EAAKgE,EAAkBhE,CAAE,EACzBA,EAAG,QAAQgF,GAAS,CAClBA,EAAM,UAAU,OAAOV,EAAO,gBAAiBA,EAAO,aAAa,EACnEU,EAAM,UAAU,IAAIvD,EAAO,aAAY,EAAK6C,EAAO,gBAAkBA,EAAO,aAAa,CAC3F,CAAC,CACH,CAAC,EACDI,EAAG,OAAQ,IAAM,CACXjD,EAAO,OAAO,WAAW,UAAY,GAEvCkE,EAAO,GAEPJ,EAAI,EACJsD,EAAM,EACN5D,EAAM,EAEV,CAAC,EACDP,EAAG,oBAAqB,IAAM,CACxB,OAAOjD,EAAO,UAAc,KAC9BwD,EAAM,CAEV,CAAC,EACDP,EAAG,kBAAmB,IAAM,CAC1BO,EAAM,CACR,CAAC,EACDP,EAAG,uBAAwB,IAAM,CAC/BmE,EAAM,EACN5D,EAAM,CACR,CAAC,EACDP,EAAG,UAAW,IAAM,CAClBe,EAAO,CACT,CAAC,EACDf,EAAG,iBAAkB,IAAM,CACzB,GAAI,CACF,GAAA1E,CACN,EAAQyB,EAAO,WACPzB,IACFA,EAAKgE,EAAkBhE,CAAE,EACzBA,EAAG,QAAQgF,GAASA,EAAM,UAAUvD,EAAO,QAAU,SAAW,KAAK,EAAEA,EAAO,OAAO,WAAW,SAAS,CAAC,EAE9G,CAAC,EACDiD,EAAG,cAAe,IAAM,CACtBO,EAAM,CACR,CAAC,EACDP,EAAG,QAAS,CAACkB,EAAIP,IAAM,CACrB,MAAMQ,EAAWR,EAAE,OACbrF,EAAKgE,EAAkBvC,EAAO,WAAW,EAAE,EACjD,GAAIA,EAAO,OAAO,WAAW,IAAMA,EAAO,OAAO,WAAW,aAAezB,GAAMA,EAAG,OAAS,GAAK,CAAC6F,EAAS,UAAU,SAASpE,EAAO,OAAO,WAAW,WAAW,EAAG,CACpK,GAAIA,EAAO,aAAeA,EAAO,WAAW,QAAUoE,IAAapE,EAAO,WAAW,QAAUA,EAAO,WAAW,QAAUoE,IAAapE,EAAO,WAAW,QAAS,OACnK,MAAMwE,EAAWjG,EAAG,CAAC,EAAE,UAAU,SAASyB,EAAO,OAAO,WAAW,WAAW,EAE5EkD,EADEsB,IAAa,GACV,iBAEA,gBAFgB,EAIvBjG,EAAG,QAAQgF,GAASA,EAAM,UAAU,OAAOvD,EAAO,OAAO,WAAW,WAAW,CAAC,CAClF,CACF,CAAC,EACD,MAAMyE,EAAS,IAAM,CACnBzE,EAAO,GAAG,UAAU,OAAOA,EAAO,OAAO,WAAW,uBAAuB,EAC3E,GAAI,CACF,GAAAzB,CACN,EAAQyB,EAAO,WACPzB,IACFA,EAAKgE,EAAkBhE,CAAE,EACzBA,EAAG,QAAQgF,GAASA,EAAM,UAAU,OAAOvD,EAAO,OAAO,WAAW,uBAAuB,CAAC,GAE9F8D,EAAI,EACJsD,EAAM,EACN5D,EAAM,CACR,EACMU,EAAU,IAAM,CACpBlE,EAAO,GAAG,UAAU,IAAIA,EAAO,OAAO,WAAW,uBAAuB,EACxE,GAAI,CACF,GAAAzB,CACN,EAAQyB,EAAO,WACPzB,IACFA,EAAKgE,EAAkBhE,CAAE,EACzBA,EAAG,QAAQgF,GAASA,EAAM,UAAU,IAAIvD,EAAO,OAAO,WAAW,uBAAuB,CAAC,GAE3FgE,EAAO,CACT,EACA,OAAO,OAAOhE,EAAO,WAAY,CAC/B,OAAAyE,EACA,QAAAP,EACA,OAAAkD,EACA,OAAA5D,EACA,KAAAM,EACA,QAAAE,CACJ,CAAG,CACH,CCrcA,SAASuD,GAASxH,EAAM,CACtB,GAAI,CACF,OAAAC,EACA,aAAAgD,EACA,GAAAC,EACA,KAAAC,EACA,OAAAL,CACJ,EAAM9C,EACJC,EAAO,SAAW,CAChB,QAAS,GACT,OAAQ,GACR,SAAU,CACd,EACEgD,EAAa,CACX,SAAU,CACR,QAAS,GACT,MAAO,IACP,kBAAmB,GACnB,qBAAsB,GACtB,gBAAiB,GACjB,iBAAkB,GAClB,kBAAmB,EACzB,CACA,CAAG,EACD,IAAIwE,EACAC,EACAC,EAAqB7E,GAAUA,EAAO,SAAWA,EAAO,SAAS,MAAQ,IACzE8E,EAAuB9E,GAAUA,EAAO,SAAWA,EAAO,SAAS,MAAQ,IAC3E+E,EACAC,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtCC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACJ,SAASC,EAAgBzE,EAAG,CACtB,CAAC5D,GAAUA,EAAO,WAAa,CAACA,EAAO,WACvC4D,EAAE,SAAW5D,EAAO,YACxBA,EAAO,UAAU,oBAAoB,gBAAiBqI,CAAe,EACjE,EAAAD,GAAwBxE,EAAE,QAAUA,EAAE,OAAO,oBAGjD0E,EAAM,EACR,CACA,MAAMC,EAAe,IAAM,CACzB,GAAIvI,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAC9CA,EAAO,SAAS,OAClB8H,EAAY,GACHA,IACTH,EAAuBC,EACvBE,EAAY,IAEd,MAAMU,EAAWxI,EAAO,SAAS,OAAS4H,EAAmBC,EAAoBF,EAAuB,IAAI,KAAI,EAAG,QAAO,EAC1H3H,EAAO,SAAS,SAAWwI,EAC3BtF,EAAK,mBAAoBsF,EAAUA,EAAWd,CAAkB,EAChED,EAAM,sBAAsB,IAAM,CAChCc,EAAY,CACd,CAAC,CACH,EACME,EAAgB,IAAM,CAC1B,IAAIC,EAMJ,OALI1I,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1C0I,EAAgB1I,EAAO,OAAO,KAAK2I,GAAWA,EAAQ,UAAU,SAAS,qBAAqB,CAAC,EAE/FD,EAAgB1I,EAAO,OAAOA,EAAO,WAAW,EAE7C0I,EACqB,SAASA,EAAc,aAAa,sBAAsB,EAAG,EAAE,EADrE,MAGtB,EACME,EAAMC,GAAc,CACxB,GAAI7I,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,qBAAqByH,CAAG,EACxBc,EAAY,EACZ,IAAInK,EAAQ,OAAOyK,EAAe,IAAc7I,EAAO,OAAO,SAAS,MAAQ6I,EAC/EnB,EAAqB1H,EAAO,OAAO,SAAS,MAC5C2H,EAAuB3H,EAAO,OAAO,SAAS,MAC9C,MAAM8I,EAAoBL,EAAa,EACnC,CAAC,OAAO,MAAMK,CAAiB,GAAKA,EAAoB,GAAK,OAAOD,EAAe,MACrFzK,EAAQ0K,EACRpB,EAAqBoB,EACrBnB,EAAuBmB,GAEzBlB,EAAmBxJ,EACnB,MAAM2K,EAAQ/I,EAAO,OAAO,MACtBgJ,GAAU,IAAM,CAChB,CAAChJ,GAAUA,EAAO,YAClBA,EAAO,OAAO,SAAS,iBACrB,CAACA,EAAO,aAAeA,EAAO,OAAO,MAAQA,EAAO,OAAO,QAC7DA,EAAO,UAAU+I,EAAO,GAAM,EAAI,EAClC7F,EAAK,UAAU,GACLlD,EAAO,OAAO,SAAS,kBACjCA,EAAO,QAAQA,EAAO,OAAO,OAAS,EAAG+I,EAAO,GAAM,EAAI,EAC1D7F,EAAK,UAAU,GAGb,CAAClD,EAAO,OAASA,EAAO,OAAO,MAAQA,EAAO,OAAO,QACvDA,EAAO,UAAU+I,EAAO,GAAM,EAAI,EAClC7F,EAAK,UAAU,GACLlD,EAAO,OAAO,SAAS,kBACjCA,EAAO,QAAQ,EAAG+I,EAAO,GAAM,EAAI,EACnC7F,EAAK,UAAU,GAGflD,EAAO,OAAO,UAChB6H,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtC,sBAAsB,IAAM,CAC1Be,EAAG,CACL,CAAC,GAEL,EACA,OAAIxK,EAAQ,GACV,aAAaoJ,CAAO,EACpBA,EAAU,WAAW,IAAM,CACzBwB,GAAO,CACT,EAAG5K,CAAK,GAER,sBAAsB,IAAM,CAC1B4K,GAAO,CACT,CAAC,EAII5K,CACT,EACM6K,EAAQ,IAAM,CAClBpB,EAAoB,IAAI,KAAI,EAAG,QAAO,EACtC7H,EAAO,SAAS,QAAU,GAC1B4I,EAAG,EACH1F,EAAK,eAAe,CACtB,EACMgG,EAAO,IAAM,CACjBlJ,EAAO,SAAS,QAAU,GAC1B,aAAawH,CAAO,EACpB,qBAAqBC,CAAG,EACxBvE,EAAK,cAAc,CACrB,EACMiG,EAAQ,CAACC,EAAUC,IAAU,CACjC,GAAIrJ,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,aAAawH,CAAO,EACf4B,IACHjB,EAAsB,IAExB,MAAMa,EAAU,IAAM,CACpB9F,EAAK,eAAe,EAChBlD,EAAO,OAAO,SAAS,kBACzBA,EAAO,UAAU,iBAAiB,gBAAiBqI,CAAe,EAElEC,EAAM,CAEV,EAEA,GADAtI,EAAO,SAAS,OAAS,GACrBqJ,EAAO,CACLnB,IACFN,EAAmB5H,EAAO,OAAO,SAAS,OAE5CkI,EAAe,GACfc,EAAO,EACP,MACF,CAEApB,GADcA,GAAoB5H,EAAO,OAAO,SAAS,QAC7B,IAAI,KAAI,EAAG,QAAO,EAAK6H,GAC/C,EAAA7H,EAAO,OAAS4H,EAAmB,GAAK,CAAC5H,EAAO,OAAO,QACvD4H,EAAmB,IAAGA,EAAmB,GAC7CoB,EAAO,EACT,EACMV,EAAS,IAAM,CACftI,EAAO,OAAS4H,EAAmB,GAAK,CAAC5H,EAAO,OAAO,MAAQA,EAAO,WAAa,CAACA,EAAO,SAAS,UACxG6H,EAAoB,IAAI,KAAI,EAAG,QAAO,EAClCM,GACFA,EAAsB,GACtBS,EAAIhB,CAAgB,GAEpBgB,EAAG,EAEL5I,EAAO,SAAS,OAAS,GACzBkD,EAAK,gBAAgB,EACvB,EACMoG,EAAqB,IAAM,CAC/B,GAAItJ,EAAO,WAAa,CAACA,EAAO,SAAS,QAAS,OAClD,MAAMuJ,EAAWhM,EAAW,EACxBgM,EAAS,kBAAoB,WAC/BpB,EAAsB,GACtBgB,EAAM,EAAI,GAERI,EAAS,kBAAoB,WAC/BjB,EAAM,CAEV,EACMkB,EAAiB5F,GAAK,CACtBA,EAAE,cAAgB,UACtBuE,EAAsB,GACtBC,EAAuB,GACnB,EAAApI,EAAO,WAAaA,EAAO,SAAS,SACxCmJ,EAAM,EAAI,EACZ,EACMM,EAAiB7F,GAAK,CACtBA,EAAE,cAAgB,UACtBwE,EAAuB,GACnBpI,EAAO,SAAS,QAClBsI,EAAM,EAEV,EACMoB,EAAoB,IAAM,CAC1B1J,EAAO,OAAO,SAAS,oBACzBA,EAAO,GAAG,iBAAiB,eAAgBwJ,CAAc,EACzDxJ,EAAO,GAAG,iBAAiB,eAAgByJ,CAAc,EAE7D,EACME,EAAoB,IAAM,CAC1B3J,EAAO,IAAM,OAAOA,EAAO,IAAO,WACpCA,EAAO,GAAG,oBAAoB,eAAgBwJ,CAAc,EAC5DxJ,EAAO,GAAG,oBAAoB,eAAgByJ,CAAc,EAEhE,EACMG,EAAuB,IAAM,CAChBrM,EAAW,EACnB,iBAAiB,mBAAoB+L,CAAkB,CAClE,EACMO,EAAuB,IAAM,CAChBtM,EAAW,EACnB,oBAAoB,mBAAoB+L,CAAkB,CACrE,EACArG,EAAG,OAAQ,IAAM,CACXjD,EAAO,OAAO,SAAS,UACzB0J,EAAiB,EACjBE,EAAoB,EACpBX,EAAK,EAET,CAAC,EACDhG,EAAG,UAAW,IAAM,CAClB0G,EAAiB,EACjBE,EAAoB,EAChB7J,EAAO,SAAS,SAClBkJ,EAAI,CAER,CAAC,EACDjG,EAAG,yBAA0B,IAAM,EAC7B+E,GAAiBG,IACnBG,EAAM,CAEV,CAAC,EACDrF,EAAG,6BAA8B,IAAM,CAChCjD,EAAO,OAAO,SAAS,qBAG1BkJ,EAAI,EAFJC,EAAM,GAAM,EAAI,CAIpB,CAAC,EACDlG,EAAG,wBAAyB,CAACkB,EAAI4E,EAAOK,IAAa,CAC/CpJ,EAAO,WAAa,CAACA,EAAO,SAAS,UACrCoJ,GAAY,CAACpJ,EAAO,OAAO,SAAS,qBACtCmJ,EAAM,GAAM,EAAI,EAEhBD,EAAI,EAER,CAAC,EACDjG,EAAG,kBAAmB,IAAM,CAC1B,GAAI,EAAAjD,EAAO,WAAa,CAACA,EAAO,SAAS,SACzC,IAAIA,EAAO,OAAO,SAAS,qBAAsB,CAC/CkJ,EAAI,EACJ,MACF,CACAnB,EAAY,GACZC,EAAgB,GAChBG,EAAsB,GACtBF,EAAoB,WAAW,IAAM,CACnCE,EAAsB,GACtBH,EAAgB,GAChBmB,EAAM,EAAI,CACZ,EAAG,GAAG,EACR,CAAC,EACDlG,EAAG,WAAY,IAAM,CACnB,GAAI,EAAAjD,EAAO,WAAa,CAACA,EAAO,SAAS,SAAW,CAAC+H,GAGrD,IAFA,aAAaE,CAAiB,EAC9B,aAAaT,CAAO,EAChBxH,EAAO,OAAO,SAAS,qBAAsB,CAC/CgI,EAAgB,GAChBD,EAAY,GACZ,MACF,CACIC,GAAiBhI,EAAO,OAAO,SAASsI,EAAM,EAClDN,EAAgB,GAChBD,EAAY,GACd,CAAC,EACD9E,EAAG,cAAe,IAAM,CAClBjD,EAAO,WAAa,CAACA,EAAO,SAAS,UACzCkI,EAAe,GACjB,CAAC,EACD,OAAO,OAAOlI,EAAO,SAAU,CAC7B,MAAAiJ,EACA,KAAAC,EACA,MAAAC,EACA,OAAAb,CACJ,CAAG,CACH,CC7RA,MAAMwB,GAA0B,CAQ9B,uBAAwB,EACxB,iCAAkC,IAClC,+BAAgC,EAChC,yBAA0B,CAC5B,EAMO,MAAMC,EAAoD,CAI/D,uBAAuBC,EAAoBC,EAAiD,CAC1F,MAAMC,EAAa,KAAK,iBAAiBF,CAAU,EAC7CG,EAAmB,KAAK,0BAA0BD,CAAU,EAC5DE,EAAc,KAAK,iBAAiBD,EAAkBF,EAAgBC,CAAU,EAEtF,MAAO,CACL,WAAAA,EACA,iBAAAC,EACA,YAAAC,CAAA,CAEJ,CAKA,sBAAsBC,EAA4C,CAChE,MAAMC,EAAmB,CAAA,EAgBzB,IAbI,CAACD,EAAO,UAAY,OAAOA,EAAO,SAAS,OAAU,UAAYA,EAAO,SAAS,OAAS5N,EAAgB,eAC5G6N,EAAO,KAAK,sCAAsC,GAGhD,OAAOD,EAAO,cAAiB,UAAYA,EAAO,aAAe5N,EAAgB,eACnF6N,EAAO,KAAK,oCAAoC,GAG9C,OAAOD,EAAO,eAAkB,UAAYA,EAAO,eAAiB5N,EAAgB,eACtF6N,EAAO,KAAK,qCAAqC,EAI/C,CAACD,EAAO,aAAe,OAAOA,EAAO,aAAgB,SACvDC,EAAO,KAAK,mCAAmC,MAE/C,UAAW,CAACC,EAAOC,CAAgB,IAAK,OAAO,QAAQH,EAAO,WAAW,EAAG,CAC1E,MAAMI,EAAW,OAAOF,CAAK,GACzB,OAAO,MAAME,CAAQ,GAAKA,GAAYhO,EAAgB,eACxD6N,EAAO,KAAK,6BAA6BC,CAAK,EAAE,GAE9C,OAAOC,EAAiB,eAAkB,UAAYA,EAAiB,eAAiB/N,EAAgB,eAC1G6N,EAAO,KAAK,wCAAwCC,CAAK,EAAE,GAEzD,OAAOC,EAAiB,cAAiB,UAAYA,EAAiB,aAAe/N,EAAgB,eACvG6N,EAAO,KAAK,uCAAuCC,CAAK,EAAE,CAE9D,CAIF,OAAI,CAACF,EAAO,YAAc,CAACA,EAAO,WAAW,IAAM,OAAOA,EAAO,WAAW,IAAO,WACjFC,EAAO,KAAK,qCAAqC,GAI/C,CAACD,EAAO,YAAc,CAACA,EAAO,WAAW,QAAU,CAACA,EAAO,WAAW,SACxEC,EAAO,KAAK,sCAAsC,EAG7C,CACL,QAASA,EAAO,SAAW7N,EAAgB,aAC3C,OAAA6N,CAAA,CAEJ,CAKQ,iBAAiBI,EAA8B,CAGrD,MAAO,EAQT,CAKQ,0BAA0BR,EAAuC,CACvE,MAAMS,EAAuB5N,EAAc,OAAO,OAAO,cAEzD,IAAI6N,EAAsBd,GAAwB,+BAC9Ce,EAAuBf,GAAwB,yBAEnD,OAAII,IACFU,EAAsBd,GAAwB,iCAC9Ce,EAAuBF,GAGlB,CACL,OAAQ,CACN,cAAeb,GAAwB,uBACvC,aAAc,EAAA,EAEhB,OAAQ,CACN,cAAec,EACf,aAAc,EAAA,EAEhB,QAAS,CACP,cAAeC,EACf,aAAc9N,EAAc,OAAO,OAAO,YAAA,CAC5C,CAEJ,CAKQ,iBACNoN,EACAF,EACAC,EACkB,CAClB,MAAMY,EAAiC,CACrC,IAAKX,EAAiB,OACtB,IAAKA,EAAiB,OACtB,KAAMA,EAAiB,OAAA,EAGzB,MAAO,CACL,KAAMD,EACN,SAAU,CACR,MAAOD,EACP,qBAAsB,EAAA,EAExB,aAAclN,EAAc,OAAO,OAAO,aAC1C,OAAQA,EAAc,OAAO,OAAO,OACpC,eAAgBA,EAAc,OAAO,OAAO,eAC5C,cAAeoN,EAAiB,QAAQ,cACxC,YAAAW,EACA,WAAY,CACV,GAAI/N,EAAc,OAAO,OAAO,WAAW,GAC3C,KAAMA,EAAc,OAAO,OAAO,WAAW,KAC7C,UAAW,EAAA,EAEb,WAAY,CACV,OAAQA,EAAc,OAAO,OAAO,WAAW,OAC/C,OAAQA,EAAc,OAAO,OAAO,WAAW,MAAA,EAEjD,aAAc,EACd,QAAS,CAACgG,GAAY4B,GAAY4C,EAAQ,CAAA,CAE9C,CACF,CClLO,MAAMwD,GAAiB,IAAe,CACzC,GAAI,CACA,KAAM,CAAE,UAAAC,GAAc,UAKtB,OAJwBA,EAAU,UAC9B1O,GAAiB,wBACjBA,GAAiB,wBAAA,IAEM,MAC/B,MAAQ,CACJ,MAAO,EACX,CACJ,ECXO,MAAM2O,EAA8C,CAMzD,iBAA+B,CAC7B,KAAK,yBAAA,EAEL,MAAMC,EAAYC,EAAuB,MAAO,CAC9C,GAAIpO,EAAc,OAAO,IAAI,YAC7B,UAAW,iCAAA,CACZ,EAED,YAAK,UAAYmO,EACVA,CACT,CAKA,oBAAoBA,EAAqC,CACvD,IAAIE,EAAY,UAAUrO,EAAc,OAAO,IAAI,WAAW,oBAG1DgO,OACFK,GAAa,qBAGf,MAAMpL,EAASmL,EAAuB,MAAO,CAC3C,UAAAC,CAAA,CACD,EAEDC,OAAAA,EAAqBH,EAAWlL,CAAM,EAC/BA,CACT,CAKA,oBAAoBA,EAAkC,CACpD,MAAMsL,EAAUH,EAAuB,MAAO,CAC5C,UAAW,uCAAA,CACZ,EAEDE,OAAAA,EAAqBrL,EAAQsL,CAAO,EAC7BA,CACT,CAKA,YAAYC,EAAkBC,EAAgC,CAC5D,MAAMC,EAAQN,EAAuB,MAAO,CAC1C,UAAW,mCAAA,CACZ,EAED,IAAIO,EAAe,GACnB,OAAIF,IACFE,EAAe,yBAAyBF,CAAS,KAInDC,EAAM,UAAY,iBAAiBC,CAAY,UAAUH,CAAQ,mCAE1DE,CACT,CAKA,iBAAiBzL,EAA2B,CAC1C,MAAM2L,EAAaR,EAAuB,MAAO,CAC/C,UAAW,mBAAA,CACZ,EACDE,EAAqBrL,EAAQ2L,CAAU,CACzC,CAKA,iBAAiB3L,EAA2B,CAC1C,MAAM4L,EAAaT,EAAuB,MAAO,CAC/C,UAAW,4EAAA,CACZ,EACKU,EAAaV,EAAuB,MAAO,CAC/C,UAAW,4EAAA,CACZ,EAEDE,EAAqBrL,EAAQ4L,CAAU,EACvCP,EAAqBrL,EAAQ6L,CAAU,CACzC,CAKA,YAAYX,EAA8B,CACxC,MAAMY,EAAmBC,GAAuB,qBAAqB,EACjED,GACFE,GAAsBF,EAAkBZ,CAAS,CAErD,CAKA,SAAgB,CACV,KAAK,YACPe,GAAuB,KAAK,SAAS,EACrC,OAAO,KAAK,UAEhB,CAKA,cAAwC,CACtC,OAAO,KAAK,SACd,CAKQ,0BAAiC,CACvC,MAAMC,EAAoBH,GAAuB,IAAIhP,EAAc,OAAO,IAAI,WAAW,EAAE,EACvFmP,GACFD,GAAuBC,CAAiB,EAG1C,MAAMC,EAAcC,GAA0B,WAAW,EACzD,UAAWtQ,KAAWqQ,EACpBF,GAAuBnQ,CAAO,CAElC,CACF,CCjIO,MAAMuQ,EAA8C,CAApD,aAAA,CACL,KAAiB,UAAYtP,EAAc,OAAO,SAAA,CAKlD,gBAAuC,CACrC,MAAMuP,EAA0B,CAAA,EAGhC,QAASC,EAAa/P,EAAoB,oBAAqB+P,GAAc,KAAK,UAAWA,GAAc/P,EAAoB,gBAAiB,CAC9I,MAAM+O,EAAW,KAAK,kBAAkB,wCAAwCgB,CAAU,EAAE,EACtFf,EAAY,KAAK,kBAAkB,uCAAuCe,CAAU,EAAE,EAExFhB,GACFe,EAAQ,KAAK,CACX,SAAU,OAAOf,CAAQ,EACzB,UAAW,OAAOC,GAAa,EAAE,EACjC,WAAAe,CAAA,CACD,CAEL,CAGA,OAAO,KAAK,iBAAiBD,CAAO,CACtC,CAKA,kBAAkBE,EAAwC,CACxD,MAAMlC,EAAmB,CAAA,EAEzB,GAAI,CAAC,MAAM,QAAQkC,CAAI,EACrB,OAAAlC,EAAO,KAAK,6BAA6B,EAClC,CAAE,QAAS,GAAO,OAAAA,CAAA,EAG3B,GAAIkC,EAAK,SAAW/P,EAAgB,aAClC,OAAA6N,EAAO,KAAK,wBAAwB,EAC7B,CAAE,QAAS,GAAO,OAAAA,CAAA,EAG3B,UAAWmB,KAASe,GACd,CAACf,EAAM,UAAY,OAAOA,EAAM,UAAa,WAC/CnB,EAAO,KAAK,kCAAkCmB,EAAM,UAAU,EAAE,EAG9DA,EAAM,WAAa,OAAOA,EAAM,WAAc,UAChDnB,EAAO,KAAK,gCAAgCmB,EAAM,UAAU,EAAE,GAG5D,OAAOA,EAAM,YAAe,UAAYA,EAAM,WAAajP,EAAoB,sBACjF8N,EAAO,KAAK,wBAAwBmB,EAAM,UAAU,EAAE,EAIpDA,EAAM,UAAY,CAAC,KAAK,WAAWA,EAAM,QAAQ,GACnDnB,EAAO,KAAK,sCAAsCmB,EAAM,UAAU,EAAE,EAIlEA,EAAM,WAAa,CAAC,KAAK,WAAWA,EAAM,SAAS,GACrDnB,EAAO,KAAK,qCAAqCmB,EAAM,UAAU,EAAE,EAIvE,MAAO,CACL,QAASnB,EAAO,SAAW7N,EAAgB,aAC3C,OAAA6N,CAAA,CAEJ,CAKA,mBAA4B,CAC1B,MAAML,EAAiB,KAAK,kBAAkB,gDAAgD,EAC9F,GAAIA,EAAgB,CAClB,MAAMwC,EAAa,OAAO,SAAS,OAAOxC,CAAc,EAAG,EAAE,EAC7D,GAAI,CAAC,OAAO,MAAMwC,CAAU,GAAKA,EAAahQ,EAAgB,aAC5D,OAAOgQ,CAEX,CACA,OAAO1P,EAAc,OAAO,qBAC9B,CAKQ,iBAAiBuP,EAA+C,CACtE,OAAOA,EAAQ,IAAKb,IAAW,CAC7B,SAAUA,EAAM,SAChB,UAAWA,EAAM,UACjB,WAAYA,EAAM,WAClB,QAAS,KAAK,aAAaA,CAAK,CAAA,EAChC,EAAE,OAAQA,GAAUA,EAAM,OAAO,CACrC,CAKQ,aAAaA,EAA8B,CACjD,MAAO,GACLA,EAAM,UACN,OAAOA,EAAM,UAAa,UAC1B,KAAK,WAAWA,EAAM,QAAQ,GAC9B,OAAOA,EAAM,YAAe,UAC5BA,EAAM,WAAahP,EAAgB,aAEvC,CAKQ,WAAWiQ,EAAsB,CACvC,GAAI,CAEF,MAAO,EADW,IAAI,IAAIA,CAAG,CAE/B,MAAQ,CAEN,OAAOA,EAAI,WAAW,GAAG,GAAKA,EAAI,WAAW,IAAI,GAAKA,EAAI,WAAW,OAAO,CAC9E,CACF,CAKQ,kBAAkB3Q,EAAsB,CAC9C,GAAI,CACF,MAAM4Q,EAAQC,GAAOA,EAAI,MACnBC,EAASF,GAASA,EAAM,UAC9B,OAAI,OAAOE,GAAW,WACbA,EAAO,KAAKF,EAAO5Q,CAAG,EAE/B,MACF,MAAQ,CACN,MACF,CACF,CACF,CCvJA,IAAI+Q,GACJ,SAASC,IAAc,CACrB,MAAMvO,EAASZ,EAAS,EAClB2L,EAAWhM,EAAW,EAC5B,MAAO,CACL,aAAcgM,EAAS,iBAAmBA,EAAS,gBAAgB,OAAS,mBAAoBA,EAAS,gBAAgB,MACzH,MAAO,CAAC,EAAE,iBAAkB/K,GAAUA,EAAO,eAAiB+K,aAAoB/K,EAAO,cAC7F,CACA,CACA,SAASwO,IAAa,CACpB,OAAKF,KACHA,GAAUC,GAAW,GAEhBD,EACT,CAEA,IAAIG,GACJ,SAASC,GAAWC,EAAO,CACzB,GAAI,CACF,UAAAnC,CACJ,EAAMmC,IAAU,OAAS,CAAA,EAAKA,EAC5B,MAAML,EAAUE,GAAU,EACpBxO,EAASZ,EAAS,EAClBwP,EAAW5O,EAAO,UAAU,SAC5B6O,EAAKrC,GAAaxM,EAAO,UAAU,UACnC8O,EAAS,CACb,IAAK,GACL,QAAS,EACb,EACQC,EAAc/O,EAAO,OAAO,MAC5BgP,EAAehP,EAAO,OAAO,OAC7BiP,EAAUJ,EAAG,MAAM,6BAA6B,EACtD,IAAIK,EAAOL,EAAG,MAAM,sBAAsB,EAC1C,MAAMM,EAAON,EAAG,MAAM,yBAAyB,EACzCO,EAAS,CAACF,GAAQL,EAAG,MAAM,4BAA4B,EACvDQ,EAAUT,IAAa,QAC7B,IAAIU,EAAQV,IAAa,WAGzB,MAAMW,EAAc,CAAC,YAAa,YAAa,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAU,EACrK,MAAI,CAACL,GAAQI,GAAShB,EAAQ,OAASiB,EAAY,QAAQ,GAAGR,CAAW,IAAIC,CAAY,EAAE,GAAK,IAC9FE,EAAOL,EAAG,MAAM,qBAAqB,EAChCK,IAAMA,EAAO,CAAC,EAAG,EAAG,QAAQ,GACjCI,EAAQ,IAINL,GAAW,CAACI,IACdP,EAAO,GAAK,UACZA,EAAO,QAAU,KAEfI,GAAQE,GAAUD,KACpBL,EAAO,GAAK,MACZA,EAAO,IAAM,IAIRA,CACT,CACA,SAASU,GAAUC,EAAW,CAC5B,OAAIA,IAAc,SAChBA,EAAY,CAAA,GAEThB,KACHA,GAAeC,GAAWe,CAAS,GAE9BhB,EACT,CAEA,IAAIiB,GACJ,SAASC,IAAc,CACrB,MAAM3P,EAASZ,EAAS,EAClB0P,EAASU,GAAS,EACxB,IAAII,EAAqB,GACzB,SAASC,GAAW,CAClB,MAAMhB,EAAK7O,EAAO,UAAU,UAAU,YAAW,EACjD,OAAO6O,EAAG,QAAQ,QAAQ,GAAK,GAAKA,EAAG,QAAQ,QAAQ,EAAI,GAAKA,EAAG,QAAQ,SAAS,EAAI,CAC1F,CACA,GAAIgB,EAAQ,EAAI,CACd,MAAMhB,EAAK,OAAO7O,EAAO,UAAU,SAAS,EAC5C,GAAI6O,EAAG,SAAS,UAAU,EAAG,CAC3B,KAAM,CAACiB,EAAOC,CAAK,EAAIlB,EAAG,MAAM,UAAU,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAImB,GAAO,OAAOA,CAAG,CAAC,EAC9FJ,EAAqBE,EAAQ,IAAMA,IAAU,IAAMC,EAAQ,CAC7D,CACF,CACA,MAAME,EAAY,+CAA+C,KAAKjQ,EAAO,UAAU,SAAS,EAC1FkQ,EAAkBL,EAAQ,EAC1BM,EAAYD,GAAmBD,GAAanB,EAAO,IACzD,MAAO,CACL,SAAUc,GAAsBM,EAChC,mBAAAN,EACA,UAAAO,EACA,UAAAF,CACJ,CACA,CACA,SAASG,IAAa,CACpB,OAAKV,KACHA,GAAUC,GAAW,GAEhBD,EACT,CAEA,SAASW,GAAO9O,EAAM,CACpB,GAAI,CACF,OAAAC,EACA,GAAAiD,EACA,KAAAC,CACJ,EAAMnD,EACJ,MAAMvB,EAASZ,EAAS,EACxB,IAAIkR,EAAW,KACXC,EAAiB,KACrB,MAAMC,EAAgB,IAAM,CACtB,CAAChP,GAAUA,EAAO,WAAa,CAACA,EAAO,cAC3CkD,EAAK,cAAc,EACnBA,EAAK,QAAQ,EACf,EACM+L,EAAiB,IAAM,CACvB,CAACjP,GAAUA,EAAO,WAAa,CAACA,EAAO,cAC3C8O,EAAW,IAAI,eAAeI,GAAW,CACvCH,EAAiBvQ,EAAO,sBAAsB,IAAM,CAClD,KAAM,CACJ,MAAA+L,EACA,OAAA4E,CACV,EAAYnP,EACJ,IAAIoP,EAAW7E,EACX8E,EAAYF,EAChBD,EAAQ,QAAQI,GAAS,CACvB,GAAI,CACF,eAAAC,EACA,YAAAC,EACA,OAAArS,CACZ,EAAcmS,EACAnS,GAAUA,IAAW6C,EAAO,KAChCoP,EAAWI,EAAcA,EAAY,OAASD,EAAe,CAAC,GAAKA,GAAgB,WACnFF,EAAYG,EAAcA,EAAY,QAAUD,EAAe,CAAC,GAAKA,GAAgB,UACvF,CAAC,GACGH,IAAa7E,GAAS8E,IAAcF,IACtCH,EAAa,CAEjB,CAAC,CACH,CAAC,EACDF,EAAS,QAAQ9O,EAAO,EAAE,EAC5B,EACMyP,EAAiB,IAAM,CACvBV,GACFvQ,EAAO,qBAAqBuQ,CAAc,EAExCD,GAAYA,EAAS,WAAa9O,EAAO,KAC3C8O,EAAS,UAAU9O,EAAO,EAAE,EAC5B8O,EAAW,KAEf,EACMY,EAA2B,IAAM,CACjC,CAAC1P,GAAUA,EAAO,WAAa,CAACA,EAAO,aAC3CkD,EAAK,mBAAmB,CAC1B,EACAD,EAAG,OAAQ,IAAM,CACf,GAAIjD,EAAO,OAAO,gBAAkB,OAAOxB,EAAO,eAAmB,IAAa,CAChFyQ,EAAc,EACd,MACF,CACAzQ,EAAO,iBAAiB,SAAUwQ,CAAa,EAC/CxQ,EAAO,iBAAiB,oBAAqBkR,CAAwB,CACvE,CAAC,EACDzM,EAAG,UAAW,IAAM,CAClBwM,EAAc,EACdjR,EAAO,oBAAoB,SAAUwQ,CAAa,EAClDxQ,EAAO,oBAAoB,oBAAqBkR,CAAwB,CAC1E,CAAC,CACH,CAEA,SAASC,GAAS5P,EAAM,CACtB,GAAI,CACF,OAAAC,EACA,aAAAgD,EACA,GAAAC,EACA,KAAAC,CACJ,EAAMnD,EACJ,MAAM6P,EAAY,CAAA,EACZpR,EAASZ,EAAS,EAClBiS,EAAS,SAAU1S,EAAQvB,EAAS,CACpCA,IAAY,SACdA,EAAU,CAAA,GAEZ,MAAMkU,EAAetR,EAAO,kBAAoBA,EAAO,uBACjDsQ,EAAW,IAAIgB,EAAaC,GAAa,CAI7C,GAAI/P,EAAO,oBAAqB,OAChC,GAAI+P,EAAU,SAAW,EAAG,CAC1B7M,EAAK,iBAAkB6M,EAAU,CAAC,CAAC,EACnC,MACF,CACA,MAAMC,EAAiB,UAA0B,CAC/C9M,EAAK,iBAAkB6M,EAAU,CAAC,CAAC,CACrC,EACIvR,EAAO,sBACTA,EAAO,sBAAsBwR,CAAc,EAE3CxR,EAAO,WAAWwR,EAAgB,CAAC,CAEvC,CAAC,EACDlB,EAAS,QAAQ3R,EAAQ,CACvB,WAAY,OAAOvB,EAAQ,WAAe,IAAc,GAAOA,EAAQ,WACvE,UAAWoE,EAAO,YAAc,OAAOpE,EAAQ,UAAc,IAAc,GAAOA,GAAS,UAC3F,cAAe,OAAOA,EAAQ,cAAkB,IAAc,GAAOA,EAAQ,aACnF,CAAK,EACDgU,EAAU,KAAKd,CAAQ,CACzB,EACMhL,EAAO,IAAM,CACjB,GAAK9D,EAAO,OAAO,SACnB,IAAIA,EAAO,OAAO,eAAgB,CAChC,MAAMiQ,EAAmB/N,GAAelC,EAAO,MAAM,EACrD,QAASZ,EAAI,EAAGA,EAAI6Q,EAAiB,OAAQ7Q,GAAK,EAChDyQ,EAAOI,EAAiB7Q,CAAC,CAAC,CAE9B,CAEAyQ,EAAO7P,EAAO,OAAQ,CACpB,UAAWA,EAAO,OAAO,oBAC/B,CAAK,EAGD6P,EAAO7P,EAAO,UAAW,CACvB,WAAY,EAClB,CAAK,EACH,EACMgE,EAAU,IAAM,CACpB4L,EAAU,QAAQd,GAAY,CAC5BA,EAAS,WAAU,CACrB,CAAC,EACDc,EAAU,OAAO,EAAGA,EAAU,MAAM,CACtC,EACA5M,EAAa,CACX,SAAU,GACV,eAAgB,GAChB,qBAAsB,EAC1B,CAAG,EACDC,EAAG,OAAQa,CAAI,EACfb,EAAG,UAAWe,CAAO,CACvB,CAIA,IAAIkM,GAAgB,CAClB,GAAGC,EAAQC,EAASC,EAAU,CAC5B,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,MAAMC,EAASF,EAAW,UAAY,OACtC,OAAAF,EAAO,MAAM,GAAG,EAAE,QAAQK,GAAS,CAC5BF,EAAK,gBAAgBE,CAAK,IAAGF,EAAK,gBAAgBE,CAAK,EAAI,CAAA,GAChEF,EAAK,gBAAgBE,CAAK,EAAED,CAAM,EAAEH,CAAO,CAC7C,CAAC,EACME,CACT,EACA,KAAKH,EAAQC,EAASC,EAAU,CAC9B,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,SAASG,GAAc,CACrBH,EAAK,IAAIH,EAAQM,CAAW,EACxBA,EAAY,gBACd,OAAOA,EAAY,eAErB,QAASC,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAE7BR,EAAQ,MAAME,EAAMK,CAAI,CAC1B,CACA,OAAAF,EAAY,eAAiBL,EACtBE,EAAK,GAAGH,EAAQM,EAAaJ,CAAQ,CAC9C,EACA,MAAMD,EAASC,EAAU,CACvB,MAAMC,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,OAAOF,GAAY,WAAY,OAAOE,EAC1C,MAAMC,EAASF,EAAW,UAAY,OACtC,OAAIC,EAAK,mBAAmB,QAAQF,CAAO,EAAI,GAC7CE,EAAK,mBAAmBC,CAAM,EAAEH,CAAO,EAElCE,CACT,EACA,OAAOF,EAAS,CACd,MAAME,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,mBAAoB,OAAOA,EACrC,MAAM7K,EAAQ6K,EAAK,mBAAmB,QAAQF,CAAO,EACrD,OAAI3K,GAAS,GACX6K,EAAK,mBAAmB,OAAO7K,EAAO,CAAC,EAElC6K,CACT,EACA,IAAIH,EAAQC,EAAS,CACnB,MAAME,EAAO,KAEb,MADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,iBACVH,EAAO,MAAM,GAAG,EAAE,QAAQK,GAAS,CAC7B,OAAOJ,EAAY,IACrBE,EAAK,gBAAgBE,CAAK,EAAI,CAAA,EACrBF,EAAK,gBAAgBE,CAAK,GACnCF,EAAK,gBAAgBE,CAAK,EAAE,QAAQ,CAACK,EAAcpL,IAAU,EACvDoL,IAAiBT,GAAWS,EAAa,gBAAkBA,EAAa,iBAAmBT,IAC7FE,EAAK,gBAAgBE,CAAK,EAAE,OAAO/K,EAAO,CAAC,CAE/C,CAAC,CAEL,CAAC,EACM6K,CACT,EACA,MAAO,CACL,MAAMA,EAAO,KAEb,GADI,CAACA,EAAK,iBAAmBA,EAAK,WAC9B,CAACA,EAAK,gBAAiB,OAAOA,EAClC,IAAIH,EACA3D,EACAsE,EACJ,QAASC,EAAQ,UAAU,OAAQJ,EAAO,IAAI,MAAMI,CAAK,EAAGC,EAAQ,EAAGA,EAAQD,EAAOC,IACpFL,EAAKK,CAAK,EAAI,UAAUA,CAAK,EAE/B,OAAI,OAAOL,EAAK,CAAC,GAAM,UAAY,MAAM,QAAQA,EAAK,CAAC,CAAC,GACtDR,EAASQ,EAAK,CAAC,EACfnE,EAAOmE,EAAK,MAAM,EAAGA,EAAK,MAAM,EAChCG,EAAUR,IAEVH,EAASQ,EAAK,CAAC,EAAE,OACjBnE,EAAOmE,EAAK,CAAC,EAAE,KACfG,EAAUH,EAAK,CAAC,EAAE,SAAWL,GAE/B9D,EAAK,QAAQsE,CAAO,GACA,MAAM,QAAQX,CAAM,EAAIA,EAASA,EAAO,MAAM,GAAG,GACzD,QAAQK,GAAS,CACvBF,EAAK,oBAAsBA,EAAK,mBAAmB,QACrDA,EAAK,mBAAmB,QAAQO,GAAgB,CAC9CA,EAAa,MAAMC,EAAS,CAACN,EAAO,GAAGhE,CAAI,CAAC,CAC9C,CAAC,EAEC8D,EAAK,iBAAmBA,EAAK,gBAAgBE,CAAK,GACpDF,EAAK,gBAAgBE,CAAK,EAAE,QAAQK,GAAgB,CAClDA,EAAa,MAAMC,EAAStE,CAAI,CAClC,CAAC,CAEL,CAAC,EACM8D,CACT,CACF,EAEA,SAASW,IAAa,CACpB,MAAMjR,EAAS,KACf,IAAIuK,EACA4E,EACJ,MAAM5Q,EAAKyB,EAAO,GACd,OAAOA,EAAO,OAAO,MAAU,KAAeA,EAAO,OAAO,QAAU,KACxEuK,EAAQvK,EAAO,OAAO,MAEtBuK,EAAQhM,EAAG,YAET,OAAOyB,EAAO,OAAO,OAAW,KAAeA,EAAO,OAAO,SAAW,KAC1EmP,EAASnP,EAAO,OAAO,OAEvBmP,EAAS5Q,EAAG,aAEV,EAAAgM,IAAU,GAAKvK,EAAO,aAAY,GAAMmP,IAAW,GAAKnP,EAAO,gBAKnEuK,EAAQA,EAAQ,SAASxI,EAAaxD,EAAI,cAAc,GAAK,EAAG,EAAE,EAAI,SAASwD,EAAaxD,EAAI,eAAe,GAAK,EAAG,EAAE,EACzH4Q,EAASA,EAAS,SAASpN,EAAaxD,EAAI,aAAa,GAAK,EAAG,EAAE,EAAI,SAASwD,EAAaxD,EAAI,gBAAgB,GAAK,EAAG,EAAE,EACvH,OAAO,MAAMgM,CAAK,IAAGA,EAAQ,GAC7B,OAAO,MAAM4E,CAAM,IAAGA,EAAS,GACnC,OAAO,OAAOnP,EAAQ,CACpB,MAAAuK,EACA,OAAA4E,EACA,KAAMnP,EAAO,aAAY,EAAKuK,EAAQ4E,CAC1C,CAAG,EACH,CAEA,SAAS+B,IAAe,CACtB,MAAMlR,EAAS,KACf,SAASmR,EAA0BjS,EAAMkS,EAAO,CAC9C,OAAO,WAAWlS,EAAK,iBAAiBc,EAAO,kBAAkBoR,CAAK,CAAC,GAAK,CAAC,CAC/E,CACA,MAAMvO,EAAS7C,EAAO,OAChB,CACJ,UAAAqR,EACA,SAAAC,EACA,KAAMC,EACN,aAAc5L,EACd,SAAA6L,CACJ,EAAMxR,EACEyR,EAAYzR,EAAO,SAAW6C,EAAO,QAAQ,QAC7C6O,EAAuBD,EAAYzR,EAAO,QAAQ,OAAO,OAASA,EAAO,OAAO,OAChF2R,EAAS7Q,EAAgBwQ,EAAU,IAAItR,EAAO,OAAO,UAAU,gBAAgB,EAC/E6F,EAAe4L,EAAYzR,EAAO,QAAQ,OAAO,OAAS2R,EAAO,OACvE,IAAIC,EAAW,CAAA,EACf,MAAMC,EAAa,CAAA,EACbC,EAAkB,CAAA,EACxB,IAAIC,EAAelP,EAAO,mBACtB,OAAOkP,GAAiB,aAC1BA,EAAelP,EAAO,mBAAmB,KAAK7C,CAAM,GAEtD,IAAIgS,EAAcnP,EAAO,kBACrB,OAAOmP,GAAgB,aACzBA,EAAcnP,EAAO,kBAAkB,KAAK7C,CAAM,GAEpD,MAAMiS,EAAyBjS,EAAO,SAAS,OACzCkS,EAA2BlS,EAAO,WAAW,OACnD,IAAImS,EAAetP,EAAO,aACtBuP,EAAgB,CAACL,EACjBM,EAAgB,EAChB5M,EAAQ,EACZ,GAAI,OAAO8L,EAAe,IACxB,OAEE,OAAOY,GAAiB,UAAYA,EAAa,QAAQ,GAAG,GAAK,EACnEA,EAAe,WAAWA,EAAa,QAAQ,IAAK,EAAE,CAAC,EAAI,IAAMZ,EACxD,OAAOY,GAAiB,WACjCA,EAAe,WAAWA,CAAY,GAExCnS,EAAO,YAAc,CAACmS,EAGtBR,EAAO,QAAQhJ,GAAW,CACpBhD,EACFgD,EAAQ,MAAM,WAAa,GAE3BA,EAAQ,MAAM,YAAc,GAE9BA,EAAQ,MAAM,aAAe,GAC7BA,EAAQ,MAAM,UAAY,EAC5B,CAAC,EAGG9F,EAAO,gBAAkBA,EAAO,UAClClD,GAAe0R,EAAW,kCAAmC,EAAE,EAC/D1R,GAAe0R,EAAW,iCAAkC,EAAE,GAEhE,MAAMiB,EAAczP,EAAO,MAAQA,EAAO,KAAK,KAAO,GAAK7C,EAAO,KAC9DsS,EACFtS,EAAO,KAAK,WAAW2R,CAAM,EACpB3R,EAAO,MAChBA,EAAO,KAAK,YAAW,EAIzB,IAAIuS,EACJ,MAAMC,EAAuB3P,EAAO,gBAAkB,QAAUA,EAAO,aAAe,OAAO,KAAKA,EAAO,WAAW,EAAE,OAAO9G,GACpH,OAAO8G,EAAO,YAAY9G,CAAG,EAAE,cAAkB,GACzD,EAAE,OAAS,EACZ,QAASqD,EAAI,EAAGA,EAAIyG,EAAczG,GAAK,EAAG,CACxCmT,EAAY,EACZ,IAAI9G,EAKJ,GAJIkG,EAAOvS,CAAC,IAAGqM,EAAQkG,EAAOvS,CAAC,GAC3BkT,GACFtS,EAAO,KAAK,YAAYZ,EAAGqM,EAAOkG,CAAM,EAEtC,EAAAA,EAAOvS,CAAC,GAAK2C,EAAa0J,EAAO,SAAS,IAAM,QAEpD,IAAI5I,EAAO,gBAAkB,OAAQ,CAC/B2P,IACFb,EAAOvS,CAAC,EAAE,MAAMY,EAAO,kBAAkB,OAAO,CAAC,EAAI,IAEvD,MAAMyS,EAAc,iBAAiBhH,CAAK,EACpCiH,EAAmBjH,EAAM,MAAM,UAC/BkH,EAAyBlH,EAAM,MAAM,gBAO3C,GANIiH,IACFjH,EAAM,MAAM,UAAY,QAEtBkH,IACFlH,EAAM,MAAM,gBAAkB,QAE5B5I,EAAO,aACT0P,EAAYvS,EAAO,aAAY,EAAKoC,GAAiBqJ,EAAO,OAAa,EAAIrJ,GAAiBqJ,EAAO,QAAc,MAC9G,CAEL,MAAMlB,EAAQ4G,EAA0BsB,EAAa,OAAO,EACtDG,EAAczB,EAA0BsB,EAAa,cAAc,EACnEI,EAAe1B,EAA0BsB,EAAa,eAAe,EACrEK,EAAa3B,EAA0BsB,EAAa,aAAa,EACjEM,EAAc5B,EAA0BsB,EAAa,cAAc,EACnEO,EAAYP,EAAY,iBAAiB,YAAY,EAC3D,GAAIO,GAAaA,IAAc,aAC7BT,EAAYhI,EAAQuI,EAAaC,MAC5B,CACL,KAAM,CACJ,YAAAE,EACA,YAAAC,EACZ,EAAczH,EACJ8G,EAAYhI,EAAQqI,EAAcC,EAAeC,EAAaC,GAAeG,GAAcD,EAC7F,CACF,CACIP,IACFjH,EAAM,MAAM,UAAYiH,GAEtBC,IACFlH,EAAM,MAAM,gBAAkBkH,GAE5B9P,EAAO,eAAc0P,EAAY,KAAK,MAAMA,CAAS,EAC3D,MACEA,GAAahB,GAAc1O,EAAO,cAAgB,GAAKsP,GAAgBtP,EAAO,cAC1EA,EAAO,eAAc0P,EAAY,KAAK,MAAMA,CAAS,GACrDZ,EAAOvS,CAAC,IACVuS,EAAOvS,CAAC,EAAE,MAAMY,EAAO,kBAAkB,OAAO,CAAC,EAAI,GAAGuS,CAAS,MAGjEZ,EAAOvS,CAAC,IACVuS,EAAOvS,CAAC,EAAE,gBAAkBmT,GAE9BT,EAAgB,KAAKS,CAAS,EAC1B1P,EAAO,gBACTuP,EAAgBA,EAAgBG,EAAY,EAAIF,EAAgB,EAAIF,EAChEE,IAAkB,GAAKjT,IAAM,IAAGgT,EAAgBA,EAAgBb,EAAa,EAAIY,GACjF/S,IAAM,IAAGgT,EAAgBA,EAAgBb,EAAa,EAAIY,GAC1D,KAAK,IAAIC,CAAa,EAAI,EAAI,MAAMA,EAAgB,GACpDvP,EAAO,eAAcuP,EAAgB,KAAK,MAAMA,CAAa,GAC7D3M,EAAQ5C,EAAO,iBAAmB,GAAG+O,EAAS,KAAKQ,CAAa,EACpEP,EAAW,KAAKO,CAAa,IAEzBvP,EAAO,eAAcuP,EAAgB,KAAK,MAAMA,CAAa,IAC5D3M,EAAQ,KAAK,IAAIzF,EAAO,OAAO,mBAAoByF,CAAK,GAAKzF,EAAO,OAAO,iBAAmB,GAAG4R,EAAS,KAAKQ,CAAa,EACjIP,EAAW,KAAKO,CAAa,EAC7BA,EAAgBA,EAAgBG,EAAYJ,GAE9CnS,EAAO,aAAeuS,EAAYJ,EAClCE,EAAgBE,EAChB9M,GAAS,EACX,CAaA,GAZAzF,EAAO,YAAc,KAAK,IAAIA,EAAO,YAAauR,CAAU,EAAIS,EAC5DrM,GAAO6L,IAAa3O,EAAO,SAAW,SAAWA,EAAO,SAAW,eACrEwO,EAAU,MAAM,MAAQ,GAAGrR,EAAO,YAAcmS,CAAY,MAE1DtP,EAAO,iBACTwO,EAAU,MAAMrR,EAAO,kBAAkB,OAAO,CAAC,EAAI,GAAGA,EAAO,YAAcmS,CAAY,MAEvFG,GACFtS,EAAO,KAAK,kBAAkBuS,EAAWX,CAAQ,EAI/C,CAAC/O,EAAO,eAAgB,CAC1B,MAAMsQ,EAAgB,CAAA,EACtB,QAAS/T,EAAI,EAAGA,EAAIwS,EAAS,OAAQxS,GAAK,EAAG,CAC3C,IAAIgU,EAAiBxB,EAASxS,CAAC,EAC3ByD,EAAO,eAAcuQ,EAAiB,KAAK,MAAMA,CAAc,GAC/DxB,EAASxS,CAAC,GAAKY,EAAO,YAAcuR,GACtC4B,EAAc,KAAKC,CAAc,CAErC,CACAxB,EAAWuB,EACP,KAAK,MAAMnT,EAAO,YAAcuR,CAAU,EAAI,KAAK,MAAMK,EAASA,EAAS,OAAS,CAAC,CAAC,EAAI,GAC5FA,EAAS,KAAK5R,EAAO,YAAcuR,CAAU,CAEjD,CACA,GAAIE,GAAa5O,EAAO,KAAM,CAC5B,MAAMR,EAAOyP,EAAgB,CAAC,EAAIK,EAClC,GAAItP,EAAO,eAAiB,EAAG,CAC7B,MAAMwQ,EAAS,KAAK,MAAMrT,EAAO,QAAQ,aAAeA,EAAO,QAAQ,aAAe6C,EAAO,cAAc,EACrGyQ,EAAYjR,EAAOQ,EAAO,eAChC,QAASzD,EAAI,EAAGA,EAAIiU,EAAQjU,GAAK,EAC/BwS,EAAS,KAAKA,EAASA,EAAS,OAAS,CAAC,EAAI0B,CAAS,CAE3D,CACA,QAASlU,EAAI,EAAGA,EAAIY,EAAO,QAAQ,aAAeA,EAAO,QAAQ,YAAaZ,GAAK,EAC7EyD,EAAO,iBAAmB,GAC5B+O,EAAS,KAAKA,EAASA,EAAS,OAAS,CAAC,EAAIvP,CAAI,EAEpDwP,EAAW,KAAKA,EAAWA,EAAW,OAAS,CAAC,EAAIxP,CAAI,EACxDrC,EAAO,aAAeqC,CAE1B,CAEA,GADIuP,EAAS,SAAW,IAAGA,EAAW,CAAC,CAAC,GACpCO,IAAiB,EAAG,CACtB,MAAMpW,EAAMiE,EAAO,aAAY,GAAM2F,EAAM,aAAe3F,EAAO,kBAAkB,aAAa,EAChG2R,EAAO,OAAO,CAAC4B,EAAGhH,IACZ,CAAC1J,EAAO,SAAWA,EAAO,KAAa,GACvC0J,IAAeoF,EAAO,OAAS,CAIpC,EAAE,QAAQhJ,GAAW,CACpBA,EAAQ,MAAM5M,CAAG,EAAI,GAAGoW,CAAY,IACtC,CAAC,CACH,CACA,GAAItP,EAAO,gBAAkBA,EAAO,qBAAsB,CACxD,IAAI2Q,EAAgB,EACpB1B,EAAgB,QAAQ2B,GAAkB,CACxCD,GAAiBC,GAAkBtB,GAAgB,EACrD,CAAC,EACDqB,GAAiBrB,EACjB,MAAMuB,EAAUF,EAAgBjC,EAAaiC,EAAgBjC,EAAa,EAC1EK,EAAWA,EAAS,IAAI+B,GAClBA,GAAQ,EAAU,CAAC5B,EACnB4B,EAAOD,EAAgBA,EAAU1B,EAC9B2B,CACR,CACH,CACA,GAAI9Q,EAAO,yBAA0B,CACnC,IAAI2Q,EAAgB,EACpB1B,EAAgB,QAAQ2B,GAAkB,CACxCD,GAAiBC,GAAkBtB,GAAgB,EACrD,CAAC,EACDqB,GAAiBrB,EACjB,MAAMyB,GAAc/Q,EAAO,oBAAsB,IAAMA,EAAO,mBAAqB,GACnF,GAAI2Q,EAAgBI,EAAarC,EAAY,CAC3C,MAAMsC,GAAmBtC,EAAaiC,EAAgBI,GAAc,EACpEhC,EAAS,QAAQ,CAAC+B,EAAMG,IAAc,CACpClC,EAASkC,CAAS,EAAIH,EAAOE,CAC/B,CAAC,EACDhC,EAAW,QAAQ,CAAC8B,EAAMG,IAAc,CACtCjC,EAAWiC,CAAS,EAAIH,EAAOE,CACjC,CAAC,CACH,CACF,CAOA,GANA,OAAO,OAAO7T,EAAQ,CACpB,OAAA2R,EACA,SAAAC,EACA,WAAAC,EACA,gBAAAC,CACJ,CAAG,EACGjP,EAAO,gBAAkBA,EAAO,SAAW,CAACA,EAAO,qBAAsB,CAC3ElD,GAAe0R,EAAW,kCAAmC,GAAG,CAACO,EAAS,CAAC,CAAC,IAAI,EAChFjS,GAAe0R,EAAW,iCAAkC,GAAGrR,EAAO,KAAO,EAAI8R,EAAgBA,EAAgB,OAAS,CAAC,EAAI,CAAC,IAAI,EACpI,MAAMiC,EAAgB,CAAC/T,EAAO,SAAS,CAAC,EAClCgU,EAAkB,CAAChU,EAAO,WAAW,CAAC,EAC5CA,EAAO,SAAWA,EAAO,SAAS,IAAIiU,GAAKA,EAAIF,CAAa,EAC5D/T,EAAO,WAAaA,EAAO,WAAW,IAAIiU,GAAKA,EAAID,CAAe,CACpE,CAeA,GAdInO,IAAiB6L,GACnB1R,EAAO,KAAK,oBAAoB,EAE9B4R,EAAS,SAAWK,IAClBjS,EAAO,OAAO,eAAeA,EAAO,cAAa,EACrDA,EAAO,KAAK,sBAAsB,GAEhC6R,EAAW,SAAWK,GACxBlS,EAAO,KAAK,wBAAwB,EAElC6C,EAAO,qBACT7C,EAAO,mBAAkB,EAE3BA,EAAO,KAAK,eAAe,EACvB,CAACyR,GAAa,CAAC5O,EAAO,UAAYA,EAAO,SAAW,SAAWA,EAAO,SAAW,QAAS,CAC5F,MAAMqR,EAAsB,GAAGrR,EAAO,sBAAsB,kBACtDsR,EAA6BnU,EAAO,GAAG,UAAU,SAASkU,CAAmB,EAC/ErO,GAAgBhD,EAAO,wBACpBsR,GAA4BnU,EAAO,GAAG,UAAU,IAAIkU,CAAmB,EACnEC,GACTnU,EAAO,GAAG,UAAU,OAAOkU,CAAmB,CAElD,CACF,CAEA,SAASE,GAAiBrL,EAAO,CAC/B,MAAM/I,EAAS,KACTqU,EAAe,CAAA,EACf5C,EAAYzR,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1D,IAAIqP,EAAY,EACZjQ,EACA,OAAO2J,GAAU,SACnB/I,EAAO,cAAc+I,CAAK,EACjBA,IAAU,IACnB/I,EAAO,cAAcA,EAAO,OAAO,KAAK,EAE1C,MAAMsU,EAAkB7O,GAClBgM,EACKzR,EAAO,OAAOA,EAAO,oBAAoByF,CAAK,CAAC,EAEjDzF,EAAO,OAAOyF,CAAK,EAG5B,GAAIzF,EAAO,OAAO,gBAAkB,QAAUA,EAAO,OAAO,cAAgB,EAC1E,GAAIA,EAAO,OAAO,gBACfA,EAAO,eAAiB,IAAI,QAAQyL,GAAS,CAC5C4I,EAAa,KAAK5I,CAAK,CACzB,CAAC,MAED,KAAKrM,EAAI,EAAGA,EAAI,KAAK,KAAKY,EAAO,OAAO,aAAa,EAAGZ,GAAK,EAAG,CAC9D,MAAMqG,EAAQzF,EAAO,YAAcZ,EACnC,GAAIqG,EAAQzF,EAAO,OAAO,QAAU,CAACyR,EAAW,MAChD4C,EAAa,KAAKC,EAAgB7O,CAAK,CAAC,CAC1C,MAGF4O,EAAa,KAAKC,EAAgBtU,EAAO,WAAW,CAAC,EAIvD,IAAKZ,EAAI,EAAGA,EAAIiV,EAAa,OAAQjV,GAAK,EACxC,GAAI,OAAOiV,EAAajV,CAAC,EAAM,IAAa,CAC1C,MAAM+P,EAASkF,EAAajV,CAAC,EAAE,aAC/BiQ,EAAYF,EAASE,EAAYF,EAASE,CAC5C,EAIEA,GAAaA,IAAc,KAAGrP,EAAO,UAAU,MAAM,OAAS,GAAGqP,CAAS,KAChF,CAEA,SAASkF,IAAqB,CAC5B,MAAMvU,EAAS,KACT2R,EAAS3R,EAAO,OAEhBwU,EAAcxU,EAAO,UAAYA,EAAO,aAAY,EAAKA,EAAO,UAAU,WAAaA,EAAO,UAAU,UAAY,EAC1H,QAAS,EAAI,EAAG,EAAI2R,EAAO,OAAQ,GAAK,EACtCA,EAAO,CAAC,EAAE,mBAAqB3R,EAAO,aAAY,EAAK2R,EAAO,CAAC,EAAE,WAAaA,EAAO,CAAC,EAAE,WAAa6C,EAAcxU,EAAO,sBAAqB,CAEnJ,CAEA,MAAMyU,GAAuB,CAAC9L,EAAS+L,EAAWtJ,IAAc,CAC1DsJ,GAAa,CAAC/L,EAAQ,UAAU,SAASyC,CAAS,EACpDzC,EAAQ,UAAU,IAAIyC,CAAS,EACtB,CAACsJ,GAAa/L,EAAQ,UAAU,SAASyC,CAAS,GAC3DzC,EAAQ,UAAU,OAAOyC,CAAS,CAEtC,EACA,SAASuJ,GAAqBC,EAAW,CACnCA,IAAc,SAChBA,EAAY,MAAQ,KAAK,WAAa,GAExC,MAAM5U,EAAS,KACT6C,EAAS7C,EAAO,OAChB,CACJ,OAAA2R,EACA,aAAchM,EACd,SAAAiM,CACJ,EAAM5R,EACJ,GAAI2R,EAAO,SAAW,EAAG,OACrB,OAAOA,EAAO,CAAC,EAAE,kBAAsB,KAAa3R,EAAO,mBAAkB,EACjF,IAAI6U,EAAe,CAACD,EAChBjP,IAAKkP,EAAeD,GACxB5U,EAAO,qBAAuB,CAAA,EAC9BA,EAAO,cAAgB,CAAA,EACvB,IAAImS,EAAetP,EAAO,aACtB,OAAOsP,GAAiB,UAAYA,EAAa,QAAQ,GAAG,GAAK,EACnEA,EAAe,WAAWA,EAAa,QAAQ,IAAK,EAAE,CAAC,EAAI,IAAMnS,EAAO,KAC/D,OAAOmS,GAAiB,WACjCA,EAAe,WAAWA,CAAY,GAExC,QAAS/S,EAAI,EAAGA,EAAIuS,EAAO,OAAQvS,GAAK,EAAG,CACzC,MAAMqM,EAAQkG,EAAOvS,CAAC,EACtB,IAAI0V,EAAcrJ,EAAM,kBACpB5I,EAAO,SAAWA,EAAO,iBAC3BiS,GAAenD,EAAO,CAAC,EAAE,mBAE3B,MAAMoD,GAAiBF,GAAgBhS,EAAO,eAAiB7C,EAAO,aAAY,EAAK,GAAK8U,IAAgBrJ,EAAM,gBAAkB0G,GAC9H6C,GAAyBH,EAAejD,EAAS,CAAC,GAAK/O,EAAO,eAAiB7C,EAAO,aAAY,EAAK,GAAK8U,IAAgBrJ,EAAM,gBAAkB0G,GACpJ8C,EAAc,EAAEJ,EAAeC,GAC/BI,EAAaD,EAAcjV,EAAO,gBAAgBZ,CAAC,EACnD+V,EAAiBF,GAAe,GAAKA,GAAejV,EAAO,KAAOA,EAAO,gBAAgBZ,CAAC,EAC1FgW,EAAYH,GAAe,GAAKA,EAAcjV,EAAO,KAAO,GAAKkV,EAAa,GAAKA,GAAclV,EAAO,MAAQiV,GAAe,GAAKC,GAAclV,EAAO,KAC3JoV,IACFpV,EAAO,cAAc,KAAKyL,CAAK,EAC/BzL,EAAO,qBAAqB,KAAKZ,CAAC,GAEpCqV,GAAqBhJ,EAAO2J,EAAWvS,EAAO,iBAAiB,EAC/D4R,GAAqBhJ,EAAO0J,EAAgBtS,EAAO,sBAAsB,EACzE4I,EAAM,SAAW9F,EAAM,CAACoP,EAAgBA,EACxCtJ,EAAM,iBAAmB9F,EAAM,CAACqP,EAAwBA,CAC1D,CACF,CAEA,SAASK,GAAeT,EAAW,CACjC,MAAM5U,EAAS,KACf,GAAI,OAAO4U,EAAc,IAAa,CACpC,MAAMU,EAAatV,EAAO,aAAe,GAAK,EAE9C4U,EAAY5U,GAAUA,EAAO,WAAaA,EAAO,UAAYsV,GAAc,CAC7E,CACA,MAAMzS,EAAS7C,EAAO,OAChBuV,EAAiBvV,EAAO,aAAY,EAAKA,EAAO,aAAY,EAClE,GAAI,CACF,SAAAW,EACA,YAAA6U,EACA,MAAAC,EACA,aAAAC,CACJ,EAAM1V,EACJ,MAAM2V,EAAeH,EACfI,EAASH,EACf,GAAIF,IAAmB,EACrB5U,EAAW,EACX6U,EAAc,GACdC,EAAQ,OACH,CACL9U,GAAYiU,EAAY5U,EAAO,aAAY,GAAMuV,EACjD,MAAMM,EAAqB,KAAK,IAAIjB,EAAY5U,EAAO,aAAY,CAAE,EAAI,EACnE8V,EAAe,KAAK,IAAIlB,EAAY5U,EAAO,aAAY,CAAE,EAAI,EACnEwV,EAAcK,GAAsBlV,GAAY,EAChD8U,EAAQK,GAAgBnV,GAAY,EAChCkV,IAAoBlV,EAAW,GAC/BmV,IAAcnV,EAAW,EAC/B,CACA,GAAIkC,EAAO,KAAM,CACf,MAAMkT,EAAkB/V,EAAO,oBAAoB,CAAC,EAC9CgW,EAAiBhW,EAAO,oBAAoBA,EAAO,OAAO,OAAS,CAAC,EACpEiW,EAAsBjW,EAAO,WAAW+V,CAAe,EACvDG,EAAqBlW,EAAO,WAAWgW,CAAc,EACrDG,EAAenW,EAAO,WAAWA,EAAO,WAAW,OAAS,CAAC,EAC7DoW,EAAe,KAAK,IAAIxB,CAAS,EACnCwB,GAAgBH,EAClBP,GAAgBU,EAAeH,GAAuBE,EAEtDT,GAAgBU,EAAeD,EAAeD,GAAsBC,EAElET,EAAe,IAAGA,GAAgB,EACxC,CACA,OAAO,OAAO1V,EAAQ,CACpB,SAAAW,EACA,aAAA+U,EACA,YAAAF,EACA,MAAAC,CACJ,CAAG,GACG5S,EAAO,qBAAuBA,EAAO,gBAAkBA,EAAO,aAAY7C,EAAO,qBAAqB4U,CAAS,EAC/GY,GAAe,CAACG,GAClB3V,EAAO,KAAK,uBAAuB,EAEjCyV,GAAS,CAACG,GACZ5V,EAAO,KAAK,iBAAiB,GAE3B2V,GAAgB,CAACH,GAAeI,GAAU,CAACH,IAC7CzV,EAAO,KAAK,UAAU,EAExBA,EAAO,KAAK,WAAYW,CAAQ,CAClC,CAEA,MAAM0V,GAAqB,CAAC1N,EAAS+L,EAAWtJ,IAAc,CACxDsJ,GAAa,CAAC/L,EAAQ,UAAU,SAASyC,CAAS,EACpDzC,EAAQ,UAAU,IAAIyC,CAAS,EACtB,CAACsJ,GAAa/L,EAAQ,UAAU,SAASyC,CAAS,GAC3DzC,EAAQ,UAAU,OAAOyC,CAAS,CAEtC,EACA,SAASkL,IAAsB,CAC7B,MAAMtW,EAAS,KACT,CACJ,OAAA2R,EACA,OAAA9O,EACA,SAAAyO,EACA,YAAAiF,CACJ,EAAMvW,EACEyR,EAAYzR,EAAO,SAAW6C,EAAO,QAAQ,QAC7CyP,EAActS,EAAO,MAAQ6C,EAAO,MAAQA,EAAO,KAAK,KAAO,EAC/D2T,EAAmBhb,GAChBsF,EAAgBwQ,EAAU,IAAIzO,EAAO,UAAU,GAAGrH,CAAQ,iBAAiBA,CAAQ,EAAE,EAAE,CAAC,EAEjG,IAAIib,EACAC,EACAC,EACJ,GAAIlF,EACF,GAAI5O,EAAO,KAAM,CACf,IAAI0J,EAAagK,EAAcvW,EAAO,QAAQ,aAC1CuM,EAAa,IAAGA,EAAavM,EAAO,QAAQ,OAAO,OAASuM,GAC5DA,GAAcvM,EAAO,QAAQ,OAAO,SAAQuM,GAAcvM,EAAO,QAAQ,OAAO,QACpFyW,EAAcD,EAAiB,6BAA6BjK,CAAU,IAAI,CAC5E,MACEkK,EAAcD,EAAiB,6BAA6BD,CAAW,IAAI,OAGzEjE,GACFmE,EAAc9E,EAAO,KAAKhJ,GAAWA,EAAQ,SAAW4N,CAAW,EACnEI,EAAYhF,EAAO,KAAKhJ,GAAWA,EAAQ,SAAW4N,EAAc,CAAC,EACrEG,EAAY/E,EAAO,KAAKhJ,GAAWA,EAAQ,SAAW4N,EAAc,CAAC,GAErEE,EAAc9E,EAAO4E,CAAW,EAGhCE,IACGnE,IAEHqE,EAAY/U,GAAe6U,EAAa,IAAI5T,EAAO,UAAU,gBAAgB,EAAE,CAAC,EAC5EA,EAAO,MAAQ,CAAC8T,IAClBA,EAAYhF,EAAO,CAAC,GAItB+E,EAAYjV,GAAegV,EAAa,IAAI5T,EAAO,UAAU,gBAAgB,EAAE,CAAC,EAC5EA,EAAO,MAAQ,CAAC6T,IAAc,IAChCA,EAAY/E,EAAOA,EAAO,OAAS,CAAC,KAI1CA,EAAO,QAAQhJ,GAAW,CACxB0N,GAAmB1N,EAASA,IAAY8N,EAAa5T,EAAO,gBAAgB,EAC5EwT,GAAmB1N,EAASA,IAAYgO,EAAW9T,EAAO,cAAc,EACxEwT,GAAmB1N,EAASA,IAAY+N,EAAW7T,EAAO,cAAc,CAC1E,CAAC,EACD7C,EAAO,kBAAiB,CAC1B,CAEA,MAAM4W,GAAuB,CAAC5W,EAAQ6W,IAAY,CAChD,GAAI,CAAC7W,GAAUA,EAAO,WAAa,CAACA,EAAO,OAAQ,OACnD,MAAM8W,EAAgB,IAAM9W,EAAO,UAAY,eAAiB,IAAIA,EAAO,OAAO,UAAU,GACtF2I,EAAUkO,EAAQ,QAAQC,EAAa,CAAE,EAC/C,GAAInO,EAAS,CACX,IAAIoO,EAASpO,EAAQ,cAAc,IAAI3I,EAAO,OAAO,kBAAkB,EAAE,EACrE,CAAC+W,GAAU/W,EAAO,YAChB2I,EAAQ,WACVoO,EAASpO,EAAQ,WAAW,cAAc,IAAI3I,EAAO,OAAO,kBAAkB,EAAE,EAGhF,sBAAsB,IAAM,CACtB2I,EAAQ,aACVoO,EAASpO,EAAQ,WAAW,cAAc,IAAI3I,EAAO,OAAO,kBAAkB,EAAE,EAC5E+W,GAAQA,EAAO,OAAM,EAE7B,CAAC,GAGDA,GAAQA,EAAO,OAAM,CAC3B,CACF,EACMC,GAAS,CAAChX,EAAQyF,IAAU,CAChC,GAAI,CAACzF,EAAO,OAAOyF,CAAK,EAAG,OAC3B,MAAMoR,EAAU7W,EAAO,OAAOyF,CAAK,EAAE,cAAc,kBAAkB,EACjEoR,GAASA,EAAQ,gBAAgB,SAAS,CAChD,EACMI,GAAUjX,GAAU,CACxB,GAAI,CAACA,GAAUA,EAAO,WAAa,CAACA,EAAO,OAAQ,OACnD,IAAIkX,EAASlX,EAAO,OAAO,oBAC3B,MAAMR,EAAMQ,EAAO,OAAO,OAC1B,GAAI,CAACR,GAAO,CAAC0X,GAAUA,EAAS,EAAG,OACnCA,EAAS,KAAK,IAAIA,EAAQ1X,CAAG,EAC7B,MAAM2X,EAAgBnX,EAAO,OAAO,gBAAkB,OAASA,EAAO,qBAAoB,EAAK,KAAK,KAAKA,EAAO,OAAO,aAAa,EAC9HuW,EAAcvW,EAAO,YAC3B,GAAIA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAAG,CACrD,MAAMoX,EAAeb,EACfc,EAAiB,CAACD,EAAeF,CAAM,EAC7CG,EAAe,KAAK,GAAG,MAAM,KAAK,CAChC,OAAQH,CACd,CAAK,EAAE,IAAI,CAAC3D,EAAGnU,IACFgY,EAAeD,EAAgB/X,CACvC,CAAC,EACFY,EAAO,OAAO,QAAQ,CAAC2I,EAASvJ,IAAM,CAChCiY,EAAe,SAAS1O,EAAQ,MAAM,GAAGqO,GAAOhX,EAAQZ,CAAC,CAC/D,CAAC,EACD,MACF,CACA,MAAMkY,EAAuBf,EAAcY,EAAgB,EAC3D,GAAInX,EAAO,OAAO,QAAUA,EAAO,OAAO,KACxC,QAASZ,EAAImX,EAAcW,EAAQ9X,GAAKkY,EAAuBJ,EAAQ9X,GAAK,EAAG,CAC7E,MAAMmY,GAAanY,EAAII,EAAMA,GAAOA,GAChC+X,EAAYhB,GAAegB,EAAYD,IAAsBN,GAAOhX,EAAQuX,CAAS,CAC3F,KAEA,SAASnY,EAAI,KAAK,IAAImX,EAAcW,EAAQ,CAAC,EAAG9X,GAAK,KAAK,IAAIkY,EAAuBJ,EAAQ1X,EAAM,CAAC,EAAGJ,GAAK,EACtGA,IAAMmX,IAAgBnX,EAAIkY,GAAwBlY,EAAImX,IACxDS,GAAOhX,EAAQZ,CAAC,CAIxB,EAEA,SAASoY,GAA0BxX,EAAQ,CACzC,KAAM,CACJ,WAAA6R,EACA,OAAAhP,CACJ,EAAM7C,EACE4U,EAAY5U,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UACnE,IAAIuW,EACJ,QAASnX,EAAI,EAAGA,EAAIyS,EAAW,OAAQzS,GAAK,EACtC,OAAOyS,EAAWzS,EAAI,CAAC,EAAM,IAC3BwV,GAAa/C,EAAWzS,CAAC,GAAKwV,EAAY/C,EAAWzS,EAAI,CAAC,GAAKyS,EAAWzS,EAAI,CAAC,EAAIyS,EAAWzS,CAAC,GAAK,EACtGmX,EAAcnX,EACLwV,GAAa/C,EAAWzS,CAAC,GAAKwV,EAAY/C,EAAWzS,EAAI,CAAC,IACnEmX,EAAcnX,EAAI,GAEXwV,GAAa/C,EAAWzS,CAAC,IAClCmX,EAAcnX,GAIlB,OAAIyD,EAAO,sBACL0T,EAAc,GAAK,OAAOA,EAAgB,OAAaA,EAAc,GAEpEA,CACT,CACA,SAASkB,GAAkBC,EAAgB,CACzC,MAAM1X,EAAS,KACT4U,EAAY5U,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UAC7D,CACJ,SAAA4R,EACA,OAAA/O,EACA,YAAa+C,EACb,UAAW+R,EACX,UAAWC,CACf,EAAM5X,EACJ,IAAIuW,EAAcmB,EACd5D,EACJ,MAAM+D,EAAsBC,GAAU,CACpC,IAAIP,EAAYO,EAAS9X,EAAO,QAAQ,aACxC,OAAIuX,EAAY,IACdA,EAAYvX,EAAO,QAAQ,OAAO,OAASuX,GAEzCA,GAAavX,EAAO,QAAQ,OAAO,SACrCuX,GAAavX,EAAO,QAAQ,OAAO,QAE9BuX,CACT,EAIA,GAHI,OAAOhB,EAAgB,MACzBA,EAAciB,GAA0BxX,CAAM,GAE5C4R,EAAS,QAAQgD,CAAS,GAAK,EACjCd,EAAYlC,EAAS,QAAQgD,CAAS,MACjC,CACL,MAAMmD,EAAO,KAAK,IAAIlV,EAAO,mBAAoB0T,CAAW,EAC5DzC,EAAYiE,EAAO,KAAK,OAAOxB,EAAcwB,GAAQlV,EAAO,cAAc,CAC5E,CAEA,GADIiR,GAAalC,EAAS,SAAQkC,EAAYlC,EAAS,OAAS,GAC5D2E,IAAgB3Q,GAAiB,CAAC5F,EAAO,OAAO,KAAM,CACpD8T,IAAc8D,IAChB5X,EAAO,UAAY8T,EACnB9T,EAAO,KAAK,iBAAiB,GAE/B,MACF,CACA,GAAIuW,IAAgB3Q,GAAiB5F,EAAO,OAAO,MAAQA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,CAC1GA,EAAO,UAAY6X,EAAoBtB,CAAW,EAClD,MACF,CACA,MAAMjE,EAActS,EAAO,MAAQ6C,EAAO,MAAQA,EAAO,KAAK,KAAO,EAGrE,IAAI0U,EACJ,GAAIvX,EAAO,SAAW6C,EAAO,QAAQ,SAAWA,EAAO,KACrD0U,EAAYM,EAAoBtB,CAAW,UAClCjE,EAAa,CACtB,MAAM0F,EAAqBhY,EAAO,OAAO,KAAK2I,GAAWA,EAAQ,SAAW4N,CAAW,EACvF,IAAI0B,EAAmB,SAASD,EAAmB,aAAa,yBAAyB,EAAG,EAAE,EAC1F,OAAO,MAAMC,CAAgB,IAC/BA,EAAmB,KAAK,IAAIjY,EAAO,OAAO,QAAQgY,CAAkB,EAAG,CAAC,GAE1ET,EAAY,KAAK,MAAMU,EAAmBpV,EAAO,KAAK,IAAI,CAC5D,SAAW7C,EAAO,OAAOuW,CAAW,EAAG,CACrC,MAAMhK,EAAavM,EAAO,OAAOuW,CAAW,EAAE,aAAa,yBAAyB,EAChFhK,EACFgL,EAAY,SAAShL,EAAY,EAAE,EAEnCgL,EAAYhB,CAEhB,MACEgB,EAAYhB,EAEd,OAAO,OAAOvW,EAAQ,CACpB,kBAAA4X,EACA,UAAA9D,EACA,kBAAA6D,EACA,UAAAJ,EACA,cAAA3R,EACA,YAAA2Q,CACJ,CAAG,EACGvW,EAAO,aACTiX,GAAQjX,CAAM,EAEhBA,EAAO,KAAK,mBAAmB,EAC/BA,EAAO,KAAK,iBAAiB,GACzBA,EAAO,aAAeA,EAAO,OAAO,sBAClC2X,IAAsBJ,GACxBvX,EAAO,KAAK,iBAAiB,EAE/BA,EAAO,KAAK,aAAa,EAE7B,CAEA,SAASkY,GAAmB3Z,EAAI+F,EAAM,CACpC,MAAMtE,EAAS,KACT6C,EAAS7C,EAAO,OACtB,IAAIyL,EAAQlN,EAAG,QAAQ,IAAIsE,EAAO,UAAU,gBAAgB,EACxD,CAAC4I,GAASzL,EAAO,WAAasE,GAAQA,EAAK,OAAS,GAAKA,EAAK,SAAS/F,CAAE,GAC3E,CAAC,GAAG+F,EAAK,MAAMA,EAAK,QAAQ/F,CAAE,EAAI,EAAG+F,EAAK,MAAM,CAAC,EAAE,QAAQC,GAAU,CAC/D,CAACkH,GAASlH,EAAO,SAAWA,EAAO,QAAQ,IAAI1B,EAAO,UAAU,gBAAgB,IAClF4I,EAAQlH,EAEZ,CAAC,EAEH,IAAI4T,EAAa,GACb5L,EACJ,GAAId,GACF,QAASrM,EAAI,EAAGA,EAAIY,EAAO,OAAO,OAAQZ,GAAK,EAC7C,GAAIY,EAAO,OAAOZ,CAAC,IAAMqM,EAAO,CAC9B0M,EAAa,GACb5L,EAAanN,EACb,KACF,EAGJ,GAAIqM,GAAS0M,EACXnY,EAAO,aAAeyL,EAClBzL,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAC1CA,EAAO,aAAe,SAASyL,EAAM,aAAa,yBAAyB,EAAG,EAAE,EAEhFzL,EAAO,aAAeuM,MAEnB,CACLvM,EAAO,aAAe,OACtBA,EAAO,aAAe,OACtB,MACF,CACI6C,EAAO,qBAAuB7C,EAAO,eAAiB,QAAaA,EAAO,eAAiBA,EAAO,aACpGA,EAAO,oBAAmB,CAE9B,CAEA,IAAIwD,GAAS,CACX,WAAAyN,GACA,aAAAC,GACA,iBAAAkD,GACA,mBAAAG,GACA,qBAAAI,GACA,eAAAU,GACA,oBAAAiB,GACA,kBAAAmB,GACA,mBAAAS,EACF,EAEA,SAASE,GAAmBzZ,EAAM,CAC5BA,IAAS,SACXA,EAAO,KAAK,aAAY,EAAK,IAAM,KAErC,MAAMqB,EAAS,KACT,CACJ,OAAA6C,EACA,aAAc8C,EACd,UAAAiP,EACA,UAAAvD,CACJ,EAAMrR,EACJ,GAAI6C,EAAO,iBACT,OAAO8C,EAAM,CAACiP,EAAYA,EAE5B,GAAI/R,EAAO,QACT,OAAO+R,EAET,IAAIyD,EAAmB3Z,GAAa2S,EAAW1S,CAAI,EACnD,OAAA0Z,GAAoBrY,EAAO,sBAAqB,EAC5C2F,IAAK0S,EAAmB,CAACA,GACtBA,GAAoB,CAC7B,CAEA,SAASC,GAAa1D,EAAW2D,EAAc,CAC7C,MAAMvY,EAAS,KACT,CACJ,aAAc2F,EACd,OAAA9C,EACA,UAAAwO,EACA,SAAA1Q,CACJ,EAAMX,EACJ,IAAIwY,EAAI,EACJC,EAAI,EACR,MAAMC,EAAI,EACN1Y,EAAO,eACTwY,EAAI7S,EAAM,CAACiP,EAAYA,EAEvB6D,EAAI7D,EAEF/R,EAAO,eACT2V,EAAI,KAAK,MAAMA,CAAC,EAChBC,EAAI,KAAK,MAAMA,CAAC,GAElBzY,EAAO,kBAAoBA,EAAO,UAClCA,EAAO,UAAYA,EAAO,aAAY,EAAKwY,EAAIC,EAC3C5V,EAAO,QACTwO,EAAUrR,EAAO,aAAY,EAAK,aAAe,WAAW,EAAIA,EAAO,aAAY,EAAK,CAACwY,EAAI,CAACC,EACpF5V,EAAO,mBACb7C,EAAO,eACTwY,GAAKxY,EAAO,sBAAqB,EAEjCyY,GAAKzY,EAAO,sBAAqB,EAEnCqR,EAAU,MAAM,UAAY,eAAemH,CAAC,OAAOC,CAAC,OAAOC,CAAC,OAI9D,IAAIC,EACJ,MAAMpD,EAAiBvV,EAAO,aAAY,EAAKA,EAAO,aAAY,EAC9DuV,IAAmB,EACrBoD,EAAc,EAEdA,GAAe/D,EAAY5U,EAAO,aAAY,GAAMuV,EAElDoD,IAAgBhY,GAClBX,EAAO,eAAe4U,CAAS,EAEjC5U,EAAO,KAAK,eAAgBA,EAAO,UAAWuY,CAAY,CAC5D,CAEA,SAASK,IAAe,CACtB,MAAO,CAAC,KAAK,SAAS,CAAC,CACzB,CAEA,SAASC,IAAe,CACtB,MAAO,CAAC,KAAK,SAAS,KAAK,SAAS,OAAS,CAAC,CAChD,CAEA,SAASC,GAAYlE,EAAW7L,EAAOgQ,EAAcC,EAAiB5P,EAAU,CAC1EwL,IAAc,SAChBA,EAAY,GAEV7L,IAAU,SACZA,EAAQ,KAAK,OAAO,OAElBgQ,IAAiB,SACnBA,EAAe,IAEbC,IAAoB,SACtBA,EAAkB,IAEpB,MAAMhZ,EAAS,KACT,CACJ,OAAA6C,EACA,UAAAwO,CACJ,EAAMrR,EACJ,GAAIA,EAAO,WAAa6C,EAAO,+BAC7B,MAAO,GAET,MAAM+V,EAAe5Y,EAAO,aAAY,EAClC6Y,EAAe7Y,EAAO,aAAY,EACxC,IAAIiZ,EAKJ,GAJID,GAAmBpE,EAAYgE,EAAcK,EAAeL,EAAsBI,GAAmBpE,EAAYiE,EAAcI,EAAeJ,EAAkBI,EAAerE,EAGnL5U,EAAO,eAAeiZ,CAAY,EAC9BpW,EAAO,QAAS,CAClB,MAAMqW,EAAMlZ,EAAO,aAAY,EAC/B,GAAI+I,IAAU,EACZsI,EAAU6H,EAAM,aAAe,WAAW,EAAI,CAACD,MAC1C,CACL,GAAI,CAACjZ,EAAO,QAAQ,aAClB,OAAAF,GAAqB,CACnB,OAAAE,EACA,eAAgB,CAACiZ,EACjB,KAAMC,EAAM,OAAS,KAC/B,CAAS,EACM,GAET7H,EAAU,SAAS,CACjB,CAAC6H,EAAM,OAAS,KAAK,EAAG,CAACD,EACzB,SAAU,QAClB,CAAO,CACH,CACA,MAAO,EACT,CACA,OAAIlQ,IAAU,GACZ/I,EAAO,cAAc,CAAC,EACtBA,EAAO,aAAaiZ,CAAY,EAC5BF,IACF/Y,EAAO,KAAK,wBAAyB+I,EAAOK,CAAQ,EACpDpJ,EAAO,KAAK,eAAe,KAG7BA,EAAO,cAAc+I,CAAK,EAC1B/I,EAAO,aAAaiZ,CAAY,EAC5BF,IACF/Y,EAAO,KAAK,wBAAyB+I,EAAOK,CAAQ,EACpDpJ,EAAO,KAAK,iBAAiB,GAE1BA,EAAO,YACVA,EAAO,UAAY,GACdA,EAAO,oCACVA,EAAO,kCAAoC,SAAuB4D,EAAG,CAC/D,CAAC5D,GAAUA,EAAO,WAClB4D,EAAE,SAAW,OACjB5D,EAAO,UAAU,oBAAoB,gBAAiBA,EAAO,iCAAiC,EAC9FA,EAAO,kCAAoC,KAC3C,OAAOA,EAAO,kCACdA,EAAO,UAAY,GACf+Y,GACF/Y,EAAO,KAAK,eAAe,EAE/B,GAEFA,EAAO,UAAU,iBAAiB,gBAAiBA,EAAO,iCAAiC,IAGxF,EACT,CAEA,IAAI4U,GAAY,CACd,aAAcwD,GACd,aAAAE,GACA,aAAAM,GACA,aAAAC,GACA,YAAAC,EACF,EAEA,SAASK,GAAc7Y,EAAUiY,EAAc,CAC7C,MAAMvY,EAAS,KACVA,EAAO,OAAO,UACjBA,EAAO,UAAU,MAAM,mBAAqB,GAAGM,CAAQ,KACvDN,EAAO,UAAU,MAAM,gBAAkBM,IAAa,EAAI,MAAQ,IAEpEN,EAAO,KAAK,gBAAiBM,EAAUiY,CAAY,CACrD,CAEA,SAASa,GAAerZ,EAAM,CAC5B,GAAI,CACF,OAAAC,EACA,aAAA+Y,EACA,UAAAM,EACA,KAAAC,CACJ,EAAMvZ,EACJ,KAAM,CACJ,YAAAwW,EACA,cAAA3Q,CACJ,EAAM5F,EACJ,IAAIO,EAAM8Y,EACL9Y,IACCgW,EAAc3Q,EAAerF,EAAM,OAAgBgW,EAAc3Q,EAAerF,EAAM,OAAYA,EAAM,SAE9GP,EAAO,KAAK,aAAasZ,CAAI,EAAE,EAC3BP,GAAgBxY,IAAQ,QAC1BP,EAAO,KAAK,uBAAuBsZ,CAAI,EAAE,EAChCP,GAAgBxC,IAAgB3Q,IACzC5F,EAAO,KAAK,wBAAwBsZ,CAAI,EAAE,EACtC/Y,IAAQ,OACVP,EAAO,KAAK,sBAAsBsZ,CAAI,EAAE,EAExCtZ,EAAO,KAAK,sBAAsBsZ,CAAI,EAAE,EAG9C,CAEA,SAASC,GAAgBR,EAAcM,EAAW,CAC5CN,IAAiB,SACnBA,EAAe,IAEjB,MAAM/Y,EAAS,KACT,CACJ,OAAA6C,CACJ,EAAM7C,EACA6C,EAAO,UACPA,EAAO,YACT7C,EAAO,iBAAgB,EAEzBoZ,GAAe,CACb,OAAApZ,EACA,aAAA+Y,EACA,UAAAM,EACA,KAAM,OACV,CAAG,EACH,CAEA,SAASG,GAAcT,EAAcM,EAAW,CAC1CN,IAAiB,SACnBA,EAAe,IAEjB,MAAM/Y,EAAS,KACT,CACJ,OAAA6C,CACJ,EAAM7C,EACJA,EAAO,UAAY,GACf,CAAA6C,EAAO,UACX7C,EAAO,cAAc,CAAC,EACtBoZ,GAAe,CACb,OAAApZ,EACA,aAAA+Y,EACA,UAAAM,EACA,KAAM,KACV,CAAG,EACH,CAEA,IAAII,GAAa,CACf,cAAAN,GACA,gBAAAI,GACA,cAAAC,EACF,EAEA,SAASE,GAAQjU,EAAOsD,EAAOgQ,EAAc3P,EAAUuQ,EAAS,CAC1DlU,IAAU,SACZA,EAAQ,GAENsT,IAAiB,SACnBA,EAAe,IAEb,OAAOtT,GAAU,WACnBA,EAAQ,SAASA,EAAO,EAAE,GAE5B,MAAMzF,EAAS,KACf,IAAIuM,EAAa9G,EACb8G,EAAa,IAAGA,EAAa,GACjC,KAAM,CACJ,OAAA1J,EACA,SAAA+O,EACA,WAAAC,EACA,cAAAjM,EACA,YAAA2Q,EACA,aAAc5Q,EACd,UAAA0L,EACA,QAAAuI,CACJ,EAAM5Z,EACJ,GAAI,CAAC4Z,GAAW,CAACxQ,GAAY,CAACuQ,GAAW3Z,EAAO,WAAaA,EAAO,WAAa6C,EAAO,+BACtF,MAAO,GAEL,OAAOkG,EAAU,MACnBA,EAAQ/I,EAAO,OAAO,OAExB,MAAM+X,EAAO,KAAK,IAAI/X,EAAO,OAAO,mBAAoBuM,CAAU,EAClE,IAAIuH,EAAYiE,EAAO,KAAK,OAAOxL,EAAawL,GAAQ/X,EAAO,OAAO,cAAc,EAChF8T,GAAalC,EAAS,SAAQkC,EAAYlC,EAAS,OAAS,GAChE,MAAMgD,EAAY,CAAChD,EAASkC,CAAS,EAErC,GAAIjR,EAAO,oBACT,QAASzD,EAAI,EAAGA,EAAIyS,EAAW,OAAQzS,GAAK,EAAG,CAC7C,MAAMya,EAAsB,CAAC,KAAK,MAAMjF,EAAY,GAAG,EACjDkF,EAAiB,KAAK,MAAMjI,EAAWzS,CAAC,EAAI,GAAG,EAC/C2a,EAAqB,KAAK,MAAMlI,EAAWzS,EAAI,CAAC,EAAI,GAAG,EACzD,OAAOyS,EAAWzS,EAAI,CAAC,EAAM,IAC3Bya,GAAuBC,GAAkBD,EAAsBE,GAAsBA,EAAqBD,GAAkB,EAC9HvN,EAAanN,EACJya,GAAuBC,GAAkBD,EAAsBE,IACxExN,EAAanN,EAAI,GAEVya,GAAuBC,IAChCvN,EAAanN,EAEjB,CAGF,GAAIY,EAAO,aAAeuM,IAAegK,IACnC,CAACvW,EAAO,iBAAmB2F,EAAMiP,EAAY5U,EAAO,WAAa4U,EAAY5U,EAAO,aAAY,EAAK4U,EAAY5U,EAAO,WAAa4U,EAAY5U,EAAO,aAAY,IAGpK,CAACA,EAAO,gBAAkB4U,EAAY5U,EAAO,WAAa4U,EAAY5U,EAAO,iBAC1EuW,GAAe,KAAOhK,GACzB,MAAO,GAITA,KAAgB3G,GAAiB,IAAMmT,GACzC/Y,EAAO,KAAK,wBAAwB,EAItCA,EAAO,eAAe4U,CAAS,EAC/B,IAAIyE,EACA9M,EAAagK,EAAa8C,EAAY,OAAgB9M,EAAagK,EAAa8C,EAAY,OAAYA,EAAY,QAGxH,MAAM5H,EAAYzR,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAG1D,GAAI,EAFqByR,GAAakI,KAEZhU,GAAO,CAACiP,IAAc5U,EAAO,WAAa,CAAC2F,GAAOiP,IAAc5U,EAAO,WAC/F,OAAAA,EAAO,kBAAkBuM,CAAU,EAE/B1J,EAAO,YACT7C,EAAO,iBAAgB,EAEzBA,EAAO,oBAAmB,EACtB6C,EAAO,SAAW,SACpB7C,EAAO,aAAa4U,CAAS,EAE3ByE,IAAc,UAChBrZ,EAAO,gBAAgB+Y,EAAcM,CAAS,EAC9CrZ,EAAO,cAAc+Y,EAAcM,CAAS,GAEvC,GAET,GAAIxW,EAAO,QAAS,CAClB,MAAMqW,EAAMlZ,EAAO,aAAY,EACzBga,EAAIrU,EAAMiP,EAAY,CAACA,EAC7B,GAAI7L,IAAU,EACR0I,IACFzR,EAAO,UAAU,MAAM,eAAiB,OACxCA,EAAO,kBAAoB,IAEzByR,GAAa,CAACzR,EAAO,2BAA6BA,EAAO,OAAO,aAAe,GACjFA,EAAO,0BAA4B,GACnC,sBAAsB,IAAM,CAC1BqR,EAAU6H,EAAM,aAAe,WAAW,EAAIc,CAChD,CAAC,GAED3I,EAAU6H,EAAM,aAAe,WAAW,EAAIc,EAE5CvI,GACF,sBAAsB,IAAM,CAC1BzR,EAAO,UAAU,MAAM,eAAiB,GACxCA,EAAO,kBAAoB,EAC7B,CAAC,MAEE,CACL,GAAI,CAACA,EAAO,QAAQ,aAClB,OAAAF,GAAqB,CACnB,OAAAE,EACA,eAAgBga,EAChB,KAAMd,EAAM,OAAS,KAC/B,CAAS,EACM,GAET7H,EAAU,SAAS,CACjB,CAAC6H,EAAM,OAAS,KAAK,EAAGc,EACxB,SAAU,QAClB,CAAO,CACH,CACA,MAAO,EACT,CAEA,MAAM3L,EADUO,GAAU,EACD,SACzB,OAAI6C,GAAa,CAACkI,GAAWtL,GAAYrO,EAAO,WAC9CA,EAAO,QAAQ,OAAO,GAAO,GAAOuM,CAAU,EAEhDvM,EAAO,cAAc+I,CAAK,EAC1B/I,EAAO,aAAa4U,CAAS,EAC7B5U,EAAO,kBAAkBuM,CAAU,EACnCvM,EAAO,oBAAmB,EAC1BA,EAAO,KAAK,wBAAyB+I,EAAOK,CAAQ,EACpDpJ,EAAO,gBAAgB+Y,EAAcM,CAAS,EAC1CtQ,IAAU,EACZ/I,EAAO,cAAc+Y,EAAcM,CAAS,EAClCrZ,EAAO,YACjBA,EAAO,UAAY,GACdA,EAAO,gCACVA,EAAO,8BAAgC,SAAuB4D,EAAG,CAC3D,CAAC5D,GAAUA,EAAO,WAClB4D,EAAE,SAAW,OACjB5D,EAAO,UAAU,oBAAoB,gBAAiBA,EAAO,6BAA6B,EAC1FA,EAAO,8BAAgC,KACvC,OAAOA,EAAO,8BACdA,EAAO,cAAc+Y,EAAcM,CAAS,EAC9C,GAEFrZ,EAAO,UAAU,iBAAiB,gBAAiBA,EAAO,6BAA6B,GAElF,EACT,CAEA,SAASia,GAAYxU,EAAOsD,EAAOgQ,EAAc3P,EAAU,CACrD3D,IAAU,SACZA,EAAQ,GAENsT,IAAiB,SACnBA,EAAe,IAEb,OAAOtT,GAAU,WAEnBA,EADsB,SAASA,EAAO,EAAE,GAG1C,MAAMzF,EAAS,KACf,GAAIA,EAAO,UAAW,OAClB,OAAO+I,EAAU,MACnBA,EAAQ/I,EAAO,OAAO,OAExB,MAAMsS,EAActS,EAAO,MAAQA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EACnF,IAAIka,EAAWzU,EACf,GAAIzF,EAAO,OAAO,KAChB,GAAIA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAE1Cka,EAAWA,EAAWla,EAAO,QAAQ,iBAChC,CACL,IAAIma,EACJ,GAAI7H,EAAa,CACf,MAAM/F,EAAa2N,EAAWla,EAAO,OAAO,KAAK,KACjDma,EAAmBna,EAAO,OAAO,KAAK2I,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAM4D,CAAU,EAAE,MACvH,MACE4N,EAAmBna,EAAO,oBAAoBka,CAAQ,EAExD,MAAME,EAAO9H,EAAc,KAAK,KAAKtS,EAAO,OAAO,OAASA,EAAO,OAAO,KAAK,IAAI,EAAIA,EAAO,OAAO,OAC/F,CACJ,eAAAqa,CACR,EAAUra,EAAO,OACX,IAAImX,EAAgBnX,EAAO,OAAO,cAC9BmX,IAAkB,OACpBA,EAAgBnX,EAAO,qBAAoB,GAE3CmX,EAAgB,KAAK,KAAK,WAAWnX,EAAO,OAAO,cAAe,EAAE,CAAC,EACjEqa,GAAkBlD,EAAgB,IAAM,IAC1CA,EAAgBA,EAAgB,IAGpC,IAAImD,EAAcF,EAAOD,EAAmBhD,EAO5C,GANIkD,IACFC,EAAcA,GAAeH,EAAmB,KAAK,KAAKhD,EAAgB,CAAC,GAEzE/N,GAAYiR,GAAkBra,EAAO,OAAO,gBAAkB,QAAU,CAACsS,IAC3EgI,EAAc,IAEZA,EAAa,CACf,MAAMjB,EAAYgB,EAAiBF,EAAmBna,EAAO,YAAc,OAAS,OAASma,EAAmBna,EAAO,YAAc,EAAIA,EAAO,OAAO,cAAgB,OAAS,OAChLA,EAAO,QAAQ,CACb,UAAAqZ,EACA,QAAS,GACT,iBAAkBA,IAAc,OAASc,EAAmB,EAAIA,EAAmBC,EAAO,EAC1F,eAAgBf,IAAc,OAASrZ,EAAO,UAAY,MACpE,CAAS,CACH,CACA,GAAIsS,EAAa,CACf,MAAM/F,EAAa2N,EAAWla,EAAO,OAAO,KAAK,KACjDka,EAAWla,EAAO,OAAO,KAAK2I,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAM4D,CAAU,EAAE,MAC/G,MACE2N,EAAWla,EAAO,oBAAoBka,CAAQ,CAElD,CAEF,6BAAsB,IAAM,CAC1Bla,EAAO,QAAQka,EAAUnR,EAAOgQ,EAAc3P,CAAQ,CACxD,CAAC,EACMpJ,CACT,CAGA,SAASua,GAAUxR,EAAOgQ,EAAc3P,EAAU,CAC5C2P,IAAiB,SACnBA,EAAe,IAEjB,MAAM/Y,EAAS,KACT,CACJ,QAAA4Z,EACA,OAAA/W,EACA,UAAA2X,CACJ,EAAMxa,EACJ,GAAI,CAAC4Z,GAAW5Z,EAAO,UAAW,OAAOA,EACrC,OAAO+I,EAAU,MACnBA,EAAQ/I,EAAO,OAAO,OAExB,IAAIya,EAAW5X,EAAO,eAClBA,EAAO,gBAAkB,QAAUA,EAAO,iBAAmB,GAAKA,EAAO,qBAC3E4X,EAAW,KAAK,IAAIza,EAAO,qBAAqB,UAAW,EAAI,EAAG,CAAC,GAErE,MAAM0a,EAAY1a,EAAO,YAAc6C,EAAO,mBAAqB,EAAI4X,EACjEhJ,EAAYzR,EAAO,SAAW6C,EAAO,QAAQ,QACnD,GAAIA,EAAO,KAAM,CACf,GAAI2X,GAAa,CAAC/I,GAAa5O,EAAO,oBAAqB,MAAO,GAMlE,GALA7C,EAAO,QAAQ,CACb,UAAW,MACjB,CAAK,EAEDA,EAAO,YAAcA,EAAO,UAAU,WAClCA,EAAO,cAAgBA,EAAO,OAAO,OAAS,GAAK6C,EAAO,QAC5D,6BAAsB,IAAM,CAC1B7C,EAAO,QAAQA,EAAO,YAAc0a,EAAW3R,EAAOgQ,EAAc3P,CAAQ,CAC9E,CAAC,EACM,EAEX,CACA,OAAIvG,EAAO,QAAU7C,EAAO,MACnBA,EAAO,QAAQ,EAAG+I,EAAOgQ,EAAc3P,CAAQ,EAEjDpJ,EAAO,QAAQA,EAAO,YAAc0a,EAAW3R,EAAOgQ,EAAc3P,CAAQ,CACrF,CAGA,SAASuR,GAAU5R,EAAOgQ,EAAc3P,EAAU,CAC5C2P,IAAiB,SACnBA,EAAe,IAEjB,MAAM/Y,EAAS,KACT,CACJ,OAAA6C,EACA,SAAA+O,EACA,WAAAC,EACA,aAAA+I,EACA,QAAAhB,EACA,UAAAY,CACJ,EAAMxa,EACJ,GAAI,CAAC4Z,GAAW5Z,EAAO,UAAW,OAAOA,EACrC,OAAO+I,EAAU,MACnBA,EAAQ/I,EAAO,OAAO,OAExB,MAAMyR,EAAYzR,EAAO,SAAW6C,EAAO,QAAQ,QACnD,GAAIA,EAAO,KAAM,CACf,GAAI2X,GAAa,CAAC/I,GAAa5O,EAAO,oBAAqB,MAAO,GAClE7C,EAAO,QAAQ,CACb,UAAW,MACjB,CAAK,EAEDA,EAAO,YAAcA,EAAO,UAAU,UACxC,CACA,MAAM4U,EAAYgG,EAAe5a,EAAO,UAAY,CAACA,EAAO,UAC5D,SAAS6a,EAAUC,EAAK,CACtB,OAAIA,EAAM,EAAU,CAAC,KAAK,MAAM,KAAK,IAAIA,CAAG,CAAC,EACtC,KAAK,MAAMA,CAAG,CACvB,CACA,MAAMjB,EAAsBgB,EAAUjG,CAAS,EACzCmG,EAAqBnJ,EAAS,IAAIkJ,GAAOD,EAAUC,CAAG,CAAC,EACvDE,EAAanY,EAAO,UAAYA,EAAO,SAAS,QACtD,IAAIoY,EAAWrJ,EAASmJ,EAAmB,QAAQlB,CAAmB,EAAI,CAAC,EAC3E,GAAI,OAAOoB,EAAa,MAAgBpY,EAAO,SAAWmY,GAAa,CACrE,IAAIE,EACJtJ,EAAS,QAAQ,CAAC+B,EAAMG,IAAc,CAChC+F,GAAuBlG,IAEzBuH,EAAgBpH,EAEpB,CAAC,EACG,OAAOoH,EAAkB,MAC3BD,EAAWD,EAAapJ,EAASsJ,CAAa,EAAItJ,EAASsJ,EAAgB,EAAIA,EAAgB,EAAIA,CAAa,EAEpH,CACA,IAAI5V,EAAY,EAShB,GARI,OAAO2V,EAAa,MACtB3V,EAAYuM,EAAW,QAAQoJ,CAAQ,EACnC3V,EAAY,IAAGA,EAAYtF,EAAO,YAAc,GAChD6C,EAAO,gBAAkB,QAAUA,EAAO,iBAAmB,GAAKA,EAAO,qBAC3EyC,EAAYA,EAAYtF,EAAO,qBAAqB,WAAY,EAAI,EAAI,EACxEsF,EAAY,KAAK,IAAIA,EAAW,CAAC,IAGjCzC,EAAO,QAAU7C,EAAO,YAAa,CACvC,MAAMiG,EAAYjG,EAAO,OAAO,SAAWA,EAAO,OAAO,QAAQ,SAAWA,EAAO,QAAUA,EAAO,QAAQ,OAAO,OAAS,EAAIA,EAAO,OAAO,OAAS,EACvJ,OAAOA,EAAO,QAAQiG,EAAW8C,EAAOgQ,EAAc3P,CAAQ,CAChE,SAAWvG,EAAO,MAAQ7C,EAAO,cAAgB,GAAK6C,EAAO,QAC3D,6BAAsB,IAAM,CAC1B7C,EAAO,QAAQsF,EAAWyD,EAAOgQ,EAAc3P,CAAQ,CACzD,CAAC,EACM,GAET,OAAOpJ,EAAO,QAAQsF,EAAWyD,EAAOgQ,EAAc3P,CAAQ,CAChE,CAGA,SAAS+R,GAAWpS,EAAOgQ,EAAc3P,EAAU,CAC7C2P,IAAiB,SACnBA,EAAe,IAEjB,MAAM/Y,EAAS,KACf,GAAI,CAAAA,EAAO,UACX,OAAI,OAAO+I,EAAU,MACnBA,EAAQ/I,EAAO,OAAO,OAEjBA,EAAO,QAAQA,EAAO,YAAa+I,EAAOgQ,EAAc3P,CAAQ,CACzE,CAGA,SAASgS,GAAerS,EAAOgQ,EAAc3P,EAAUiS,EAAW,CAC5DtC,IAAiB,SACnBA,EAAe,IAEbsC,IAAc,SAChBA,EAAY,IAEd,MAAMrb,EAAS,KACf,GAAIA,EAAO,UAAW,OAClB,OAAO+I,EAAU,MACnBA,EAAQ/I,EAAO,OAAO,OAExB,IAAIyF,EAAQzF,EAAO,YACnB,MAAM+X,EAAO,KAAK,IAAI/X,EAAO,OAAO,mBAAoByF,CAAK,EACvDqO,EAAYiE,EAAO,KAAK,OAAOtS,EAAQsS,GAAQ/X,EAAO,OAAO,cAAc,EAC3E4U,EAAY5U,EAAO,aAAeA,EAAO,UAAY,CAACA,EAAO,UACnE,GAAI4U,GAAa5U,EAAO,SAAS8T,CAAS,EAAG,CAG3C,MAAMwH,EAActb,EAAO,SAAS8T,CAAS,EACvCyH,EAAWvb,EAAO,SAAS8T,EAAY,CAAC,EAC1Cc,EAAY0G,GAAeC,EAAWD,GAAeD,IACvD5V,GAASzF,EAAO,OAAO,eAE3B,KAAO,CAGL,MAAMib,EAAWjb,EAAO,SAAS8T,EAAY,CAAC,EACxCwH,EAActb,EAAO,SAAS8T,CAAS,EACzCc,EAAYqG,IAAaK,EAAcL,GAAYI,IACrD5V,GAASzF,EAAO,OAAO,eAE3B,CACA,OAAAyF,EAAQ,KAAK,IAAIA,EAAO,CAAC,EACzBA,EAAQ,KAAK,IAAIA,EAAOzF,EAAO,WAAW,OAAS,CAAC,EAC7CA,EAAO,QAAQyF,EAAOsD,EAAOgQ,EAAc3P,CAAQ,CAC5D,CAEA,SAASoS,IAAsB,CAC7B,MAAMxb,EAAS,KACf,GAAIA,EAAO,UAAW,OACtB,KAAM,CACJ,OAAA6C,EACA,SAAAyO,CACJ,EAAMtR,EACEmX,EAAgBtU,EAAO,gBAAkB,OAAS7C,EAAO,qBAAoB,EAAK6C,EAAO,cAC/F,IAAI4Y,EAAezb,EAAO,sBAAsBA,EAAO,YAAY,EAC/DuX,EACJ,MAAMT,EAAgB9W,EAAO,UAAY,eAAiB,IAAI6C,EAAO,UAAU,GACzE6Y,EAAS1b,EAAO,MAAQA,EAAO,OAAO,MAAQA,EAAO,OAAO,KAAK,KAAO,EAC9E,GAAI6C,EAAO,KAAM,CACf,GAAI7C,EAAO,UAAW,OACtBuX,EAAY,SAASvX,EAAO,aAAa,aAAa,yBAAyB,EAAG,EAAE,EAChF6C,EAAO,eACT7C,EAAO,YAAYuX,CAAS,EACnBkE,GAAgBC,GAAU1b,EAAO,OAAO,OAASmX,GAAiB,GAAKnX,EAAO,OAAO,KAAK,KAAO,GAAKA,EAAO,OAAO,OAASmX,IACtInX,EAAO,QAAO,EACdyb,EAAezb,EAAO,cAAcc,EAAgBwQ,EAAU,GAAGwF,CAAa,6BAA6BS,CAAS,IAAI,EAAE,CAAC,CAAC,EAC5HpZ,GAAS,IAAM,CACb6B,EAAO,QAAQyb,CAAY,CAC7B,CAAC,GAEDzb,EAAO,QAAQyb,CAAY,CAE/B,MACEzb,EAAO,QAAQyb,CAAY,CAE/B,CAEA,IAAIhQ,GAAQ,CACV,QAAAiO,GACA,YAAAO,GACA,UAAAM,GACA,UAAAI,GACA,WAAAQ,GACA,eAAAC,GACA,oBAAAI,EACF,EAEA,SAASG,GAAWC,EAAgBjC,EAAS,CAC3C,MAAM3Z,EAAS,KACT,CACJ,OAAA6C,EACA,SAAAyO,CACJ,EAAMtR,EACJ,GAAI,CAAC6C,EAAO,MAAQ7C,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,OACrE,MAAM6b,EAAa,IAAM,CACR/a,EAAgBwQ,EAAU,IAAIzO,EAAO,UAAU,gBAAgB,EACvE,QAAQ,CAACtE,EAAIkH,IAAU,CAC5BlH,EAAG,aAAa,0BAA2BkH,CAAK,CAClD,CAAC,CACH,EACMqW,EAAmB,IAAM,CAC7B,MAAMnK,EAAS7Q,EAAgBwQ,EAAU,IAAIzO,EAAO,eAAe,EAAE,EACrE8O,EAAO,QAAQpT,GAAM,CACnBA,EAAG,OAAM,CACX,CAAC,EACGoT,EAAO,OAAS,IAClB3R,EAAO,aAAY,EACnBA,EAAO,aAAY,EAEvB,EACMsS,EAActS,EAAO,MAAQ6C,EAAO,MAAQA,EAAO,KAAK,KAAO,EACjEA,EAAO,qBAAuBA,EAAO,eAAiB,GAAKyP,IAC7DwJ,EAAgB,EAElB,MAAMC,EAAiBlZ,EAAO,gBAAkByP,EAAczP,EAAO,KAAK,KAAO,GAC3EmZ,EAAkBhc,EAAO,OAAO,OAAS+b,IAAmB,EAC5DE,EAAiB3J,GAAetS,EAAO,OAAO,OAAS6C,EAAO,KAAK,OAAS,EAC5EqZ,EAAiBC,GAAkB,CACvC,QAAS/c,EAAI,EAAGA,EAAI+c,EAAgB/c,GAAK,EAAG,CAC1C,MAAMuJ,EAAU3I,EAAO,UAAYtE,GAAc,eAAgB,CAACmH,EAAO,eAAe,CAAC,EAAInH,GAAc,MAAO,CAACmH,EAAO,WAAYA,EAAO,eAAe,CAAC,EAC7J7C,EAAO,SAAS,OAAO2I,CAAO,CAChC,CACF,EACA,GAAIqT,EAAiB,CACnB,GAAInZ,EAAO,mBAAoB,CAC7B,MAAMuZ,EAAcL,EAAiB/b,EAAO,OAAO,OAAS+b,EAC5DG,EAAeE,CAAW,EAC1Bpc,EAAO,aAAY,EACnBA,EAAO,aAAY,CACrB,MACEsB,GAAY,iLAAiL,EAE/Lua,EAAU,CACZ,SAAWI,EAAgB,CACzB,GAAIpZ,EAAO,mBAAoB,CAC7B,MAAMuZ,EAAcvZ,EAAO,KAAK,KAAO7C,EAAO,OAAO,OAAS6C,EAAO,KAAK,KAC1EqZ,EAAeE,CAAW,EAC1Bpc,EAAO,aAAY,EACnBA,EAAO,aAAY,CACrB,MACEsB,GAAY,4KAA4K,EAE1Lua,EAAU,CACZ,MACEA,EAAU,EAEZ7b,EAAO,QAAQ,CACb,eAAA4b,EACA,UAAW/Y,EAAO,eAAiB,OAAY,OAC/C,QAAA8W,CACJ,CAAG,CACH,CAEA,SAAS0C,GAAQlP,EAAO,CACtB,GAAI,CACF,eAAAyO,EACA,QAAAlC,EAAU,GACV,UAAAL,EACA,aAAAf,EACA,iBAAAL,EACA,QAAA0B,EACA,aAAApB,EACA,aAAA+D,CACJ,EAAMnP,IAAU,OAAS,CAAA,EAAKA,EAC5B,MAAMnN,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,KAAM,OACzBA,EAAO,KAAK,eAAe,EAC3B,KAAM,CACJ,OAAA2R,EACA,eAAA4K,EACA,eAAAC,EACA,SAAAlL,EACA,OAAAzO,CACJ,EAAM7C,EACE,CACJ,eAAAqa,EACA,aAAAoC,CACJ,EAAM5Z,EAGJ,GAFA7C,EAAO,eAAiB,GACxBA,EAAO,eAAiB,GACpBA,EAAO,SAAW6C,EAAO,QAAQ,QAAS,CACxC6W,IACE,CAAC7W,EAAO,gBAAkB7C,EAAO,YAAc,EACjDA,EAAO,QAAQA,EAAO,QAAQ,OAAO,OAAQ,EAAG,GAAO,EAAI,EAClD6C,EAAO,gBAAkB7C,EAAO,UAAY6C,EAAO,cAC5D7C,EAAO,QAAQA,EAAO,QAAQ,OAAO,OAASA,EAAO,UAAW,EAAG,GAAO,EAAI,EACrEA,EAAO,YAAcA,EAAO,SAAS,OAAS,GACvDA,EAAO,QAAQA,EAAO,QAAQ,aAAc,EAAG,GAAO,EAAI,GAG9DA,EAAO,eAAiBuc,EACxBvc,EAAO,eAAiBwc,EACxBxc,EAAO,KAAK,SAAS,EACrB,MACF,CACA,IAAImX,EAAgBtU,EAAO,cACvBsU,IAAkB,OACpBA,EAAgBnX,EAAO,qBAAoB,GAE3CmX,EAAgB,KAAK,KAAK,WAAWtU,EAAO,cAAe,EAAE,CAAC,EAC1DwX,GAAkBlD,EAAgB,IAAM,IAC1CA,EAAgBA,EAAgB,IAGpC,MAAM4E,EAAiBlZ,EAAO,mBAAqBsU,EAAgBtU,EAAO,eAC1E,IAAI6Z,EAAerC,EAAiB,KAAK,IAAI0B,EAAgB,KAAK,KAAK5E,EAAgB,CAAC,CAAC,EAAI4E,EACzFW,EAAeX,IAAmB,IACpCW,GAAgBX,EAAiBW,EAAeX,GAElDW,GAAgB7Z,EAAO,qBACvB7C,EAAO,aAAe0c,EACtB,MAAMpK,EAActS,EAAO,MAAQ6C,EAAO,MAAQA,EAAO,KAAK,KAAO,EACjE8O,EAAO,OAASwF,EAAgBuF,GAAgB1c,EAAO,OAAO,SAAW,SAAW2R,EAAO,OAASwF,EAAgBuF,EAAe,EACrIpb,GAAY,0OAA0O,EAC7OgR,GAAezP,EAAO,KAAK,OAAS,OAC7CvB,GAAY,yEAAyE,EAEvF,MAAMqb,EAAuB,CAAA,EACvBC,EAAsB,CAAA,EACtBxC,EAAO9H,EAAc,KAAK,KAAKX,EAAO,OAAS9O,EAAO,KAAK,IAAI,EAAI8O,EAAO,OAC1EkL,EAAoBlD,GAAWS,EAAOqC,EAAetF,GAAiB,CAACkD,EAC7E,IAAI9D,EAAcsG,EAAoBJ,EAAezc,EAAO,YACxD,OAAOiY,EAAqB,IAC9BA,EAAmBjY,EAAO,cAAc2R,EAAO,KAAKpT,GAAMA,EAAG,UAAU,SAASsE,EAAO,gBAAgB,CAAC,CAAC,EAEzG0T,EAAc0B,EAEhB,MAAM6E,EAASzD,IAAc,QAAU,CAACA,EAClC0D,EAAS1D,IAAc,QAAU,CAACA,EACxC,IAAI2D,EAAkB,EAClBC,EAAiB,EAErB,MAAMC,GADiB5K,EAAcX,EAAOsG,CAAgB,EAAE,OAASA,IACrBoC,GAAkB,OAAO/B,EAAiB,IAAc,CAACnB,EAAgB,EAAI,GAAM,GAErI,GAAI+F,EAA0BR,EAAc,CAC1CM,EAAkB,KAAK,IAAIN,EAAeQ,EAAyBnB,CAAc,EACjF,QAAS3c,EAAI,EAAGA,EAAIsd,EAAeQ,EAAyB9d,GAAK,EAAG,CAClE,MAAMqG,EAAQrG,EAAI,KAAK,MAAMA,EAAIgb,CAAI,EAAIA,EACzC,GAAI9H,EAAa,CACf,MAAM6K,EAAoB/C,EAAO3U,EAAQ,EACzC,QAASrG,EAAIuS,EAAO,OAAS,EAAGvS,GAAK,EAAGA,GAAK,EACvCuS,EAAOvS,CAAC,EAAE,SAAW+d,GAAmBR,EAAqB,KAAKvd,CAAC,CAK3E,MACEud,EAAqB,KAAKvC,EAAO3U,EAAQ,CAAC,CAE9C,CACF,SAAWyX,EAA0B/F,EAAgBiD,EAAOsC,EAAc,CACxEO,EAAiB,KAAK,IAAIC,GAA2B9C,EAAOsC,EAAe,GAAIX,CAAc,EACzFc,IACFI,EAAiB,KAAK,IAAIA,EAAgB9F,EAAgBiD,EAAOqC,EAAe,CAAC,GAEnF,QAASrd,EAAI,EAAGA,EAAI6d,EAAgB7d,GAAK,EAAG,CAC1C,MAAMqG,EAAQrG,EAAI,KAAK,MAAMA,EAAIgb,CAAI,EAAIA,EACrC9H,EACFX,EAAO,QAAQ,CAAClG,EAAOc,IAAe,CAChCd,EAAM,SAAWhG,GAAOmX,EAAoB,KAAKrQ,CAAU,CACjE,CAAC,EAEDqQ,EAAoB,KAAKnX,CAAK,CAElC,CACF,CAsCA,GArCAzF,EAAO,oBAAsB,GAC7B,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,EACGA,EAAO,OAAO,SAAW,SAAW2R,EAAO,OAASwF,EAAgBuF,EAAe,IACjFE,EAAoB,SAAS3E,CAAgB,GAC/C2E,EAAoB,OAAOA,EAAoB,QAAQ3E,CAAgB,EAAG,CAAC,EAEzE0E,EAAqB,SAAS1E,CAAgB,GAChD0E,EAAqB,OAAOA,EAAqB,QAAQ1E,CAAgB,EAAG,CAAC,GAG7E8E,GACFJ,EAAqB,QAAQlX,GAAS,CACpCkM,EAAOlM,CAAK,EAAE,kBAAoB,GAClC6L,EAAS,QAAQK,EAAOlM,CAAK,CAAC,EAC9BkM,EAAOlM,CAAK,EAAE,kBAAoB,EACpC,CAAC,EAECqX,GACFF,EAAoB,QAAQnX,GAAS,CACnCkM,EAAOlM,CAAK,EAAE,kBAAoB,GAClC6L,EAAS,OAAOK,EAAOlM,CAAK,CAAC,EAC7BkM,EAAOlM,CAAK,EAAE,kBAAoB,EACpC,CAAC,EAEHzF,EAAO,aAAY,EACf6C,EAAO,gBAAkB,OAC3B7C,EAAO,aAAY,EACVsS,IAAgBqK,EAAqB,OAAS,GAAKI,GAAUH,EAAoB,OAAS,GAAKE,IACxG9c,EAAO,OAAO,QAAQ,CAACyL,EAAOc,IAAe,CAC3CvM,EAAO,KAAK,YAAYuM,EAAYd,EAAOzL,EAAO,MAAM,CAC1D,CAAC,EAEC6C,EAAO,qBACT7C,EAAO,mBAAkB,EAEvB0Z,GACF,GAAIiD,EAAqB,OAAS,GAAKI,GACrC,GAAI,OAAOnB,EAAmB,IAAa,CACzC,MAAMwB,EAAwBpd,EAAO,WAAWuW,CAAW,EAErD8G,EADoBrd,EAAO,WAAWuW,EAAcyG,CAAe,EACxCI,EAC7Bd,EACFtc,EAAO,aAAaA,EAAO,UAAYqd,CAAI,GAE3Crd,EAAO,QAAQuW,EAAc,KAAK,KAAKyG,CAAe,EAAG,EAAG,GAAO,EAAI,EACnE1E,IACFtY,EAAO,gBAAgB,eAAiBA,EAAO,gBAAgB,eAAiBqd,EAChFrd,EAAO,gBAAgB,iBAAmBA,EAAO,gBAAgB,iBAAmBqd,GAG1F,SACM/E,EAAc,CAChB,MAAMgF,EAAQhL,EAAcqK,EAAqB,OAAS9Z,EAAO,KAAK,KAAO8Z,EAAqB,OAClG3c,EAAO,QAAQA,EAAO,YAAcsd,EAAO,EAAG,GAAO,EAAI,EACzDtd,EAAO,gBAAgB,iBAAmBA,EAAO,SACnD,UAEO4c,EAAoB,OAAS,GAAKE,EAC3C,GAAI,OAAOlB,EAAmB,IAAa,CACzC,MAAMwB,EAAwBpd,EAAO,WAAWuW,CAAW,EAErD8G,EADoBrd,EAAO,WAAWuW,EAAc0G,CAAc,EACvCG,EAC7Bd,EACFtc,EAAO,aAAaA,EAAO,UAAYqd,CAAI,GAE3Crd,EAAO,QAAQuW,EAAc0G,EAAgB,EAAG,GAAO,EAAI,EACvD3E,IACFtY,EAAO,gBAAgB,eAAiBA,EAAO,gBAAgB,eAAiBqd,EAChFrd,EAAO,gBAAgB,iBAAmBA,EAAO,gBAAgB,iBAAmBqd,GAG1F,KAAO,CACL,MAAMC,EAAQhL,EAAcsK,EAAoB,OAAS/Z,EAAO,KAAK,KAAO+Z,EAAoB,OAChG5c,EAAO,QAAQA,EAAO,YAAcsd,EAAO,EAAG,GAAO,EAAI,CAC3D,EAKJ,GAFAtd,EAAO,eAAiBuc,EACxBvc,EAAO,eAAiBwc,EACpBxc,EAAO,YAAcA,EAAO,WAAW,SAAW,CAACuY,EAAc,CACnE,MAAMgF,EAAa,CACjB,eAAA3B,EACA,UAAAvC,EACA,aAAAf,EACA,iBAAAL,EACA,aAAc,EACpB,EACQ,MAAM,QAAQjY,EAAO,WAAW,OAAO,EACzCA,EAAO,WAAW,QAAQ,QAAQhC,GAAK,CACjC,CAACA,EAAE,WAAaA,EAAE,OAAO,MAAMA,EAAE,QAAQ,CAC3C,GAAGuf,EACH,QAASvf,EAAE,OAAO,gBAAkB6E,EAAO,cAAgB6W,EAAU,EAC/E,CAAS,CACH,CAAC,EACQ1Z,EAAO,WAAW,mBAAmBA,EAAO,aAAeA,EAAO,WAAW,QAAQ,OAAO,MACrGA,EAAO,WAAW,QAAQ,QAAQ,CAChC,GAAGud,EACH,QAASvd,EAAO,WAAW,QAAQ,OAAO,gBAAkB6C,EAAO,cAAgB6W,EAAU,EACrG,CAAO,CAEL,CACA1Z,EAAO,KAAK,SAAS,CACvB,CAEA,SAASwd,IAAc,CACrB,MAAMxd,EAAS,KACT,CACJ,OAAA6C,EACA,SAAAyO,CACJ,EAAMtR,EACJ,GAAI,CAAC6C,EAAO,MAAQ,CAACyO,GAAYtR,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAAS,OAClFA,EAAO,aAAY,EACnB,MAAMyd,EAAiB,CAAA,EACvBzd,EAAO,OAAO,QAAQ2I,GAAW,CAC/B,MAAMlD,EAAQ,OAAOkD,EAAQ,iBAAqB,IAAcA,EAAQ,aAAa,yBAAyB,EAAI,EAAIA,EAAQ,iBAC9H8U,EAAehY,CAAK,EAAIkD,CAC1B,CAAC,EACD3I,EAAO,OAAO,QAAQ2I,GAAW,CAC/BA,EAAQ,gBAAgB,yBAAyB,CACnD,CAAC,EACD8U,EAAe,QAAQ9U,GAAW,CAChC2I,EAAS,OAAO3I,CAAO,CACzB,CAAC,EACD3I,EAAO,aAAY,EACnBA,EAAO,QAAQA,EAAO,UAAW,CAAC,CACpC,CAEA,IAAI0d,GAAO,CACT,WAAA/B,GACA,QAAAU,GACA,YAAAmB,EACF,EAEA,SAASG,GAAcC,EAAQ,CAC7B,MAAM5d,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,eAAiBA,EAAO,OAAO,eAAiBA,EAAO,UAAYA,EAAO,OAAO,QAAS,OAC7G,MAAMzB,EAAKyB,EAAO,OAAO,oBAAsB,YAAcA,EAAO,GAAKA,EAAO,UAC5EA,EAAO,YACTA,EAAO,oBAAsB,IAE/BzB,EAAG,MAAM,OAAS,OAClBA,EAAG,MAAM,OAASqf,EAAS,WAAa,OACpC5d,EAAO,WACT,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,CAEL,CAEA,SAAS6d,IAAkB,CACzB,MAAM7d,EAAS,KACXA,EAAO,OAAO,eAAiBA,EAAO,UAAYA,EAAO,OAAO,UAGhEA,EAAO,YACTA,EAAO,oBAAsB,IAE/BA,EAAOA,EAAO,OAAO,oBAAsB,YAAc,KAAO,WAAW,EAAE,MAAM,OAAS,GACxFA,EAAO,WACT,sBAAsB,IAAM,CAC1BA,EAAO,oBAAsB,EAC/B,CAAC,EAEL,CAEA,IAAI8d,GAAa,CACf,cAAAH,GACA,gBAAAE,EACF,EAGA,SAASE,GAAeviB,EAAUwiB,EAAM,CAClCA,IAAS,SACXA,EAAO,MAET,SAASC,EAAc1f,EAAI,CACzB,GAAI,CAACA,GAAMA,IAAOhB,EAAW,GAAMgB,IAAOX,EAAS,EAAI,OAAO,KAC1DW,EAAG,eAAcA,EAAKA,EAAG,cAC7B,MAAM2f,EAAQ3f,EAAG,QAAQ/C,CAAQ,EACjC,MAAI,CAAC0iB,GAAS,CAAC3f,EAAG,YACT,KAEF2f,GAASD,EAAc1f,EAAG,YAAW,EAAG,IAAI,CACrD,CACA,OAAO0f,EAAcD,CAAI,CAC3B,CACA,SAASG,GAAiBne,EAAQwQ,EAAO4N,EAAQ,CAC/C,MAAM5f,EAASZ,EAAS,EAClB,CACJ,OAAAiF,CACJ,EAAM7C,EACEqe,EAAqBxb,EAAO,mBAC5Byb,EAAqBzb,EAAO,mBAClC,OAAIwb,IAAuBD,GAAUE,GAAsBF,GAAU5f,EAAO,WAAa8f,GACnFD,IAAuB,WACzB7N,EAAM,eAAc,EACb,IAEF,GAEF,EACT,CACA,SAAS+N,GAAa/N,EAAO,CAC3B,MAAMxQ,EAAS,KACTuJ,EAAWhM,EAAW,EAC5B,IAAIqG,EAAI4M,EACJ5M,EAAE,gBAAeA,EAAIA,EAAE,eAC3B,MAAM4I,EAAOxM,EAAO,gBACpB,GAAI4D,EAAE,OAAS,cAAe,CAC5B,GAAI4I,EAAK,YAAc,MAAQA,EAAK,YAAc5I,EAAE,UAClD,OAEF4I,EAAK,UAAY5I,EAAE,SACrB,MAAWA,EAAE,OAAS,cAAgBA,EAAE,cAAc,SAAW,IAC/D4I,EAAK,QAAU5I,EAAE,cAAc,CAAC,EAAE,YAEpC,GAAIA,EAAE,OAAS,aAAc,CAE3Bua,GAAiBne,EAAQ4D,EAAGA,EAAE,cAAc,CAAC,EAAE,KAAK,EACpD,MACF,CACA,KAAM,CACJ,OAAAf,EACA,QAAA2b,EACA,QAAA5E,CACJ,EAAM5Z,EAGJ,GAFI,CAAC4Z,GACD,CAAC/W,EAAO,eAAiBe,EAAE,cAAgB,SAC3C5D,EAAO,WAAa6C,EAAO,+BAC7B,OAEE,CAAC7C,EAAO,WAAa6C,EAAO,SAAWA,EAAO,MAChD7C,EAAO,QAAO,EAEhB,IAAIoE,EAAWR,EAAE,OAMjB,GALIf,EAAO,oBAAsB,WAC3B,CAACzB,GAAiBgD,EAAUpE,EAAO,SAAS,GAE9C,UAAW4D,GAAKA,EAAE,QAAU,GAC5B,WAAYA,GAAKA,EAAE,OAAS,GAC5B4I,EAAK,WAAaA,EAAK,QAAS,OAGpC,MAAMiS,EAAuB,CAAC,CAAC5b,EAAO,gBAAkBA,EAAO,iBAAmB,GAE5E6b,EAAY9a,EAAE,aAAeA,EAAE,aAAY,EAAKA,EAAE,KACpD6a,GAAwB7a,EAAE,QAAUA,EAAE,OAAO,YAAc8a,IAC7Dta,EAAWsa,EAAU,CAAC,GAExB,MAAMC,EAAoB9b,EAAO,kBAAoBA,EAAO,kBAAoB,IAAIA,EAAO,cAAc,GACnG+b,EAAiB,CAAC,EAAEhb,EAAE,QAAUA,EAAE,OAAO,YAG/C,GAAIf,EAAO,YAAc+b,EAAiBb,GAAeY,EAAmBva,CAAQ,EAAIA,EAAS,QAAQua,CAAiB,GAAI,CAC5H3e,EAAO,WAAa,GACpB,MACF,CACA,GAAI6C,EAAO,cACL,CAACuB,EAAS,QAAQvB,EAAO,YAAY,EAAG,OAE9C2b,EAAQ,SAAW5a,EAAE,MACrB4a,EAAQ,SAAW5a,EAAE,MACrB,MAAMwa,EAASI,EAAQ,SACjBK,EAASL,EAAQ,SAIvB,GAAI,CAACL,GAAiBne,EAAQ4D,EAAGwa,CAAM,EACrC,OAEF,OAAO,OAAO5R,EAAM,CAClB,UAAW,GACX,QAAS,GACT,oBAAqB,GACrB,YAAa,OACb,YAAa,MACjB,CAAG,EACDgS,EAAQ,OAASJ,EACjBI,EAAQ,OAASK,EACjBrS,EAAK,eAAiBnO,GAAG,EACzB2B,EAAO,WAAa,GACpBA,EAAO,WAAU,EACjBA,EAAO,eAAiB,OACpB6C,EAAO,UAAY,IAAG2J,EAAK,mBAAqB,IACpD,IAAIsS,EAAiB,GACjB1a,EAAS,QAAQoI,EAAK,iBAAiB,IACzCsS,EAAiB,GACb1a,EAAS,WAAa,WACxBoI,EAAK,UAAY,KAGjBjD,EAAS,eAAiBA,EAAS,cAAc,QAAQiD,EAAK,iBAAiB,GAAKjD,EAAS,gBAAkBnF,IAAaR,EAAE,cAAgB,SAAWA,EAAE,cAAgB,SAAW,CAACQ,EAAS,QAAQoI,EAAK,iBAAiB,IAChOjD,EAAS,cAAc,KAAI,EAE7B,MAAMwV,EAAuBD,GAAkB9e,EAAO,gBAAkB6C,EAAO,0BAC1EA,EAAO,+BAAiCkc,IAAyB,CAAC3a,EAAS,mBAC9ER,EAAE,eAAc,EAEdf,EAAO,UAAYA,EAAO,SAAS,SAAW7C,EAAO,UAAYA,EAAO,WAAa,CAAC6C,EAAO,SAC/F7C,EAAO,SAAS,aAAY,EAE9BA,EAAO,KAAK,aAAc4D,CAAC,CAC7B,CAEA,SAASob,GAAYxO,EAAO,CAC1B,MAAMjH,EAAWhM,EAAW,EACtByC,EAAS,KACTwM,EAAOxM,EAAO,gBACd,CACJ,OAAA6C,EACA,QAAA2b,EACA,aAAc7Y,EACd,QAAAiU,CACJ,EAAM5Z,EAEJ,GADI,CAAC4Z,GACD,CAAC/W,EAAO,eAAiB2N,EAAM,cAAgB,QAAS,OAC5D,IAAI5M,EAAI4M,EAER,GADI5M,EAAE,gBAAeA,EAAIA,EAAE,eACvBA,EAAE,OAAS,gBACT4I,EAAK,UAAY,MACV5I,EAAE,YACF4I,EAAK,WAAW,OAE7B,IAAIyS,EACJ,GAAIrb,EAAE,OAAS,aAEb,GADAqb,EAAc,CAAC,GAAGrb,EAAE,cAAc,EAAE,KAAKoW,GAAKA,EAAE,aAAexN,EAAK,OAAO,EACvE,CAACyS,GAAeA,EAAY,aAAezS,EAAK,QAAS,YAE7DyS,EAAcrb,EAEhB,GAAI,CAAC4I,EAAK,UAAW,CACfA,EAAK,aAAeA,EAAK,aAC3BxM,EAAO,KAAK,oBAAqB4D,CAAC,EAEpC,MACF,CACA,MAAMsb,EAAQD,EAAY,MACpBE,EAAQF,EAAY,MAC1B,GAAIrb,EAAE,wBAAyB,CAC7B4a,EAAQ,OAASU,EACjBV,EAAQ,OAASW,EACjB,MACF,CACA,GAAI,CAACnf,EAAO,eAAgB,CACrB4D,EAAE,OAAO,QAAQ4I,EAAK,iBAAiB,IAC1CxM,EAAO,WAAa,IAElBwM,EAAK,YACP,OAAO,OAAOgS,EAAS,CACrB,OAAQU,EACR,OAAQC,EACR,SAAUD,EACV,SAAUC,CAClB,CAAO,EACD3S,EAAK,eAAiBnO,GAAG,GAE3B,MACF,CACA,GAAIwE,EAAO,qBAAuB,CAACA,EAAO,KACxC,GAAI7C,EAAO,cAET,GAAImf,EAAQX,EAAQ,QAAUxe,EAAO,WAAaA,EAAO,aAAY,GAAMmf,EAAQX,EAAQ,QAAUxe,EAAO,WAAaA,EAAO,eAAgB,CAC9IwM,EAAK,UAAY,GACjBA,EAAK,QAAU,GACf,MACF,MACK,IAAI7G,IAAQuZ,EAAQV,EAAQ,QAAU,CAACxe,EAAO,WAAaA,EAAO,aAAY,GAAMkf,EAAQV,EAAQ,QAAU,CAACxe,EAAO,WAAaA,EAAO,aAAY,GAC3J,OACK,GAAI,CAAC2F,IAAQuZ,EAAQV,EAAQ,QAAUxe,EAAO,WAAaA,EAAO,aAAY,GAAMkf,EAAQV,EAAQ,QAAUxe,EAAO,WAAaA,EAAO,aAAY,GAC1J,OAMJ,GAHIuJ,EAAS,eAAiBA,EAAS,cAAc,QAAQiD,EAAK,iBAAiB,GAAKjD,EAAS,gBAAkB3F,EAAE,QAAUA,EAAE,cAAgB,SAC/I2F,EAAS,cAAc,KAAI,EAEzBA,EAAS,eACP3F,EAAE,SAAW2F,EAAS,eAAiB3F,EAAE,OAAO,QAAQ4I,EAAK,iBAAiB,EAAG,CACnFA,EAAK,QAAU,GACfxM,EAAO,WAAa,GACpB,MACF,CAEEwM,EAAK,qBACPxM,EAAO,KAAK,YAAa4D,CAAC,EAE5B4a,EAAQ,UAAYA,EAAQ,SAC5BA,EAAQ,UAAYA,EAAQ,SAC5BA,EAAQ,SAAWU,EACnBV,EAAQ,SAAWW,EACnB,MAAMC,EAAQZ,EAAQ,SAAWA,EAAQ,OACnCa,EAAQb,EAAQ,SAAWA,EAAQ,OACzC,GAAIxe,EAAO,OAAO,WAAa,KAAK,KAAKof,GAAS,EAAIC,GAAS,CAAC,EAAIrf,EAAO,OAAO,UAAW,OAC7F,GAAI,OAAOwM,EAAK,YAAgB,IAAa,CAC3C,IAAI8S,EACAtf,EAAO,aAAY,GAAMwe,EAAQ,WAAaA,EAAQ,QAAUxe,EAAO,WAAU,GAAMwe,EAAQ,WAAaA,EAAQ,OACtHhS,EAAK,YAAc,GAGf4S,EAAQA,EAAQC,EAAQA,GAAS,KACnCC,EAAa,KAAK,MAAM,KAAK,IAAID,CAAK,EAAG,KAAK,IAAID,CAAK,CAAC,EAAI,IAAM,KAAK,GACvE5S,EAAK,YAAcxM,EAAO,eAAiBsf,EAAazc,EAAO,WAAa,GAAKyc,EAAazc,EAAO,WAG3G,CASA,GARI2J,EAAK,aACPxM,EAAO,KAAK,oBAAqB4D,CAAC,EAEhC,OAAO4I,EAAK,YAAgB,MAC1BgS,EAAQ,WAAaA,EAAQ,QAAUA,EAAQ,WAAaA,EAAQ,UACtEhS,EAAK,YAAc,IAGnBA,EAAK,aAAe5I,EAAE,OAAS,aAAe4I,EAAK,gCAAiC,CACtFA,EAAK,UAAY,GACjB,MACF,CACA,GAAI,CAACA,EAAK,YACR,OAEFxM,EAAO,WAAa,GAChB,CAAC6C,EAAO,SAAWe,EAAE,YACvBA,EAAE,eAAc,EAEdf,EAAO,0BAA4B,CAACA,EAAO,QAC7Ce,EAAE,gBAAe,EAEnB,IAAIyZ,EAAOrd,EAAO,aAAY,EAAKof,EAAQC,EACvCE,EAAcvf,EAAO,aAAY,EAAKwe,EAAQ,SAAWA,EAAQ,UAAYA,EAAQ,SAAWA,EAAQ,UACxG3b,EAAO,iBACTwa,EAAO,KAAK,IAAIA,CAAI,GAAK1X,EAAM,EAAI,IACnC4Z,EAAc,KAAK,IAAIA,CAAW,GAAK5Z,EAAM,EAAI,KAEnD6Y,EAAQ,KAAOnB,EACfA,GAAQxa,EAAO,WACX8C,IACF0X,EAAO,CAACA,EACRkC,EAAc,CAACA,GAEjB,MAAMC,EAAuBxf,EAAO,iBACpCA,EAAO,eAAiBqd,EAAO,EAAI,OAAS,OAC5Crd,EAAO,iBAAmBuf,EAAc,EAAI,OAAS,OACrD,MAAME,EAASzf,EAAO,OAAO,MAAQ,CAAC6C,EAAO,QACvC6c,EAAe1f,EAAO,mBAAqB,QAAUA,EAAO,gBAAkBA,EAAO,mBAAqB,QAAUA,EAAO,eACjI,GAAI,CAACwM,EAAK,QAAS,CAQjB,GAPIiT,GAAUC,GACZ1f,EAAO,QAAQ,CACb,UAAWA,EAAO,cAC1B,CAAO,EAEHwM,EAAK,eAAiBxM,EAAO,aAAY,EACzCA,EAAO,cAAc,CAAC,EAClBA,EAAO,UAAW,CACpB,MAAM2f,EAAM,IAAI,OAAO,YAAY,gBAAiB,CAClD,QAAS,GACT,WAAY,GACZ,OAAQ,CACN,kBAAmB,EAC7B,CACA,CAAO,EACD3f,EAAO,UAAU,cAAc2f,CAAG,CACpC,CACAnT,EAAK,oBAAsB,GAEvB3J,EAAO,aAAe7C,EAAO,iBAAmB,IAAQA,EAAO,iBAAmB,KACpFA,EAAO,cAAc,EAAI,EAE3BA,EAAO,KAAK,kBAAmB4D,CAAC,CAClC,CAGA,GADA,IAAI,KAAI,EAAG,QAAO,EACdf,EAAO,iBAAmB,IAAS2J,EAAK,SAAWA,EAAK,oBAAsBgT,IAAyBxf,EAAO,kBAAoByf,GAAUC,GAAgB,KAAK,IAAIrC,CAAI,GAAK,EAAG,CACnL,OAAO,OAAOmB,EAAS,CACrB,OAAQU,EACR,OAAQC,EACR,SAAUD,EACV,SAAUC,EACV,eAAgB3S,EAAK,gBAC3B,CAAK,EACDA,EAAK,cAAgB,GACrBA,EAAK,eAAiBA,EAAK,iBAC3B,MACF,CACAxM,EAAO,KAAK,aAAc4D,CAAC,EAC3B4I,EAAK,QAAU,GACfA,EAAK,iBAAmB6Q,EAAO7Q,EAAK,eACpC,IAAIoT,EAAsB,GACtBC,EAAkBhd,EAAO,gBAiD7B,GAhDIA,EAAO,sBACTgd,EAAkB,GAEhBxC,EAAO,GACLoC,GAAUC,GAA8BlT,EAAK,oBAAsBA,EAAK,kBAAoB3J,EAAO,eAAiB7C,EAAO,eAAiBA,EAAO,gBAAgBA,EAAO,YAAc,CAAC,GAAK6C,EAAO,gBAAkB,QAAU7C,EAAO,OAAO,OAAS6C,EAAO,eAAiB,EAAI7C,EAAO,gBAAgBA,EAAO,YAAc,CAAC,EAAIA,EAAO,OAAO,aAAe,GAAKA,EAAO,OAAO,aAAeA,EAAO,aAAY,IACzZA,EAAO,QAAQ,CACb,UAAW,OACX,aAAc,GACd,iBAAkB,CAC1B,CAAO,EAECwM,EAAK,iBAAmBxM,EAAO,aAAY,IAC7C4f,EAAsB,GAClB/c,EAAO,aACT2J,EAAK,iBAAmBxM,EAAO,aAAY,EAAK,GAAK,CAACA,EAAO,aAAY,EAAKwM,EAAK,eAAiB6Q,IAASwC,KAGxGxC,EAAO,IACZoC,GAAUC,GAA8BlT,EAAK,oBAAsBA,EAAK,kBAAoB3J,EAAO,eAAiB7C,EAAO,aAAY,EAAKA,EAAO,gBAAgBA,EAAO,gBAAgB,OAAS,CAAC,EAAIA,EAAO,OAAO,cAAgB6C,EAAO,gBAAkB,QAAU7C,EAAO,OAAO,OAAS6C,EAAO,eAAiB,EAAI7C,EAAO,gBAAgBA,EAAO,gBAAgB,OAAS,CAAC,EAAIA,EAAO,OAAO,aAAe,GAAKA,EAAO,aAAY,IAC/aA,EAAO,QAAQ,CACb,UAAW,OACX,aAAc,GACd,iBAAkBA,EAAO,OAAO,QAAU6C,EAAO,gBAAkB,OAAS7C,EAAO,qBAAoB,EAAK,KAAK,KAAK,WAAW6C,EAAO,cAAe,EAAE,CAAC,EAClK,CAAO,EAEC2J,EAAK,iBAAmBxM,EAAO,aAAY,IAC7C4f,EAAsB,GAClB/c,EAAO,aACT2J,EAAK,iBAAmBxM,EAAO,aAAY,EAAK,GAAKA,EAAO,aAAY,EAAKwM,EAAK,eAAiB6Q,IAASwC,KAI9GD,IACFhc,EAAE,wBAA0B,IAI1B,CAAC5D,EAAO,gBAAkBA,EAAO,iBAAmB,QAAUwM,EAAK,iBAAmBA,EAAK,iBAC7FA,EAAK,iBAAmBA,EAAK,gBAE3B,CAACxM,EAAO,gBAAkBA,EAAO,iBAAmB,QAAUwM,EAAK,iBAAmBA,EAAK,iBAC7FA,EAAK,iBAAmBA,EAAK,gBAE3B,CAACxM,EAAO,gBAAkB,CAACA,EAAO,iBACpCwM,EAAK,iBAAmBA,EAAK,gBAI3B3J,EAAO,UAAY,EACrB,GAAI,KAAK,IAAIwa,CAAI,EAAIxa,EAAO,WAAa2J,EAAK,oBAC5C,GAAI,CAACA,EAAK,mBAAoB,CAC5BA,EAAK,mBAAqB,GAC1BgS,EAAQ,OAASA,EAAQ,SACzBA,EAAQ,OAASA,EAAQ,SACzBhS,EAAK,iBAAmBA,EAAK,eAC7BgS,EAAQ,KAAOxe,EAAO,aAAY,EAAKwe,EAAQ,SAAWA,EAAQ,OAASA,EAAQ,SAAWA,EAAQ,OACtG,MACF,MACK,CACLhS,EAAK,iBAAmBA,EAAK,eAC7B,MACF,CAEE,CAAC3J,EAAO,cAAgBA,EAAO,WAG/BA,EAAO,UAAYA,EAAO,SAAS,SAAW7C,EAAO,UAAY6C,EAAO,uBAC1E7C,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,GAExB6C,EAAO,UAAYA,EAAO,SAAS,SAAW7C,EAAO,UACvDA,EAAO,SAAS,YAAW,EAG7BA,EAAO,eAAewM,EAAK,gBAAgB,EAE3CxM,EAAO,aAAawM,EAAK,gBAAgB,EAC3C,CAEA,SAASsT,GAAWtP,EAAO,CACzB,MAAMxQ,EAAS,KACTwM,EAAOxM,EAAO,gBACpB,IAAI4D,EAAI4M,EACJ5M,EAAE,gBAAeA,EAAIA,EAAE,eAC3B,IAAIqb,EAEJ,GADqBrb,EAAE,OAAS,YAAcA,EAAE,OAAS,eAOvD,GADAqb,EAAc,CAAC,GAAGrb,EAAE,cAAc,EAAE,KAAKoW,GAAKA,EAAE,aAAexN,EAAK,OAAO,EACvE,CAACyS,GAAeA,EAAY,aAAezS,EAAK,QAAS,WAN5C,CAEjB,GADIA,EAAK,UAAY,MACjB5I,EAAE,YAAc4I,EAAK,UAAW,OACpCyS,EAAcrb,CAChB,CAIA,GAAI,CAAC,gBAAiB,aAAc,eAAgB,aAAa,EAAE,SAASA,EAAE,IAAI,GAE5E,EADY,CAAC,gBAAiB,aAAa,EAAE,SAASA,EAAE,IAAI,IAAM5D,EAAO,QAAQ,UAAYA,EAAO,QAAQ,YAE9G,OAGJwM,EAAK,UAAY,KACjBA,EAAK,QAAU,KACf,KAAM,CACJ,OAAA3J,EACA,QAAA2b,EACA,aAAc7Y,EACd,WAAAkM,EACA,QAAA+H,CACJ,EAAM5Z,EAEJ,GADI,CAAC4Z,GACD,CAAC/W,EAAO,eAAiBe,EAAE,cAAgB,QAAS,OAKxD,GAJI4I,EAAK,qBACPxM,EAAO,KAAK,WAAY4D,CAAC,EAE3B4I,EAAK,oBAAsB,GACvB,CAACA,EAAK,UAAW,CACfA,EAAK,SAAW3J,EAAO,YACzB7C,EAAO,cAAc,EAAK,EAE5BwM,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,MACF,CAGI3J,EAAO,YAAc2J,EAAK,SAAWA,EAAK,YAAcxM,EAAO,iBAAmB,IAAQA,EAAO,iBAAmB,KACtHA,EAAO,cAAc,EAAK,EAI5B,MAAM+f,EAAe1hB,GAAG,EAClB2hB,EAAWD,EAAevT,EAAK,eAGrC,GAAIxM,EAAO,WAAY,CACrB,MAAMigB,EAAWrc,EAAE,MAAQA,EAAE,cAAgBA,EAAE,aAAY,EAC3D5D,EAAO,mBAAmBigB,GAAYA,EAAS,CAAC,GAAKrc,EAAE,OAAQqc,CAAQ,EACvEjgB,EAAO,KAAK,YAAa4D,CAAC,EACtBoc,EAAW,KAAOD,EAAevT,EAAK,cAAgB,KACxDxM,EAAO,KAAK,wBAAyB4D,CAAC,CAE1C,CAKA,GAJA4I,EAAK,cAAgBnO,GAAG,EACxBF,GAAS,IAAM,CACR6B,EAAO,YAAWA,EAAO,WAAa,GAC7C,CAAC,EACG,CAACwM,EAAK,WAAa,CAACA,EAAK,SAAW,CAACxM,EAAO,gBAAkBwe,EAAQ,OAAS,GAAK,CAAChS,EAAK,eAAiBA,EAAK,mBAAqBA,EAAK,gBAAkB,CAACA,EAAK,cAAe,CACnLA,EAAK,UAAY,GACjBA,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,MACF,CACAA,EAAK,UAAY,GACjBA,EAAK,QAAU,GACfA,EAAK,YAAc,GACnB,IAAI0T,EAMJ,GALIrd,EAAO,aACTqd,EAAava,EAAM3F,EAAO,UAAY,CAACA,EAAO,UAE9CkgB,EAAa,CAAC1T,EAAK,iBAEjB3J,EAAO,QACT,OAEF,GAAIA,EAAO,UAAYA,EAAO,SAAS,QAAS,CAC9C7C,EAAO,SAAS,WAAW,CACzB,WAAAkgB,CACN,CAAK,EACD,MACF,CAGA,MAAMC,EAAcD,GAAc,CAAClgB,EAAO,aAAY,GAAM,CAACA,EAAO,OAAO,KAC3E,IAAIogB,EAAY,EACZ9M,EAAYtT,EAAO,gBAAgB,CAAC,EACxC,QAASZ,EAAI,EAAGA,EAAIyS,EAAW,OAAQzS,GAAKA,EAAIyD,EAAO,mBAAqB,EAAIA,EAAO,eAAgB,CACrG,MAAM6X,EAAYtb,EAAIyD,EAAO,mBAAqB,EAAI,EAAIA,EAAO,eAC7D,OAAOgP,EAAWzS,EAAIsb,CAAS,EAAM,KACnCyF,GAAeD,GAAcrO,EAAWzS,CAAC,GAAK8gB,EAAarO,EAAWzS,EAAIsb,CAAS,KACrF0F,EAAYhhB,EACZkU,EAAYzB,EAAWzS,EAAIsb,CAAS,EAAI7I,EAAWzS,CAAC,IAE7C+gB,GAAeD,GAAcrO,EAAWzS,CAAC,KAClDghB,EAAYhhB,EACZkU,EAAYzB,EAAWA,EAAW,OAAS,CAAC,EAAIA,EAAWA,EAAW,OAAS,CAAC,EAEpF,CACA,IAAIwO,EAAmB,KACnBC,EAAkB,KAClBzd,EAAO,SACL7C,EAAO,YACTsgB,EAAkBzd,EAAO,SAAWA,EAAO,QAAQ,SAAW7C,EAAO,QAAUA,EAAO,QAAQ,OAAO,OAAS,EAAIA,EAAO,OAAO,OAAS,EAChIA,EAAO,QAChBqgB,EAAmB,IAIvB,MAAME,GAASL,EAAarO,EAAWuO,CAAS,GAAK9M,EAC/CoH,EAAY0F,EAAYvd,EAAO,mBAAqB,EAAI,EAAIA,EAAO,eACzE,GAAImd,EAAWnd,EAAO,aAAc,CAElC,GAAI,CAACA,EAAO,WAAY,CACtB7C,EAAO,QAAQA,EAAO,WAAW,EACjC,MACF,CACIA,EAAO,iBAAmB,SACxBugB,GAAS1d,EAAO,gBAAiB7C,EAAO,QAAQ6C,EAAO,QAAU7C,EAAO,MAAQqgB,EAAmBD,EAAY1F,CAAS,EAAO1a,EAAO,QAAQogB,CAAS,GAEzJpgB,EAAO,iBAAmB,SACxBugB,EAAQ,EAAI1d,EAAO,gBACrB7C,EAAO,QAAQogB,EAAY1F,CAAS,EAC3B4F,IAAoB,MAAQC,EAAQ,GAAK,KAAK,IAAIA,CAAK,EAAI1d,EAAO,gBAC3E7C,EAAO,QAAQsgB,CAAe,EAE9BtgB,EAAO,QAAQogB,CAAS,EAG9B,KAAO,CAEL,GAAI,CAACvd,EAAO,YAAa,CACvB7C,EAAO,QAAQA,EAAO,WAAW,EACjC,MACF,CAC0BA,EAAO,aAAe4D,EAAE,SAAW5D,EAAO,WAAW,QAAU4D,EAAE,SAAW5D,EAAO,WAAW,QAQ7G4D,EAAE,SAAW5D,EAAO,WAAW,OACxCA,EAAO,QAAQogB,EAAY1F,CAAS,EAEpC1a,EAAO,QAAQogB,CAAS,GATpBpgB,EAAO,iBAAmB,QAC5BA,EAAO,QAAQqgB,IAAqB,KAAOA,EAAmBD,EAAY1F,CAAS,EAEjF1a,EAAO,iBAAmB,QAC5BA,EAAO,QAAQsgB,IAAoB,KAAOA,EAAkBF,CAAS,EAO3E,CACF,CAEA,SAASI,IAAW,CAClB,MAAMxgB,EAAS,KACT,CACJ,OAAA6C,EACA,GAAAtE,CACJ,EAAMyB,EACJ,GAAIzB,GAAMA,EAAG,cAAgB,EAAG,OAG5BsE,EAAO,aACT7C,EAAO,cAAa,EAItB,KAAM,CACJ,eAAAwc,EACA,eAAAD,EACA,SAAA3K,CACJ,EAAM5R,EACEyR,EAAYzR,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAG1DA,EAAO,eAAiB,GACxBA,EAAO,eAAiB,GACxBA,EAAO,WAAU,EACjBA,EAAO,aAAY,EACnBA,EAAO,oBAAmB,EAC1B,MAAMygB,EAAgBhP,GAAa5O,EAAO,MACrCA,EAAO,gBAAkB,QAAUA,EAAO,cAAgB,IAAM7C,EAAO,OAAS,CAACA,EAAO,aAAe,CAACA,EAAO,OAAO,gBAAkB,CAACygB,EAC5IzgB,EAAO,QAAQA,EAAO,OAAO,OAAS,EAAG,EAAG,GAAO,EAAI,EAEnDA,EAAO,OAAO,MAAQ,CAACyR,EACzBzR,EAAO,YAAYA,EAAO,UAAW,EAAG,GAAO,EAAI,EAEnDA,EAAO,QAAQA,EAAO,YAAa,EAAG,GAAO,EAAI,EAGjDA,EAAO,UAAYA,EAAO,SAAS,SAAWA,EAAO,SAAS,SAChE,aAAaA,EAAO,SAAS,aAAa,EAC1CA,EAAO,SAAS,cAAgB,WAAW,IAAM,CAC3CA,EAAO,UAAYA,EAAO,SAAS,SAAWA,EAAO,SAAS,QAChEA,EAAO,SAAS,OAAM,CAE1B,EAAG,GAAG,GAGRA,EAAO,eAAiBuc,EACxBvc,EAAO,eAAiBwc,EACpBxc,EAAO,OAAO,eAAiB4R,IAAa5R,EAAO,UACrDA,EAAO,cAAa,CAExB,CAEA,SAAS0gB,GAAQ9c,EAAG,CAClB,MAAM5D,EAAS,KACVA,EAAO,UACPA,EAAO,aACNA,EAAO,OAAO,eAAe4D,EAAE,eAAc,EAC7C5D,EAAO,OAAO,0BAA4BA,EAAO,YACnD4D,EAAE,gBAAe,EACjBA,EAAE,yBAAwB,IAGhC,CAEA,SAAS+c,IAAW,CAClB,MAAM3gB,EAAS,KACT,CACJ,UAAAqR,EACA,aAAAuJ,EACA,QAAAhB,CACJ,EAAM5Z,EACJ,GAAI,CAAC4Z,EAAS,OACd5Z,EAAO,kBAAoBA,EAAO,UAC9BA,EAAO,eACTA,EAAO,UAAY,CAACqR,EAAU,WAE9BrR,EAAO,UAAY,CAACqR,EAAU,UAG5BrR,EAAO,YAAc,IAAGA,EAAO,UAAY,GAC/CA,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,EAC1B,IAAI2Y,EACJ,MAAMpD,EAAiBvV,EAAO,aAAY,EAAKA,EAAO,aAAY,EAC9DuV,IAAmB,EACrBoD,EAAc,EAEdA,GAAe3Y,EAAO,UAAYA,EAAO,aAAY,GAAMuV,EAEzDoD,IAAgB3Y,EAAO,UACzBA,EAAO,eAAe4a,EAAe,CAAC5a,EAAO,UAAYA,EAAO,SAAS,EAE3EA,EAAO,KAAK,eAAgBA,EAAO,UAAW,EAAK,CACrD,CAEA,SAAS4gB,GAAOhd,EAAG,CACjB,MAAM5D,EAAS,KACf4W,GAAqB5W,EAAQ4D,EAAE,MAAM,EACjC,EAAA5D,EAAO,OAAO,SAAWA,EAAO,OAAO,gBAAkB,QAAU,CAACA,EAAO,OAAO,aAGtFA,EAAO,OAAM,CACf,CAEA,SAAS6gB,IAAuB,CAC9B,MAAM7gB,EAAS,KACXA,EAAO,gCACXA,EAAO,8BAAgC,GACnCA,EAAO,OAAO,sBAChBA,EAAO,GAAG,MAAM,YAAc,QAElC,CAEA,MAAMmQ,GAAS,CAACnQ,EAAQuQ,IAAW,CACjC,MAAMhH,EAAWhM,EAAW,EACtB,CACJ,OAAAsF,EACA,GAAAtE,EACA,UAAA8S,EACA,OAAA/D,CACJ,EAAMtN,EACE8gB,EAAU,CAAC,CAACje,EAAO,OACnBke,EAAYxQ,IAAW,KAAO,mBAAqB,sBACnDyQ,EAAezQ,EACjB,CAAChS,GAAM,OAAOA,GAAO,WAGzBgL,EAASwX,CAAS,EAAE,aAAc/gB,EAAO,qBAAsB,CAC7D,QAAS,GACT,QAAA8gB,CACJ,CAAG,EACDviB,EAAGwiB,CAAS,EAAE,aAAc/gB,EAAO,aAAc,CAC/C,QAAS,EACb,CAAG,EACDzB,EAAGwiB,CAAS,EAAE,cAAe/gB,EAAO,aAAc,CAChD,QAAS,EACb,CAAG,EACDuJ,EAASwX,CAAS,EAAE,YAAa/gB,EAAO,YAAa,CACnD,QAAS,GACT,QAAA8gB,CACJ,CAAG,EACDvX,EAASwX,CAAS,EAAE,cAAe/gB,EAAO,YAAa,CACrD,QAAS,GACT,QAAA8gB,CACJ,CAAG,EACDvX,EAASwX,CAAS,EAAE,WAAY/gB,EAAO,WAAY,CACjD,QAAS,EACb,CAAG,EACDuJ,EAASwX,CAAS,EAAE,YAAa/gB,EAAO,WAAY,CAClD,QAAS,EACb,CAAG,EACDuJ,EAASwX,CAAS,EAAE,gBAAiB/gB,EAAO,WAAY,CACtD,QAAS,EACb,CAAG,EACDuJ,EAASwX,CAAS,EAAE,cAAe/gB,EAAO,WAAY,CACpD,QAAS,EACb,CAAG,EACDuJ,EAASwX,CAAS,EAAE,aAAc/gB,EAAO,WAAY,CACnD,QAAS,EACb,CAAG,EACDuJ,EAASwX,CAAS,EAAE,eAAgB/gB,EAAO,WAAY,CACrD,QAAS,EACb,CAAG,EACDuJ,EAASwX,CAAS,EAAE,cAAe/gB,EAAO,WAAY,CACpD,QAAS,EACb,CAAG,GAGG6C,EAAO,eAAiBA,EAAO,2BACjCtE,EAAGwiB,CAAS,EAAE,QAAS/gB,EAAO,QAAS,EAAI,EAEzC6C,EAAO,SACTwO,EAAU0P,CAAS,EAAE,SAAU/gB,EAAO,QAAQ,EAI5C6C,EAAO,qBACT7C,EAAOghB,CAAY,EAAE1T,EAAO,KAAOA,EAAO,QAAU,0CAA4C,wBAAyBkT,GAAU,EAAI,EAEvIxgB,EAAOghB,CAAY,EAAE,iBAAkBR,GAAU,EAAI,EAIvDjiB,EAAGwiB,CAAS,EAAE,OAAQ/gB,EAAO,OAAQ,CACnC,QAAS,EACb,CAAG,EACH,EACA,SAASihB,IAAe,CACtB,MAAMjhB,EAAS,KACT,CACJ,OAAA6C,CACJ,EAAM7C,EACJA,EAAO,aAAeue,GAAa,KAAKve,CAAM,EAC9CA,EAAO,YAAcgf,GAAY,KAAKhf,CAAM,EAC5CA,EAAO,WAAa8f,GAAW,KAAK9f,CAAM,EAC1CA,EAAO,qBAAuB6gB,GAAqB,KAAK7gB,CAAM,EAC1D6C,EAAO,UACT7C,EAAO,SAAW2gB,GAAS,KAAK3gB,CAAM,GAExCA,EAAO,QAAU0gB,GAAQ,KAAK1gB,CAAM,EACpCA,EAAO,OAAS4gB,GAAO,KAAK5gB,CAAM,EAClCmQ,GAAOnQ,EAAQ,IAAI,CACrB,CACA,SAASkhB,IAAe,CAEtB/Q,GADe,KACA,KAAK,CACtB,CACA,IAAIgR,GAAW,CACb,aAAAF,GACA,aAAAC,EACF,EAEA,MAAME,GAAgB,CAACphB,EAAQ6C,IACtB7C,EAAO,MAAQ6C,EAAO,MAAQA,EAAO,KAAK,KAAO,EAE1D,SAASwe,IAAgB,CACvB,MAAMrhB,EAAS,KACT,CACJ,UAAAuX,EACA,YAAA+J,EACA,OAAAze,EACA,GAAAtE,CACJ,EAAMyB,EACE8K,EAAcjI,EAAO,YAC3B,GAAI,CAACiI,GAAeA,GAAe,OAAO,KAAKA,CAAW,EAAE,SAAW,EAAG,OAC1E,MAAMvB,EAAWhM,EAAW,EAGtBgkB,EAAkB1e,EAAO,kBAAoB,UAAY,CAACA,EAAO,gBAAkBA,EAAO,gBAAkB,YAC5G2e,EAAsB,CAAC,SAAU,WAAW,EAAE,SAAS3e,EAAO,eAAe,GAAK,CAACA,EAAO,gBAAkB7C,EAAO,GAAKuJ,EAAS,cAAc1G,EAAO,eAAe,EACrK4e,EAAazhB,EAAO,cAAc8K,EAAayW,EAAiBC,CAAmB,EACzF,GAAI,CAACC,GAAczhB,EAAO,oBAAsByhB,EAAY,OAE5D,MAAMC,GADuBD,KAAc3W,EAAcA,EAAY2W,CAAU,EAAI,SAClCzhB,EAAO,eAClD2hB,EAAcP,GAAcphB,EAAQ6C,CAAM,EAC1C+e,EAAaR,GAAcphB,EAAQ0hB,CAAgB,EACnDG,EAAgB7hB,EAAO,OAAO,WAC9B8hB,EAAeJ,EAAiB,WAChCK,EAAalf,EAAO,QACtB8e,GAAe,CAACC,GAClBrjB,EAAG,UAAU,OAAO,GAAGsE,EAAO,sBAAsB,OAAQ,GAAGA,EAAO,sBAAsB,aAAa,EACzG7C,EAAO,qBAAoB,GAClB,CAAC2hB,GAAeC,IACzBrjB,EAAG,UAAU,IAAI,GAAGsE,EAAO,sBAAsB,MAAM,GACnD6e,EAAiB,KAAK,MAAQA,EAAiB,KAAK,OAAS,UAAY,CAACA,EAAiB,KAAK,MAAQ7e,EAAO,KAAK,OAAS,WAC/HtE,EAAG,UAAU,IAAI,GAAGsE,EAAO,sBAAsB,aAAa,EAEhE7C,EAAO,qBAAoB,GAEzB6hB,GAAiB,CAACC,EACpB9hB,EAAO,gBAAe,EACb,CAAC6hB,GAAiBC,GAC3B9hB,EAAO,cAAa,EAItB,CAAC,aAAc,aAAc,WAAW,EAAE,QAAQgC,GAAQ,CACxD,GAAI,OAAO0f,EAAiB1f,CAAI,EAAM,IAAa,OACnD,MAAMggB,EAAmBnf,EAAOb,CAAI,GAAKa,EAAOb,CAAI,EAAE,QAChDigB,EAAkBP,EAAiB1f,CAAI,GAAK0f,EAAiB1f,CAAI,EAAE,QACrEggB,GAAoB,CAACC,GACvBjiB,EAAOgC,CAAI,EAAE,QAAO,EAElB,CAACggB,GAAoBC,GACvBjiB,EAAOgC,CAAI,EAAE,OAAM,CAEvB,CAAC,EACD,MAAMkgB,EAAmBR,EAAiB,WAAaA,EAAiB,YAAc7e,EAAO,UACvFsf,EAActf,EAAO,OAAS6e,EAAiB,gBAAkB7e,EAAO,eAAiBqf,GACzFE,EAAUvf,EAAO,KACnBqf,GAAoBZ,GACtBthB,EAAO,gBAAe,EAExB9C,EAAO8C,EAAO,OAAQ0hB,CAAgB,EACtC,MAAMW,EAAYriB,EAAO,OAAO,QAC1BsiB,EAAUtiB,EAAO,OAAO,KAC9B,OAAO,OAAOA,EAAQ,CACpB,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,cAClC,CAAG,EACG+hB,GAAc,CAACM,EACjBriB,EAAO,QAAO,EACL,CAAC+hB,GAAcM,GACxBriB,EAAO,OAAM,EAEfA,EAAO,kBAAoByhB,EAC3BzhB,EAAO,KAAK,oBAAqB0hB,CAAgB,EAC7CJ,IACEa,GACFniB,EAAO,YAAW,EAClBA,EAAO,WAAWuX,CAAS,EAC3BvX,EAAO,aAAY,GACV,CAACoiB,GAAWE,GACrBtiB,EAAO,WAAWuX,CAAS,EAC3BvX,EAAO,aAAY,GACVoiB,GAAW,CAACE,GACrBtiB,EAAO,YAAW,GAGtBA,EAAO,KAAK,aAAc0hB,CAAgB,CAC5C,CAEA,SAASa,GAAczX,EAAakT,EAAMwE,EAAa,CAIrD,GAHIxE,IAAS,SACXA,EAAO,UAEL,CAAClT,GAAekT,IAAS,aAAe,CAACwE,EAAa,OAC1D,IAAIf,EAAa,GACjB,MAAMjjB,EAASZ,EAAS,EAClB6kB,EAAgBzE,IAAS,SAAWxf,EAAO,YAAcgkB,EAAY,aACrEE,EAAS,OAAO,KAAK5X,CAAW,EAAE,IAAI6X,GAAS,CACnD,GAAI,OAAOA,GAAU,UAAYA,EAAM,QAAQ,GAAG,IAAM,EAAG,CACzD,MAAMC,EAAW,WAAWD,EAAM,OAAO,CAAC,CAAC,EAE3C,MAAO,CACL,MAFYF,EAAgBG,EAG5B,MAAAD,CACR,CACI,CACA,MAAO,CACL,MAAOA,EACP,MAAAA,CACN,CACE,CAAC,EACDD,EAAO,KAAK,CAAC,EAAGG,IAAM,SAAS,EAAE,MAAO,EAAE,EAAI,SAASA,EAAE,MAAO,EAAE,CAAC,EACnE,QAASzjB,EAAI,EAAGA,EAAIsjB,EAAO,OAAQtjB,GAAK,EAAG,CACzC,KAAM,CACJ,MAAAujB,EACA,MAAA3mB,CACN,EAAQ0mB,EAAOtjB,CAAC,EACR4e,IAAS,SACPxf,EAAO,WAAW,eAAexC,CAAK,KAAK,EAAE,UAC/CylB,EAAakB,GAEN3mB,GAASwmB,EAAY,cAC9Bf,EAAakB,EAEjB,CACA,OAAOlB,GAAc,KACvB,CAEA,IAAI3W,GAAc,CAChB,cAAAuW,GACA,cAAAkB,EACF,EAEA,SAASO,GAAe5T,EAAS6T,EAAQ,CACvC,MAAMC,EAAgB,CAAA,EACtB,OAAA9T,EAAQ,QAAQ+T,GAAQ,CAClB,OAAOA,GAAS,SAClB,OAAO,KAAKA,CAAI,EAAE,QAAQC,GAAc,CAClCD,EAAKC,CAAU,GACjBF,EAAc,KAAKD,EAASG,CAAU,CAE1C,CAAC,EACQ,OAAOD,GAAS,UACzBD,EAAc,KAAKD,EAASE,CAAI,CAEpC,CAAC,EACMD,CACT,CACA,SAASG,IAAa,CACpB,MAAMnjB,EAAS,KACT,CACJ,WAAAkjB,EACA,OAAArgB,EACA,IAAA8C,EACA,GAAApH,EACA,OAAA+O,CACJ,EAAMtN,EAEEojB,EAAWN,GAAe,CAAC,cAAejgB,EAAO,UAAW,CAChE,YAAa7C,EAAO,OAAO,UAAY6C,EAAO,SAAS,OAC3D,EAAK,CACD,WAAcA,EAAO,UACzB,EAAK,CACD,IAAO8C,CACX,EAAK,CACD,KAAQ9C,EAAO,MAAQA,EAAO,KAAK,KAAO,CAC9C,EAAK,CACD,cAAeA,EAAO,MAAQA,EAAO,KAAK,KAAO,GAAKA,EAAO,KAAK,OAAS,QAC/E,EAAK,CACD,QAAWyK,EAAO,OACtB,EAAK,CACD,IAAOA,EAAO,GAClB,EAAK,CACD,WAAYzK,EAAO,OACvB,EAAK,CACD,SAAYA,EAAO,SAAWA,EAAO,cACzC,EAAK,CACD,iBAAkBA,EAAO,mBAC7B,CAAG,EAAGA,EAAO,sBAAsB,EACjCqgB,EAAW,KAAK,GAAGE,CAAQ,EAC3B7kB,EAAG,UAAU,IAAI,GAAG2kB,CAAU,EAC9BljB,EAAO,qBAAoB,CAC7B,CAEA,SAASqjB,IAAgB,CACvB,MAAMrjB,EAAS,KACT,CACJ,GAAAzB,EACA,WAAA2kB,CACJ,EAAMljB,EACA,CAACzB,GAAM,OAAOA,GAAO,WACzBA,EAAG,UAAU,OAAO,GAAG2kB,CAAU,EACjCljB,EAAO,qBAAoB,EAC7B,CAEA,IAAIjC,GAAU,CACZ,WAAAolB,GACA,cAAAE,EACF,EAEA,SAASC,IAAgB,CACvB,MAAMtjB,EAAS,KACT,CACJ,SAAUujB,EACV,OAAA1gB,CACJ,EAAM7C,EACE,CACJ,mBAAAwjB,CACJ,EAAM3gB,EACJ,GAAI2gB,EAAoB,CACtB,MAAMxN,EAAiBhW,EAAO,OAAO,OAAS,EACxCyjB,EAAqBzjB,EAAO,WAAWgW,CAAc,EAAIhW,EAAO,gBAAgBgW,CAAc,EAAIwN,EAAqB,EAC7HxjB,EAAO,SAAWA,EAAO,KAAOyjB,CAClC,MACEzjB,EAAO,SAAWA,EAAO,SAAS,SAAW,EAE3C6C,EAAO,iBAAmB,KAC5B7C,EAAO,eAAiB,CAACA,EAAO,UAE9B6C,EAAO,iBAAmB,KAC5B7C,EAAO,eAAiB,CAACA,EAAO,UAE9BujB,GAAaA,IAAcvjB,EAAO,WACpCA,EAAO,MAAQ,IAEbujB,IAAcvjB,EAAO,UACvBA,EAAO,KAAKA,EAAO,SAAW,OAAS,QAAQ,CAEnD,CACA,IAAI0jB,GAAkB,CACpB,cAAAJ,EACF,EAEIK,GAAW,CACb,KAAM,GACN,UAAW,aACX,eAAgB,GAChB,sBAAuB,mBACvB,kBAAmB,UACnB,aAAc,EACd,MAAO,IACP,QAAS,GACT,qBAAsB,GACtB,eAAgB,GAChB,OAAQ,GACR,eAAgB,GAChB,aAAc,SACd,QAAS,GACT,kBAAmB,wDAEnB,MAAO,KACP,OAAQ,KAER,+BAAgC,GAEhC,UAAW,KACX,IAAK,KAEL,mBAAoB,GACpB,mBAAoB,GAEpB,WAAY,GAEZ,eAAgB,GAEhB,iBAAkB,GAElB,OAAQ,QAIR,YAAa,OACb,gBAAiB,SAEjB,aAAc,EACd,cAAe,EACf,eAAgB,EAChB,mBAAoB,EACpB,mBAAoB,GACpB,eAAgB,GAChB,qBAAsB,GACtB,mBAAoB,EAEpB,kBAAmB,EAEnB,oBAAqB,GACrB,yBAA0B,GAE1B,cAAe,GAEf,aAAc,GAEd,WAAY,EACZ,WAAY,GACZ,cAAe,GACf,YAAa,GACb,WAAY,GACZ,gBAAiB,GACjB,aAAc,IACd,aAAc,GACd,eAAgB,GAChB,UAAW,EACX,yBAA0B,GAC1B,yBAA0B,GAC1B,8BAA+B,GAC/B,oBAAqB,GAErB,kBAAmB,GAEnB,WAAY,GACZ,gBAAiB,IAEjB,oBAAqB,GAErB,WAAY,GAEZ,cAAe,GACf,yBAA0B,GAC1B,oBAAqB,GAErB,KAAM,GACN,mBAAoB,GACpB,qBAAsB,EACtB,oBAAqB,GAErB,OAAQ,GAER,eAAgB,GAChB,eAAgB,GAChB,aAAc,KAEd,UAAW,GACX,eAAgB,oBAChB,kBAAmB,KAEnB,iBAAkB,GAClB,wBAAyB,GAEzB,uBAAwB,UAExB,WAAY,eACZ,gBAAiB,qBACjB,iBAAkB,sBAClB,kBAAmB,uBACnB,uBAAwB,6BACxB,eAAgB,oBAChB,eAAgB,oBAChB,aAAc,iBACd,mBAAoB,wBACpB,oBAAqB,EAErB,mBAAoB,GAEpB,aAAc,EAChB,EAEA,SAASC,GAAmB/gB,EAAQghB,EAAkB,CACpD,OAAO,SAAsB5mB,EAAK,CAC5BA,IAAQ,SACVA,EAAM,CAAA,GAER,MAAM6mB,EAAkB,OAAO,KAAK7mB,CAAG,EAAE,CAAC,EACpC8mB,EAAe9mB,EAAI6mB,CAAe,EACxC,GAAI,OAAOC,GAAiB,UAAYA,IAAiB,KAAM,CAC7D7mB,EAAO2mB,EAAkB5mB,CAAG,EAC5B,MACF,CAYA,GAXI4F,EAAOihB,CAAe,IAAM,KAC9BjhB,EAAOihB,CAAe,EAAI,CACxB,QAAS,EACjB,GAEQA,IAAoB,cAAgBjhB,EAAOihB,CAAe,GAAKjhB,EAAOihB,CAAe,EAAE,SAAW,CAACjhB,EAAOihB,CAAe,EAAE,QAAU,CAACjhB,EAAOihB,CAAe,EAAE,SAChKjhB,EAAOihB,CAAe,EAAE,KAAO,IAE7B,CAAC,aAAc,WAAW,EAAE,QAAQA,CAAe,GAAK,GAAKjhB,EAAOihB,CAAe,GAAKjhB,EAAOihB,CAAe,EAAE,SAAW,CAACjhB,EAAOihB,CAAe,EAAE,KACtJjhB,EAAOihB,CAAe,EAAE,KAAO,IAE7B,EAAEA,KAAmBjhB,GAAU,YAAakhB,GAAe,CAC7D7mB,EAAO2mB,EAAkB5mB,CAAG,EAC5B,MACF,CACI,OAAO4F,EAAOihB,CAAe,GAAM,UAAY,EAAE,YAAajhB,EAAOihB,CAAe,KACtFjhB,EAAOihB,CAAe,EAAE,QAAU,IAE/BjhB,EAAOihB,CAAe,IAAGjhB,EAAOihB,CAAe,EAAI,CACtD,QAAS,EACf,GACI5mB,EAAO2mB,EAAkB5mB,CAAG,CAC9B,CACF,CAGA,MAAM+mB,GAAa,CACjB,cAAA9T,GACA,OAAA1M,GACA,UAAAoR,GACA,WAAA6E,GACA,MAAAhO,GACA,KAAAiS,GACA,WAAAI,GACA,OAAQqD,GACR,YAAArW,GACA,cAAe4Y,GACf,QAAA3lB,EACF,EACMkmB,GAAmB,CAAA,EACzB,MAAMC,CAAO,CACX,aAAc,CACZ,IAAI3lB,EACAsE,EACJ,QAAS6N,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAEzBD,EAAK,SAAW,GAAKA,EAAK,CAAC,EAAE,aAAe,OAAO,UAAU,SAAS,KAAKA,EAAK,CAAC,CAAC,EAAE,MAAM,EAAG,EAAE,IAAM,SACvG9N,EAAS8N,EAAK,CAAC,EAEf,CAACpS,EAAIsE,CAAM,EAAI8N,EAEZ9N,IAAQA,EAAS,CAAA,GACtBA,EAAS3F,EAAO,CAAA,EAAI2F,CAAM,EACtBtE,GAAM,CAACsE,EAAO,KAAIA,EAAO,GAAKtE,GAClC,MAAMgL,EAAWhM,EAAW,EAC5B,GAAIsF,EAAO,IAAM,OAAOA,EAAO,IAAO,UAAY0G,EAAS,iBAAiB1G,EAAO,EAAE,EAAE,OAAS,EAAG,CACjG,MAAMshB,EAAU,CAAA,EAChB,OAAA5a,EAAS,iBAAiB1G,EAAO,EAAE,EAAE,QAAQ2f,GAAe,CAC1D,MAAM4B,EAAYlnB,EAAO,CAAA,EAAI2F,EAAQ,CACnC,GAAI2f,CACd,CAAS,EACD2B,EAAQ,KAAK,IAAID,EAAOE,CAAS,CAAC,CACpC,CAAC,EAEMD,CACT,CAGA,MAAMnkB,EAAS,KACfA,EAAO,WAAa,GACpBA,EAAO,QAAUgN,GAAU,EAC3BhN,EAAO,OAASgO,GAAU,CACxB,UAAWnL,EAAO,SACxB,CAAK,EACD7C,EAAO,QAAU4O,GAAU,EAC3B5O,EAAO,gBAAkB,CAAA,EACzBA,EAAO,mBAAqB,CAAA,EAC5BA,EAAO,QAAU,CAAC,GAAGA,EAAO,WAAW,EACnC6C,EAAO,SAAW,MAAM,QAAQA,EAAO,OAAO,GAChD7C,EAAO,QAAQ,KAAK,GAAG6C,EAAO,OAAO,EAEvC,MAAMghB,EAAmB,CAAA,EACzB7jB,EAAO,QAAQ,QAAQqkB,GAAO,CAC5BA,EAAI,CACF,OAAAxhB,EACA,OAAA7C,EACA,aAAc4jB,GAAmB/gB,EAAQghB,CAAgB,EACzD,GAAI7jB,EAAO,GAAG,KAAKA,CAAM,EACzB,KAAMA,EAAO,KAAK,KAAKA,CAAM,EAC7B,IAAKA,EAAO,IAAI,KAAKA,CAAM,EAC3B,KAAMA,EAAO,KAAK,KAAKA,CAAM,CACrC,CAAO,CACH,CAAC,EAGD,MAAMskB,EAAepnB,EAAO,GAAIymB,GAAUE,CAAgB,EAG1D,OAAA7jB,EAAO,OAAS9C,EAAO,CAAA,EAAIonB,EAAcL,GAAkBphB,CAAM,EACjE7C,EAAO,eAAiB9C,EAAO,CAAA,EAAI8C,EAAO,MAAM,EAChDA,EAAO,aAAe9C,EAAO,CAAA,EAAI2F,CAAM,EAGnC7C,EAAO,QAAUA,EAAO,OAAO,IACjC,OAAO,KAAKA,EAAO,OAAO,EAAE,EAAE,QAAQukB,GAAa,CACjDvkB,EAAO,GAAGukB,EAAWvkB,EAAO,OAAO,GAAGukB,CAAS,CAAC,CAClD,CAAC,EAECvkB,EAAO,QAAUA,EAAO,OAAO,OACjCA,EAAO,MAAMA,EAAO,OAAO,KAAK,EAIlC,OAAO,OAAOA,EAAQ,CACpB,QAASA,EAAO,OAAO,QACvB,GAAAzB,EAEA,WAAY,CAAA,EAEZ,OAAQ,CAAA,EACR,WAAY,CAAA,EACZ,SAAU,CAAA,EACV,gBAAiB,CAAA,EAEjB,cAAe,CACb,OAAOyB,EAAO,OAAO,YAAc,YACrC,EACA,YAAa,CACX,OAAOA,EAAO,OAAO,YAAc,UACrC,EAEA,YAAa,EACb,UAAW,EAEX,YAAa,GACb,MAAO,GAEP,UAAW,EACX,kBAAmB,EACnB,SAAU,EACV,SAAU,EACV,UAAW,GACX,uBAAwB,CAGtB,OAAO,KAAK,MAAM,KAAK,UAAY,GAAK,EAAE,EAAI,GAAK,EACrD,EAEA,eAAgBA,EAAO,OAAO,eAC9B,eAAgBA,EAAO,OAAO,eAE9B,gBAAiB,CACf,UAAW,OACX,QAAS,OACT,oBAAqB,OACrB,eAAgB,OAChB,YAAa,OACb,iBAAkB,OAClB,eAAgB,OAChB,mBAAoB,OAEpB,kBAAmBA,EAAO,OAAO,kBAEjC,cAAe,EACf,aAAc,OAEd,WAAY,CAAA,EACZ,oBAAqB,OACrB,YAAa,OACb,UAAW,KACX,QAAS,IACjB,EAEM,WAAY,GAEZ,eAAgBA,EAAO,OAAO,eAC9B,QAAS,CACP,OAAQ,EACR,OAAQ,EACR,SAAU,EACV,SAAU,EACV,KAAM,CACd,EAEM,aAAc,CAAA,EACd,aAAc,CACpB,CAAK,EACDA,EAAO,KAAK,SAAS,EAGjBA,EAAO,OAAO,MAChBA,EAAO,KAAI,EAKNA,CACT,CACA,kBAAkBwkB,EAAU,CAC1B,OAAI,KAAK,eACAA,EAGF,CACL,MAAS,SACT,aAAc,cACd,iBAAkB,eAClB,cAAe,aACf,eAAgB,gBAChB,eAAgB,cAChB,gBAAiB,iBACjB,YAAe,cACrB,EAAMA,CAAQ,CACZ,CACA,cAAc7b,EAAS,CACrB,KAAM,CACJ,SAAA2I,EACA,OAAAzO,CACN,EAAQ,KACE8O,EAAS7Q,EAAgBwQ,EAAU,IAAIzO,EAAO,UAAU,gBAAgB,EACxEkT,EAAkB9T,GAAa0P,EAAO,CAAC,CAAC,EAC9C,OAAO1P,GAAa0G,CAAO,EAAIoN,CACjC,CACA,oBAAoBtQ,EAAO,CACzB,OAAO,KAAK,cAAc,KAAK,OAAO,KAAKkD,GAAWA,EAAQ,aAAa,yBAAyB,EAAI,IAAMlD,CAAK,CAAC,CACtH,CACA,sBAAsBA,EAAO,CAC3B,OAAI,KAAK,MAAQ,KAAK,OAAO,MAAQ,KAAK,OAAO,KAAK,KAAO,IACvD,KAAK,OAAO,KAAK,OAAS,SAC5BA,EAAQ,KAAK,MAAMA,EAAQ,KAAK,OAAO,KAAK,IAAI,EACvC,KAAK,OAAO,KAAK,OAAS,QACnCA,EAAQA,EAAQ,KAAK,KAAK,KAAK,OAAO,OAAS,KAAK,OAAO,KAAK,IAAI,IAGjEA,CACT,CACA,cAAe,CACb,MAAMzF,EAAS,KACT,CACJ,SAAAsR,EACA,OAAAzO,CACN,EAAQ7C,EACJA,EAAO,OAASc,EAAgBwQ,EAAU,IAAIzO,EAAO,UAAU,gBAAgB,CACjF,CACA,QAAS,CACP,MAAM7C,EAAS,KACXA,EAAO,UACXA,EAAO,QAAU,GACbA,EAAO,OAAO,YAChBA,EAAO,cAAa,EAEtBA,EAAO,KAAK,QAAQ,EACtB,CACA,SAAU,CACR,MAAMA,EAAS,KACVA,EAAO,UACZA,EAAO,QAAU,GACbA,EAAO,OAAO,YAChBA,EAAO,gBAAe,EAExBA,EAAO,KAAK,SAAS,EACvB,CACA,YAAYW,EAAUoI,EAAO,CAC3B,MAAM/I,EAAS,KACfW,EAAW,KAAK,IAAI,KAAK,IAAIA,EAAU,CAAC,EAAG,CAAC,EAC5C,MAAM8jB,EAAMzkB,EAAO,aAAY,EAEzBS,GADMT,EAAO,aAAY,EACRykB,GAAO9jB,EAAW8jB,EACzCzkB,EAAO,YAAYS,EAAS,OAAOsI,EAAU,IAAc,EAAIA,CAAK,EACpE/I,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,CAC5B,CACA,sBAAuB,CACrB,MAAMA,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,cAAgB,CAACA,EAAO,GAAI,OAC/C,MAAM0kB,EAAM1kB,EAAO,GAAG,UAAU,MAAM,GAAG,EAAE,OAAOoL,GACzCA,EAAU,QAAQ,QAAQ,IAAM,GAAKA,EAAU,QAAQpL,EAAO,OAAO,sBAAsB,IAAM,CACzG,EACDA,EAAO,KAAK,oBAAqB0kB,EAAI,KAAK,GAAG,CAAC,CAChD,CACA,gBAAgB/b,EAAS,CACvB,MAAM3I,EAAS,KACf,OAAIA,EAAO,UAAkB,GACtB2I,EAAQ,UAAU,MAAM,GAAG,EAAE,OAAOyC,GAClCA,EAAU,QAAQ,cAAc,IAAM,GAAKA,EAAU,QAAQpL,EAAO,OAAO,UAAU,IAAM,CACnG,EAAE,KAAK,GAAG,CACb,CACA,mBAAoB,CAClB,MAAMA,EAAS,KACf,GAAI,CAACA,EAAO,OAAO,cAAgB,CAACA,EAAO,GAAI,OAC/C,MAAM2kB,EAAU,CAAA,EAChB3kB,EAAO,OAAO,QAAQ2I,GAAW,CAC/B,MAAMua,EAAaljB,EAAO,gBAAgB2I,CAAO,EACjDgc,EAAQ,KAAK,CACX,QAAAhc,EACA,WAAAua,CACR,CAAO,EACDljB,EAAO,KAAK,cAAe2I,EAASua,CAAU,CAChD,CAAC,EACDljB,EAAO,KAAK,gBAAiB2kB,CAAO,CACtC,CACA,qBAAqBC,EAAMC,EAAO,CAC5BD,IAAS,SACXA,EAAO,WAELC,IAAU,SACZA,EAAQ,IAEV,MAAM7kB,EAAS,KACT,CACJ,OAAA6C,EACA,OAAA8O,EACA,WAAAE,EACA,gBAAAC,EACA,KAAMP,EACN,YAAAgF,CACN,EAAQvW,EACJ,IAAI8kB,EAAM,EACV,GAAI,OAAOjiB,EAAO,eAAkB,SAAU,OAAOA,EAAO,cAC5D,GAAIA,EAAO,eAAgB,CACzB,IAAI0P,EAAYZ,EAAO4E,CAAW,EAAI,KAAK,KAAK5E,EAAO4E,CAAW,EAAE,eAAe,EAAI,EACnFwO,EACJ,QAAS3lB,EAAImX,EAAc,EAAGnX,EAAIuS,EAAO,OAAQvS,GAAK,EAChDuS,EAAOvS,CAAC,GAAK,CAAC2lB,IAChBxS,GAAa,KAAK,KAAKZ,EAAOvS,CAAC,EAAE,eAAe,EAChD0lB,GAAO,EACHvS,EAAYhB,IAAYwT,EAAY,KAG5C,QAAS3lB,EAAImX,EAAc,EAAGnX,GAAK,EAAGA,GAAK,EACrCuS,EAAOvS,CAAC,GAAK,CAAC2lB,IAChBxS,GAAaZ,EAAOvS,CAAC,EAAE,gBACvB0lB,GAAO,EACHvS,EAAYhB,IAAYwT,EAAY,IAG9C,SAEMH,IAAS,UACX,QAASxlB,EAAImX,EAAc,EAAGnX,EAAIuS,EAAO,OAAQvS,GAAK,GAChCylB,EAAQhT,EAAWzS,CAAC,EAAI0S,EAAgB1S,CAAC,EAAIyS,EAAW0E,CAAW,EAAIhF,EAAaM,EAAWzS,CAAC,EAAIyS,EAAW0E,CAAW,EAAIhF,KAEhJuT,GAAO,OAKX,SAAS1lB,EAAImX,EAAc,EAAGnX,GAAK,EAAGA,GAAK,EACrByS,EAAW0E,CAAW,EAAI1E,EAAWzS,CAAC,EAAImS,IAE5DuT,GAAO,GAKf,OAAOA,CACT,CACA,QAAS,CACP,MAAM9kB,EAAS,KACf,GAAI,CAACA,GAAUA,EAAO,UAAW,OACjC,KAAM,CACJ,SAAA4R,EACA,OAAA/O,CACN,EAAQ7C,EAEA6C,EAAO,aACT7C,EAAO,cAAa,EAEtB,CAAC,GAAGA,EAAO,GAAG,iBAAiB,kBAAkB,CAAC,EAAE,QAAQ6W,GAAW,CACjEA,EAAQ,UACVD,GAAqB5W,EAAQ6W,CAAO,CAExC,CAAC,EACD7W,EAAO,WAAU,EACjBA,EAAO,aAAY,EACnBA,EAAO,eAAc,EACrBA,EAAO,oBAAmB,EAC1B,SAASsY,GAAe,CACtB,MAAM0M,EAAiBhlB,EAAO,aAAeA,EAAO,UAAY,GAAKA,EAAO,UACtEiZ,EAAe,KAAK,IAAI,KAAK,IAAI+L,EAAgBhlB,EAAO,aAAY,CAAE,EAAGA,EAAO,aAAY,CAAE,EACpGA,EAAO,aAAaiZ,CAAY,EAChCjZ,EAAO,kBAAiB,EACxBA,EAAO,oBAAmB,CAC5B,CACA,IAAIilB,EACJ,GAAIpiB,EAAO,UAAYA,EAAO,SAAS,SAAW,CAACA,EAAO,QACxDyV,EAAY,EACRzV,EAAO,YACT7C,EAAO,iBAAgB,MAEpB,CACL,IAAK6C,EAAO,gBAAkB,QAAUA,EAAO,cAAgB,IAAM7C,EAAO,OAAS,CAAC6C,EAAO,eAAgB,CAC3G,MAAM8O,EAAS3R,EAAO,SAAW6C,EAAO,QAAQ,QAAU7C,EAAO,QAAQ,OAASA,EAAO,OACzFilB,EAAajlB,EAAO,QAAQ2R,EAAO,OAAS,EAAG,EAAG,GAAO,EAAI,CAC/D,MACEsT,EAAajlB,EAAO,QAAQA,EAAO,YAAa,EAAG,GAAO,EAAI,EAE3DilB,GACH3M,EAAY,CAEhB,CACIzV,EAAO,eAAiB+O,IAAa5R,EAAO,UAC9CA,EAAO,cAAa,EAEtBA,EAAO,KAAK,QAAQ,CACtB,CACA,gBAAgBklB,EAAcC,EAAY,CACpCA,IAAe,SACjBA,EAAa,IAEf,MAAMnlB,EAAS,KACTolB,EAAmBplB,EAAO,OAAO,UAKvC,OAJKklB,IAEHA,EAAeE,IAAqB,aAAe,WAAa,cAE9DF,IAAiBE,GAAoBF,IAAiB,cAAgBA,IAAiB,aAG3FllB,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,sBAAsB,GAAGolB,CAAgB,EAAE,EACvFplB,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,sBAAsB,GAAGklB,CAAY,EAAE,EAChFllB,EAAO,qBAAoB,EAC3BA,EAAO,OAAO,UAAYklB,EAC1BllB,EAAO,OAAO,QAAQ2I,GAAW,CAC3Buc,IAAiB,WACnBvc,EAAQ,MAAM,MAAQ,GAEtBA,EAAQ,MAAM,OAAS,EAE3B,CAAC,EACD3I,EAAO,KAAK,iBAAiB,EACzBmlB,GAAYnlB,EAAO,OAAM,GACtBA,CACT,CACA,wBAAwBqZ,EAAW,CACjC,MAAMrZ,EAAS,KACXA,EAAO,KAAOqZ,IAAc,OAAS,CAACrZ,EAAO,KAAOqZ,IAAc,QACtErZ,EAAO,IAAMqZ,IAAc,MAC3BrZ,EAAO,aAAeA,EAAO,OAAO,YAAc,cAAgBA,EAAO,IACrEA,EAAO,KACTA,EAAO,GAAG,UAAU,IAAI,GAAGA,EAAO,OAAO,sBAAsB,KAAK,EACpEA,EAAO,GAAG,IAAM,QAEhBA,EAAO,GAAG,UAAU,OAAO,GAAGA,EAAO,OAAO,sBAAsB,KAAK,EACvEA,EAAO,GAAG,IAAM,OAElBA,EAAO,OAAM,EACf,CACA,MAAMlE,EAAS,CACb,MAAMkE,EAAS,KACf,GAAIA,EAAO,QAAS,MAAO,GAG3B,IAAIzB,EAAKzC,GAAWkE,EAAO,OAAO,GAIlC,GAHI,OAAOzB,GAAO,WAChBA,EAAK,SAAS,cAAcA,CAAE,GAE5B,CAACA,EACH,MAAO,GAETA,EAAG,OAASyB,EACRzB,EAAG,YAAcA,EAAG,WAAW,MAAQA,EAAG,WAAW,KAAK,WAAayB,EAAO,OAAO,sBAAsB,YAAW,IACxHA,EAAO,UAAY,IAErB,MAAMqlB,EAAqB,IAClB,KAAKrlB,EAAO,OAAO,cAAgB,IAAI,KAAI,EAAG,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC,GAW3E,IAAIqR,EARE9S,GAAMA,EAAG,YAAcA,EAAG,WAAW,cAC3BA,EAAG,WAAW,cAAc8mB,EAAkB,CAAE,EAIvDvkB,EAAgBvC,EAAI8mB,EAAkB,CAAE,EAAE,CAAC,EAIpD,MAAI,CAAChU,GAAarR,EAAO,OAAO,iBAC9BqR,EAAY3V,GAAc,MAAOsE,EAAO,OAAO,YAAY,EAC3DzB,EAAG,OAAO8S,CAAS,EACnBvQ,EAAgBvC,EAAI,IAAIyB,EAAO,OAAO,UAAU,EAAE,EAAE,QAAQ2I,GAAW,CACrE0I,EAAU,OAAO1I,CAAO,CAC1B,CAAC,GAEH,OAAO,OAAO3I,EAAQ,CACpB,GAAAzB,EACA,UAAA8S,EACA,SAAUrR,EAAO,WAAa,CAACzB,EAAG,WAAW,KAAK,WAAaA,EAAG,WAAW,KAAO8S,EACpF,OAAQrR,EAAO,UAAYzB,EAAG,WAAW,KAAOA,EAChD,QAAS,GAET,IAAKA,EAAG,IAAI,YAAW,IAAO,OAASwD,EAAaxD,EAAI,WAAW,IAAM,MACzE,aAAcyB,EAAO,OAAO,YAAc,eAAiBzB,EAAG,IAAI,YAAW,IAAO,OAASwD,EAAaxD,EAAI,WAAW,IAAM,OAC/H,SAAUwD,EAAasP,EAAW,SAAS,IAAM,aACvD,CAAK,EACM,EACT,CACA,KAAK9S,EAAI,CACP,MAAMyB,EAAS,KAGf,GAFIA,EAAO,aACKA,EAAO,MAAMzB,CAAE,IACf,GAAO,OAAOyB,EAC9BA,EAAO,KAAK,YAAY,EAGpBA,EAAO,OAAO,aAChBA,EAAO,cAAa,EAItBA,EAAO,WAAU,EAGjBA,EAAO,WAAU,EAGjBA,EAAO,aAAY,EACfA,EAAO,OAAO,eAChBA,EAAO,cAAa,EAIlBA,EAAO,OAAO,YAAcA,EAAO,SACrCA,EAAO,cAAa,EAIlBA,EAAO,OAAO,MAAQA,EAAO,SAAWA,EAAO,OAAO,QAAQ,QAChEA,EAAO,QAAQA,EAAO,OAAO,aAAeA,EAAO,QAAQ,aAAc,EAAGA,EAAO,OAAO,mBAAoB,GAAO,EAAI,EAEzHA,EAAO,QAAQA,EAAO,OAAO,aAAc,EAAGA,EAAO,OAAO,mBAAoB,GAAO,EAAI,EAIzFA,EAAO,OAAO,MAChBA,EAAO,WAAW,OAAW,EAAI,EAInCA,EAAO,aAAY,EACnB,MAAMslB,EAAe,CAAC,GAAGtlB,EAAO,GAAG,iBAAiB,kBAAkB,CAAC,EACvE,OAAIA,EAAO,WACTslB,EAAa,KAAK,GAAGtlB,EAAO,OAAO,iBAAiB,kBAAkB,CAAC,EAEzEslB,EAAa,QAAQzO,GAAW,CAC1BA,EAAQ,SACVD,GAAqB5W,EAAQ6W,CAAO,EAEpCA,EAAQ,iBAAiB,OAAQjT,GAAK,CACpCgT,GAAqB5W,EAAQ4D,EAAE,MAAM,CACvC,CAAC,CAEL,CAAC,EACDqT,GAAQjX,CAAM,EAGdA,EAAO,YAAc,GACrBiX,GAAQjX,CAAM,EAGdA,EAAO,KAAK,MAAM,EAClBA,EAAO,KAAK,WAAW,EAChBA,CACT,CACA,QAAQulB,EAAgBC,EAAa,CAC/BD,IAAmB,SACrBA,EAAiB,IAEfC,IAAgB,SAClBA,EAAc,IAEhB,MAAMxlB,EAAS,KACT,CACJ,OAAA6C,EACA,GAAAtE,EACA,UAAA8S,EACA,OAAAM,CACN,EAAQ3R,EACJ,OAAI,OAAOA,EAAO,OAAW,KAAeA,EAAO,YAGnDA,EAAO,KAAK,eAAe,EAG3BA,EAAO,YAAc,GAGrBA,EAAO,aAAY,EAGf6C,EAAO,MACT7C,EAAO,YAAW,EAIhBwlB,IACFxlB,EAAO,cAAa,EAChBzB,GAAM,OAAOA,GAAO,UACtBA,EAAG,gBAAgB,OAAO,EAExB8S,GACFA,EAAU,gBAAgB,OAAO,EAE/BM,GAAUA,EAAO,QACnBA,EAAO,QAAQhJ,GAAW,CACxBA,EAAQ,UAAU,OAAO9F,EAAO,kBAAmBA,EAAO,uBAAwBA,EAAO,iBAAkBA,EAAO,eAAgBA,EAAO,cAAc,EACvJ8F,EAAQ,gBAAgB,OAAO,EAC/BA,EAAQ,gBAAgB,yBAAyB,CACnD,CAAC,GAGL3I,EAAO,KAAK,SAAS,EAGrB,OAAO,KAAKA,EAAO,eAAe,EAAE,QAAQukB,GAAa,CACvDvkB,EAAO,IAAIukB,CAAS,CACtB,CAAC,EACGgB,IAAmB,KACjBvlB,EAAO,IAAM,OAAOA,EAAO,IAAO,WACpCA,EAAO,GAAG,OAAS,MAErB/B,GAAY+B,CAAM,GAEpBA,EAAO,UAAY,IACZ,IACT,CACA,OAAO,eAAeylB,EAAa,CACjCvoB,EAAO+mB,GAAkBwB,CAAW,CACtC,CACA,WAAW,kBAAmB,CAC5B,OAAOxB,EACT,CACA,WAAW,UAAW,CACpB,OAAON,EACT,CACA,OAAO,cAAcU,EAAK,CACnBH,EAAO,UAAU,cAAaA,EAAO,UAAU,YAAc,CAAA,GAClE,MAAMwB,EAAUxB,EAAO,UAAU,YAC7B,OAAOG,GAAQ,YAAcqB,EAAQ,QAAQrB,CAAG,EAAI,GACtDqB,EAAQ,KAAKrB,CAAG,CAEpB,CACA,OAAO,IAAIsB,EAAQ,CACjB,OAAI,MAAM,QAAQA,CAAM,GACtBA,EAAO,QAAQC,GAAK1B,EAAO,cAAc0B,CAAC,CAAC,EACpC1B,IAETA,EAAO,cAAcyB,CAAM,EACpBzB,EACT,CACF,CACA,OAAO,KAAKF,EAAU,EAAE,QAAQ6B,GAAkB,CAChD,OAAO,KAAK7B,GAAW6B,CAAc,CAAC,EAAE,QAAQC,GAAe,CAC7D5B,EAAO,UAAU4B,CAAW,EAAI9B,GAAW6B,CAAc,EAAEC,CAAW,CACxE,CAAC,CACH,CAAC,EACD5B,EAAO,IAAI,CAACrV,GAAQc,EAAQ,CAAC,ECh2HtB,MAAMoW,EAA0D,CAAhE,aAAA,CAEL,KAAQ,YAAc,EAAA,CAKtB,WAAW1b,EAAsD,CAC/D,GAAI,CAEF,YAAK,QAAA,EAGL,KAAK,eAAiB,IAAI6Z,EAAO,IAAInnB,EAAc,OAAO,IAAI,WAAW,GAAIsN,CAAM,EACnF,KAAK,YAAc,GAEZ,KAAK,cACd,MAAQ,CAEN,MACF,CACF,CAKA,SAAgB,CACd,GAAI,KAAK,gBAAkB,CAAC,KAAK,YAC/B,GAAI,CACF,KAAK,eAAe,QAAQ,GAAM,EAAI,CACxC,MAAQ,CAER,QAAA,CACE,OAAO,KAAK,eACZ,KAAK,YAAc,EACrB,CAEJ,CAKA,eAAyB,CACvB,OAAO,KAAK,iBAAmB,MAAQ,CAAC,KAAK,WAC/C,CAKA,aAA0C,CACxC,GAAI,KAAK,gBACP,OAAO,KAAK,cAGhB,CAKA,aAAaA,EAAiD,CAC5D,YAAK,QAAA,EACE,KAAK,WAAWA,CAAM,CAC/B,CAKA,sBAAgC,CAC9B,GAAI,CAEF,OADkB,SAAS,cAAc,IAAItN,EAAc,OAAO,IAAI,WAAW,EAAE,IAC9D,IACvB,MAAQ,CACN,MAAO,EACT,CACF,CAKA,oBAAoBsN,EAA0BjM,EAAQ1B,GAAO,eAAsB,CACjF,WAAW,IAAM,CACX,KAAK,wBACP,KAAK,WAAW2N,CAAM,CAE1B,EAAGjM,CAAK,CACV,CAKA,WAKE,CACA,MAAO,CACL,cAAe,KAAK,cAAA,EACpB,YAAa,KAAK,YAClB,YAAa,KAAK,iBAAmB,KACrC,mBAAoB,KAAK,qBAAA,CAAqB,CAElD,CACF,CCvGO,MAAM4nB,EAAiB,CAO1B,aAAc,CAFd,KAAiB,UAAYjpB,EAAc,OAAO,UAG9C,KAAK,cAAgB,IAAIgN,GACzB,KAAK,WAAa,IAAIkB,GACtB,KAAK,YAAc,IAAIoB,GACvB,KAAK,iBAAmB,IAAI0Z,EAChC,CAKA,sBAAsBE,EAA0B,CAC5C,GAAI,CACA,KAAK,QAAA,EAGL,MAAMC,EAAY,KAAK,YAAY,eAAA,EACnC,GAAIA,EAAU,SAAWzpB,EAAgB,aACrC,OAIJ,MAAMyO,EAAY,KAAK,WAAW,gBAAA,EAC5BlL,EAAS,KAAK,WAAW,oBAAoBkL,CAAS,EACtDI,EAAU,KAAK,WAAW,oBAAoBtL,CAAM,EAGpDgK,EAAa,KAAK,eAAesB,EAAS4a,CAAS,EACzD,KAAK,WAAW,iBAAiBlmB,CAAM,EACvC,KAAK,WAAW,iBAAiBA,CAAM,EAGvC,KAAK,WAAW,YAAYkL,CAAS,EAGrC,WAAW,IAAM,CACb,KAAK,iBAAiBlB,CAAU,CACpC,EAAG,KAAK,SAAS,CACrB,MAAQ,CAER,CACJ,CAKQ,eAAesB,EAAsB4a,EAAyC,CAClF,IAAIlc,EAAavN,EAAgB,aAEjC,UAAWgP,KAASya,EAChB,GAAIza,EAAM,QAAS,CACf,MAAM0a,EAAe,KAAK,WAAW,YAAY1a,EAAM,SAAUA,EAAM,SAAS,EAChFJ,EAAqBC,EAAS6a,CAAY,EAC1Cnc,GAAcxN,EAAoB,eACtC,CAGJ,OAAOwN,CACX,CAOQ,iBAAiBA,EAA0B,CAC/C,GAAI,CACA,MAAMC,EAAiB,KAAK,YAAY,kBAAA,EAClCmc,EAAe,KAAK,cAAc,uBAAuBpc,EAAYC,CAAc,EAIzF,GAAI,CADqB,KAAK,cAAc,sBAAsBmc,EAAa,WAAW,EACpE,QAClB,OAIJ,KAAK,iBAAiB,WAAWA,EAAa,WAAW,CAC7D,MAAQ,CAER,CACJ,CAKA,SAAgB,CACZ,KAAK,iBAAiB,QAAA,EACtB,KAAK,WAAW,QAAA,CACpB,CAEJ,CCxGO,MAAMC,CAAa,CAKd,aAAc,CAHtB,KAAQ,SAA4B,CAAA,EACpC,KAAQ,cAAgB,EAIxB,CAKA,OAAc,aAA4B,CACtC,OAAKA,EAAa,WACdA,EAAa,SAAW,IAAIA,GAEzBA,EAAa,QACxB,CAKO,YAAsB,CACzB,GAAI,CACA,OAAI,KAAK,gBAKT,KAAK,yBAAA,EACL,KAAK,cAAgB,IACd,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CAKO,WAAoBC,EAAmBxV,EAAkC,CAC5E,GAAI,CACA,OAAOwV,EAAA,CACX,OAASC,EAAO,CACZ,YAAK,SAASA,EAAgBzV,CAAO,EAC9B,EACX,CACJ,CAKO,YAAqBwV,EAA4BxV,EAA2C,CAC/F,OAAOwV,EAAA,EAAK,MAAOC,IACf,KAAK,SAASA,EAAgBzV,CAAO,EAC9B,GACV,CACL,CAKQ,SAASyV,EAAczV,EAAuB,CAClD,GAAI,CACA,MAAM0V,EAAuB,CACzB,cAAe,KACf,MAAAD,EACA,QAAAzV,CAAA,EAGJ,KAAK,SAAS,KAAK0V,CAAK,EAGpB,KAAK,SAAS,OAASjqB,GAAe,uBACtC,KAAK,SAAS,MAAA,CAQtB,MAAQ,CAER,CACJ,CAKQ,0BAAiC,CACrC,GAAI,CAEA,WAAW,iBAAiB,qBAAuBiU,GAAU,CACzD,KAAK,SACD,IAAI,MAAM,OAAOA,EAAM,MAAM,CAAC,EAC9B,6BAAA,CAER,CAAC,CACL,MAAQ,CAER,CACJ,CAKO,aAA+B,CAClC,MAAO,CAAC,GAAG,KAAK,QAAQ,CAC5B,CAKO,eAAsB,CACzB,KAAK,SAAW,CAAA,CACpB,CACJ,CCrHO,MAAMiW,CAAc,CAGf,aAAc,CAEtB,CAKA,OAAc,aAA6B,CACvC,OAAKA,EAAc,WACfA,EAAc,SAAW,IAAIA,GAE1BA,EAAc,QACzB,CAKO,YAAsB,CACzB,GAAI,CAEA,OADqB7Z,EAAI,QAAQ,IAAI,WAAW,IACxB,MAC5B,MAAQ,CAEJ,GAAI,CACA,OAAO,WAAW,SAAS,SAAS,SAAS,OAAO,CACxD,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CAKO,WAAkC,CACrC,OAAO7P,CACX,CAKO,uBAAiC,CACpC,GAAI,CAIA,QAASwP,EAAa,EAAmBA,GAAcxP,EAAc,OAAO,UAAWwP,GAAc,EAEjG,GADcK,EAAI,MAAM,UAAU,wCAAwCL,CAAU,EAAE,EAElF,MAAO,GAGf,MAAO,EACX,MAAQ,CACJ,MAAO,EACX,CACJ,CACJ,CCpDAK,EAAI,aAAa,IAAI7P,EAAc,IAAI,YAAa,IAAM,CACtD,MAAM2pB,EAAeL,EAAa,YAAA,EAC5BM,EAAgBF,EAAc,YAAA,EAGpC,GAAI,CAACC,EAAa,aACd,OAGJ,MAAME,EAAmB,IAAIZ,GAG7B9oB,GAAAA,OAAO2pB,GAAc,UAAW,OAAQ,SAAoCC,EAAgB,CACxFJ,EAAa,WAAW,IAAM,CACtBC,EAAc,cACdI,GAAoBD,EAAOF,CAA2B,CAE9D,EAAG,8BAA8B,CACrC,CAAC,CACL,CAAC,EAKD,MAAMG,GAAsB,CACxBD,EACAF,EACAI,IACO,CACP,GAAI,CAIA,GAHsBP,EAAc,YAAA,EAGlB,wBACd,GAAI,CACAG,EAAiB,sBAAsBE,CAAK,CAChD,MAAQ,CAER,CAMA,CAACla,EAAI,QAAQ,MAAQ7B,MACrBkc,GAAA,CAGR,MAAQ,CAER,CACJ,EAOMA,GAAgB,IAAY,CAC9B,IAAIC,EAAsB,SAAS,eAAenqB,EAAc,GAAG,YAAY,EAE/E,GAAImqB,IAAwB,KAAM,CAE9B,MAAMC,EAAgBva,EAAI,MAAM,UAAU,+CAA+C,GAAK7P,EAAc,GAAG,cAE/GmqB,EAAsB,SAAS,cAAc,KAAK,EAClDA,EAAoB,GAAKnqB,EAAc,GAAG,aAC1CmqB,EAAoB,UAAY,mCAChCA,EAAoB,UAAY,aAAaC,CAAa,kDAG1D,MAAMC,EAAsB,SAAS,cAAc,yDAAyD,EACxGA,GAEAA,EAAoB,YAAYF,CAAmB,CAE3D,CACJ", "x_google_ignoreList": [3, 4, 5, 6, 7, 8, 9, 14]}