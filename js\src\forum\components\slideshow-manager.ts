import * as DOMUtils from '../utils/dom-utils';
import { defaultConfig } from '../../common/config';
import { ARRAY_CONSTANTS, SLIDESHOW_CONSTANTS } from '../../common/config/constants';
import { SwiperConfigManager } from './swiper/swiper-config-manager';
import { SwiperDOMBuilder } from './swiper/swiper-dom-builder';
import { SlideDataManager } from './swiper/slide-data-manager';
import { SwiperLifecycleManager } from './swiper/swiper-lifecycle-manager';
import type { FlarumVnode, ProcessedSlideData } from '../../common/config/types';

/**
 * Slideshow manager for header advertisements
 * Coordinates between different modules to manage the complete slideshow lifecycle
 */
export class SlideshowManager {
    private readonly configManager: SwiperConfigManager;
    private readonly domBuilder: SwiperDOMBuilder;
    private readonly dataManager: SlideDataManager;
    private readonly lifecycleManager: SwiperLifecycleManager;
    private readonly checkTime = defaultConfig.slider.checkTime;

    constructor() {
        this.configManager = new SwiperConfigManager();
        this.domBuilder = new SwiperDOMBuilder();
        this.dataManager = new SlideDataManager();
        this.lifecycleManager = new SwiperLifecycleManager();
    }

    /**
     * Initialize and attach slideshow to the DOM
     */
    attachAdvertiseHeader(_vdom: FlarumVnode): void {
        try {
            this.destroy(); // Clean up any existing instance

            // Fetch slide data
            const slideData = this.dataManager.fetchSlideData();
            if (slideData.length === ARRAY_CONSTANTS.EMPTY_LENGTH) {
                return; // No slides to display
            }

            // Build DOM structure
            const container = this.domBuilder.createContainer();
            const swiper = this.domBuilder.createSwiperElement(container);
            const wrapper = this.domBuilder.createSwiperWrapper(swiper);

            // Populate slides
            const slideCount = this.populateSlides(wrapper, slideData);
            this.domBuilder.createPagination(swiper);
            this.domBuilder.createNavigation(swiper);

            // Append to DOM
            this.domBuilder.appendToDOM(container);

            // Initialize Swiper after DOM attachment
            setTimeout(() => {
                this.initializeSwiper(slideCount);
            }, this.checkTime);
        } catch {
            // Silently handle slideshow creation errors
        }
    }

    /**
     * Populate slides with processed slide data
     */
    private populateSlides(wrapper: HTMLElement, slideData: ProcessedSlideData[]): number {
        let slideCount = ARRAY_CONSTANTS.EMPTY_LENGTH;

        for (const slide of slideData) {
            if (slide.isValid) {
                const slideElement = this.domBuilder.createSlide(slide.imageSrc, slide.imageLink);
                DOMUtils.appendChild(wrapper, slideElement);
                slideCount += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT;
            }
        }

        return slideCount;
    }



    /**
     * Initialize Swiper with calculated configuration
     */
    private initializeSwiper(slideCount: number): void {
        try {
            const transitionTime = this.dataManager.getTransitionTime();
            const configResult = this.configManager.calculateConfiguration(slideCount, transitionTime);

            // Validate configuration
            const validationResult = this.configManager.validateConfiguration(configResult.finalConfig);
            if (!validationResult.isValid) {
                return;
            }

            // Initialize Swiper instance
            this.lifecycleManager.initialize(configResult.finalConfig);
        } catch {
            // Silently handle Swiper initialization errors
        }
    }

    /**
     * Destroy slideshow instance
     */
    destroy(): void {
        this.lifecycleManager.destroy();
        this.domBuilder.cleanup();
    }

}
