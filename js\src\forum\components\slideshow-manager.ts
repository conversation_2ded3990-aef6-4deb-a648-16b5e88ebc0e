import Swiper from 'swiper';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import app from 'flarum/forum/app';
import * as DOMUtils from '../utils/dom-utils';
import { isMobileDevice } from '../utils/mobile-detection';
import { defaultConfig } from '../../common/config';
import { SLIDESHOW_CONSTANTS } from '../../common/config/constants';
import type { FlarumVnode } from '../../common/config/types';

/**
 * Swiper configuration constants
 * Based on Swiper documentation requirements for loop mode
 */
const SWIPER_CONFIG_CONSTANTS = {
    MIN_SLIDES_FOR_DUAL_VIEW: 8,
    // For slidesPerView: 1.2 with centeredSlides, need at least 8 slides for stable loop
    // This is more conservative to avoid the loop warning
    MIN_SLIDES_FOR_PARTIAL_VIEW_LOOP: 8,
    // For slidesPerView: 1 with centeredSlides, need at least 3 slides
    MIN_SLIDES_FOR_SINGLE_VIEW_LOOP: 3,
    // Responsive slidesPerView values
    MOBILE_SLIDES_PER_VIEW: 1,
    TABLET_SLIDES_PER_VIEW_WITH_LOOP: 1.1,
    TABLET_SLIDES_PER_VIEW_NO_LOOP: 1,
    FALLBACK_SLIDES_PER_VIEW: 1,
} as const;



/**
 * Slideshow manager for header advertisements
 */
export class SlideshowManager {
    private swiper: Swiper | undefined;
    private container: HTMLElement | undefined;
    private readonly maxSlides = defaultConfig.slider.maxSlides;
    private readonly checkTime = defaultConfig.slider.checkTime;

    /**
     * Safely read a forum attribute if available
     */
    private getForumAttribute(key: string): unknown {
        try {
            const forum = app && app.forum;
            const attrFn = forum && forum.attribute;
            if (typeof attrFn === 'function') {
                return attrFn.call(forum, key);
            }
            return;
        } catch {
            return;
        }
    }

    /**
     * Initialize and attach slideshow to the DOM
     */
    attachAdvertiseHeader(_vdom: FlarumVnode): void {
        try {
            this.destroy(); // Clean up any existing instance

            const container = this.createContainer();
            const swiper = this.createSwiperElement(container);
            const wrapper = this.createSwiperWrapper(swiper);

            const slideCount = this.populateSlides(wrapper);
            this.createPagination(swiper);
            this.createNavigation(swiper);

            this.container = container;
            this.appendToDOM(container);

            // Initialize Swiper after DOM attachment with slide count
            setTimeout(() => {
                this.initializeSwiper(this.getTransitionTime(), slideCount);
            }, this.checkTime);
        } catch {
            // Silently handle slideshow creation errors
        }
    }

    /**
     * Remove existing navigation elements
     */
    private removeExistingNavigation(): void {
        const existingContainer = DOMUtils.querySelector(`#${defaultConfig.slider.dom.containerId}`);
        if (existingContainer) {
            DOMUtils.removeElement(existingContainer);
        }

        const navElements = DOMUtils.querySelectorAll(".item-nav");
        for (const element of navElements) {
            DOMUtils.removeElement(element);
        }
    }

    /**
     * Create main container element
     * @returns Container element
     */
    private createContainer(): HTMLElement {
        this.removeExistingNavigation();

        const container = DOMUtils.createElement('div', {
            id: defaultConfig.slider.dom.containerId,
            className: 'adContainer adContainer--forced'
        });

        return container;
    }



    /**
     * Create Swiper element
     * @param {HTMLElement} container - Parent container
     * @returns {HTMLElement} Swiper element
     */
    private createSwiperElement(container: HTMLElement): HTMLElement {
        let className = `swiper ${defaultConfig.slider.dom.swiperClass} adSwiper--forced`;

        // Add mobile-specific class if on mobile device
        if (isMobileDevice()) {
            className += ' adSwiper--mobile';
        }

        const swiper = DOMUtils.createElement('div', {
            className
        });

        DOMUtils.appendChild(container, swiper);
        return swiper;
    }

    /**
     * Create Swiper wrapper
     * @param {HTMLElement} swiper - Swiper element
     * @returns {HTMLElement} Wrapper element
     */
    private createSwiperWrapper(swiper: HTMLElement): HTMLElement {
        const wrapper = DOMUtils.createElement('div', {
            className: 'swiper-wrapper swiper-wrapper--forced'
        });

        DOMUtils.appendChild(swiper, wrapper);
        return wrapper;
    }

    /**
     * Get transition time from forum settings
     * @returns Transition time in milliseconds
     */
    private getTransitionTime(): number {
        const transitionTime = this.getForumAttribute('wusong8899-header-advertisement.TransitionTime');
        if (transitionTime) {
            return Number.parseInt(String(transitionTime), 10);
        }
        return defaultConfig.slider.defaultTransitionTime;
    }

    /**
     * Populate slides with data from forum settings
     * @param {HTMLElement} wrapper - Swiper wrapper element
     * @returns {number} Number of slides created
     */
    private populateSlides(wrapper: HTMLElement): number {
        let slideCount = 0;
        for (let slideIndex = SLIDESHOW_CONSTANTS.INITIAL_SLIDE_INDEX; slideIndex <= this.maxSlides; slideIndex += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT) {
            const imageSrc = this.getForumAttribute(`wusong8899-header-advertisement.Image${slideIndex}`);
            const imageLink = this.getForumAttribute(`wusong8899-header-advertisement.Link${slideIndex}`);

            if (imageSrc) {
                const slide = this.createSlide(String(imageSrc), String(imageLink || ''));
                DOMUtils.appendChild(wrapper, slide);
                slideCount += SLIDESHOW_CONSTANTS.SLIDE_INCREMENT;
            }
        }
        return slideCount;
    }

    /**
     * Create individual slide
     * @param {string} imageSrc - Image source URL
     * @param {string} imageLink - Link URL
     * @returns {HTMLElement} Slide element
     */
    private createSlide(imageSrc: string, imageLink: string): HTMLElement {
        const slide = DOMUtils.createElement('div', {
            className: 'swiper-slide swiper-slide--forced'
        });

        let clickHandler = '';
        if (imageLink) {
            clickHandler = `window.location.href="${imageLink}"`;
        }

        // Create image with CSS class instead of inline styles
        slide.innerHTML = `<img onclick='${clickHandler}' src='${imageSrc}' class='swiper-slide__image' />`;

        return slide;
    }

    /**
     * Create pagination element
     * @param {HTMLElement} swiper - Swiper element
     */
    private createPagination(swiper: HTMLElement): void {
        const pagination = DOMUtils.createElement('div', {
            className: 'swiper-pagination'
        });
        DOMUtils.appendChild(swiper, pagination);
    }

    /**
     * Create navigation elements
     * @param {HTMLElement} swiper - Swiper element
     */
    private createNavigation(swiper: HTMLElement): void {
        const prevButton = DOMUtils.createElement('div', {
            className: 'swiper-button-prev swiper-navigation-button swiper-navigation-button--prev'
        });
        const nextButton = DOMUtils.createElement('div', {
            className: 'swiper-button-next swiper-navigation-button swiper-navigation-button--next'
        });

        DOMUtils.appendChild(swiper, prevButton);
        DOMUtils.appendChild(swiper, nextButton);
    }

    /**
     * Append slideshow to DOM
     * @param {HTMLElement} container - Container element
     */
    private appendToDOM(container: HTMLElement): void {
        const contentContainer = DOMUtils.querySelector("#content .container");
        if (contentContainer) {
            DOMUtils.prependChild(contentContainer, container);
        }
    }

    /**
     * Initialize Swiper instance
     * @param {number} transitionTime - Transition time in milliseconds
     * @param {number} _slideCount - Number of slides available (unused for now)
     */
    private initializeSwiper(transitionTime: number, _slideCount: number): void {
        try {
            // Disable loop mode to avoid conflicts with other Swiper instances on the page
            // This prevents the transform calculation errors and slide positioning issues
            const defaultSlidesPerView = defaultConfig.slider.swiper.slidesPerView as number;
            const enableLoop = false; // Temporarily disable loop to fix positioning issues

            // Calculate responsive slidesPerView values based on loop capability
            let tabletSlidesPerView = SWIPER_CONFIG_CONSTANTS.TABLET_SLIDES_PER_VIEW_NO_LOOP;
            let desktopSlidesPerView = SWIPER_CONFIG_CONSTANTS.FALLBACK_SLIDES_PER_VIEW;

            if (enableLoop) {
                tabletSlidesPerView = SWIPER_CONFIG_CONSTANTS.TABLET_SLIDES_PER_VIEW_WITH_LOOP;
                desktopSlidesPerView = defaultSlidesPerView;
            }

            this.swiper = new Swiper(`.${defaultConfig.slider.dom.swiperClass}`, {
                loop: enableLoop,
                autoplay: {
                    delay: transitionTime,
                    disableOnInteraction: false,
                },
                spaceBetween: defaultConfig.slider.swiper.spaceBetween,
                effect: defaultConfig.slider.swiper.effect,
                centeredSlides: defaultConfig.slider.swiper.centeredSlides,
                slidesPerView: desktopSlidesPerView,
                // Simplified responsive breakpoints
                breakpoints: {
                    // Mobile devices - show less of next slide
                    320: {
                        slidesPerView: SWIPER_CONFIG_CONSTANTS.MOBILE_SLIDES_PER_VIEW,
                        spaceBetween: 10,
                    },
                    // Tablets - show more of next slide
                    768: {
                        slidesPerView: tabletSlidesPerView,
                        spaceBetween: 12,
                    },
                    // Desktop - use default configuration only if loop is enabled
                    1024: {
                        slidesPerView: desktopSlidesPerView,
                        spaceBetween: defaultConfig.slider.swiper.spaceBetween,
                    },
                },
                pagination: {
                    el: defaultConfig.slider.swiper.pagination.el,
                    type: defaultConfig.slider.swiper.pagination.type,
                    clickable: true,
                },
                navigation: {
                    nextEl: defaultConfig.slider.swiper.navigation.nextEl,
                    prevEl: defaultConfig.slider.swiper.navigation.prevEl,
                },
                initialSlide: 0,
                modules: [Navigation, Pagination, Autoplay]
            });
        } catch {
            // Silently handle Swiper initialization errors
        }
    }



    /**
     * Destroy slideshow instance
     */
    destroy(): void {
        if (this.swiper) {
            this.swiper.destroy(true, true);
            delete this.swiper;
        }

        if (this.container) {
            DOMUtils.removeElement(this.container);
            delete this.container;
        }
    }
}
