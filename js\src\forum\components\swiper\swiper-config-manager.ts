import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { defaultConfig } from '../../../common/config';
import { ARRAY_CONSTANTS } from '../../../common/config/constants';
import type {
  ISwiperConfigManager,
  ConfigCalculationResult,
  SwiperFullConfig,
  ResponsiveConfig,
  SwiperBreakpoints,
  ValidationResult
} from '../../../common/config/types';

/**
 * Swiper configuration constants
 * Based on Swiper documentation requirements for loop mode
 */
const SWIPER_CONFIG_CONSTANTS = {
  MIN_SLIDES_FOR_DUAL_VIEW: 8,
  // For slidesPerView: 1.2 with centeredSlides, need at least 8 slides for stable loop
  // This is more conservative to avoid the loop warning
  MIN_SLIDES_FOR_PARTIAL_VIEW_LOOP: 8,
  // For slidesPerView: 1 with centeredSlides, need at least 3 slides
  MIN_SLIDES_FOR_SINGLE_VIEW_LOOP: 3,
  // Responsive slidesPerView values
  MOBILE_SLIDES_PER_VIEW: 1,
  TABLET_SLIDES_PER_VIEW_WITH_LOOP: 1.1,
  TABLET_SLIDES_PER_VIEW_NO_LOOP: 1,
  FALLBACK_SLIDES_PER_VIEW: 1,
} as const;

/**
 * Swiper configuration manager
 * Handles all Swiper configuration generation, validation, and responsive calculations
 */
export class SwiperConfigManager implements ISwiperConfigManager {
  /**
   * Calculate complete Swiper configuration based on slide count and transition time
   */
  calculateConfiguration(slideCount: number, transitionTime: number): ConfigCalculationResult {
    const enableLoop = this.shouldEnableLoop(slideCount);
    const responsiveConfig = this.calculateResponsiveConfig(enableLoop);
    const finalConfig = this.buildFinalConfig(responsiveConfig, transitionTime, enableLoop);

    return {
      enableLoop,
      responsiveConfig,
      finalConfig
    };
  }

  /**
   * Validate Swiper configuration
   */
  validateConfiguration(config: SwiperFullConfig): ValidationResult {
    const errors: string[] = [];

    // Validate required properties
    if (!config.autoplay || typeof config.autoplay.delay !== 'number' || config.autoplay.delay <= ARRAY_CONSTANTS.EMPTY_LENGTH) {
      errors.push('Invalid autoplay delay configuration');
    }

    if (typeof config.spaceBetween !== 'number' || config.spaceBetween < ARRAY_CONSTANTS.EMPTY_LENGTH) {
      errors.push('Invalid spaceBetween configuration');
    }

    if (typeof config.slidesPerView !== 'number' || config.slidesPerView <= ARRAY_CONSTANTS.EMPTY_LENGTH) {
      errors.push('Invalid slidesPerView configuration');
    }

    // Validate breakpoints
    if (!config.breakpoints || typeof config.breakpoints !== 'object') {
      errors.push('Invalid breakpoints configuration');
    } else {
      for (const [width, breakpointConfig] of Object.entries(config.breakpoints)) {
        const widthNum = Number(width);
        if (Number.isNaN(widthNum) || widthNum <= ARRAY_CONSTANTS.EMPTY_LENGTH) {
          errors.push(`Invalid breakpoint width: ${width}`);
        }
        if (typeof breakpointConfig.slidesPerView !== 'number' || breakpointConfig.slidesPerView <= ARRAY_CONSTANTS.EMPTY_LENGTH) {
          errors.push(`Invalid slidesPerView for breakpoint ${width}`);
        }
        if (typeof breakpointConfig.spaceBetween !== 'number' || breakpointConfig.spaceBetween < ARRAY_CONSTANTS.EMPTY_LENGTH) {
          errors.push(`Invalid spaceBetween for breakpoint ${width}`);
        }
      }
    }

    // Validate pagination
    if (!config.pagination || !config.pagination.el || typeof config.pagination.el !== 'string') {
      errors.push('Invalid pagination element selector');
    }

    // Validate navigation
    if (!config.navigation || !config.navigation.nextEl || !config.navigation.prevEl) {
      errors.push('Invalid navigation element selectors');
    }

    return {
      isValid: errors.length === ARRAY_CONSTANTS.EMPTY_LENGTH,
      errors
    };
  }

  /**
   * Determine if loop mode should be enabled based on slide count
   */
  private shouldEnableLoop(_slideCount: number): boolean {
    // Disable loop mode to avoid conflicts with other Swiper instances on the page
    // This prevents the transform calculation errors and slide positioning issues
    return false;

    // Future implementation when loop issues are resolved:
    // const defaultSlidesPerView = defaultConfig.slider.swiper.slidesPerView as number;
    // if (defaultSlidesPerView > 1) {
    //   return slideCount >= SWIPER_CONFIG_CONSTANTS.MIN_SLIDES_FOR_PARTIAL_VIEW_LOOP;
    // }
    // return slideCount >= SWIPER_CONFIG_CONSTANTS.MIN_SLIDES_FOR_SINGLE_VIEW_LOOP;
  }

  /**
   * Calculate responsive configuration based on loop capability
   */
  private calculateResponsiveConfig(enableLoop: boolean): ResponsiveConfig {
    const defaultSlidesPerView = defaultConfig.slider.swiper.slidesPerView as number;

    let tabletSlidesPerView = SWIPER_CONFIG_CONSTANTS.TABLET_SLIDES_PER_VIEW_NO_LOOP;
    let desktopSlidesPerView = SWIPER_CONFIG_CONSTANTS.FALLBACK_SLIDES_PER_VIEW;

    if (enableLoop) {
      tabletSlidesPerView = SWIPER_CONFIG_CONSTANTS.TABLET_SLIDES_PER_VIEW_WITH_LOOP;
      desktopSlidesPerView = defaultSlidesPerView;
    }

    return {
      mobile: {
        slidesPerView: SWIPER_CONFIG_CONSTANTS.MOBILE_SLIDES_PER_VIEW,
        spaceBetween: 10
      },
      tablet: {
        slidesPerView: tabletSlidesPerView,
        spaceBetween: 12
      },
      desktop: {
        slidesPerView: desktopSlidesPerView,
        spaceBetween: defaultConfig.slider.swiper.spaceBetween
      }
    };
  }

  /**
   * Build final Swiper configuration object
   */
  private buildFinalConfig(
    responsiveConfig: ResponsiveConfig,
    transitionTime: number,
    enableLoop: boolean
  ): SwiperFullConfig {
    const breakpoints: SwiperBreakpoints = {
      320: responsiveConfig.mobile,
      768: responsiveConfig.tablet,
      1024: responsiveConfig.desktop
    };

    return {
      loop: enableLoop,
      autoplay: {
        delay: transitionTime,
        disableOnInteraction: false
      },
      spaceBetween: defaultConfig.slider.swiper.spaceBetween,
      effect: defaultConfig.slider.swiper.effect,
      centeredSlides: defaultConfig.slider.swiper.centeredSlides,
      slidesPerView: responsiveConfig.desktop.slidesPerView,
      breakpoints,
      pagination: {
        el: defaultConfig.slider.swiper.pagination.el,
        type: defaultConfig.slider.swiper.pagination.type,
        clickable: true
      },
      navigation: {
        nextEl: defaultConfig.slider.swiper.navigation.nextEl,
        prevEl: defaultConfig.slider.swiper.navigation.prevEl
      },
      initialSlide: 0,
      modules: [Navigation, Pagination, Autoplay]
    };
  }
}
