import Swiper from 'swiper';
import { defaultConfig } from '../../../common/config';
import { TIMING } from '../../../common/config/constants';
import type {
  ISwiperLifecycleManager,
  SwiperInstance,
  SwiperFullConfig
} from '../../../common/config/types';

/**
 * Swiper lifecycle manager
 * Handles Swiper instance creation, destruction, and lifecycle management
 */
export class SwiperLifecycleManager implements ISwiperLifecycleManager {
  private swiperInstance: SwiperInstance | undefined;
  private isDestroyed = false;

  /**
   * Initialize Swiper instance with the provided configuration
   */
  initialize(config: SwiperFullConfig): SwiperInstance | undefined {
    try {
      // Destroy existing instance if any
      this.destroy();

      // Create new Swiper instance
      this.swiperInstance = new Swiper(`.${defaultConfig.slider.dom.swiperClass}`, config) as SwiperInstance;
      this.isDestroyed = false;

      return this.swiperInstance;
    } catch {
      // Silently handle initialization errors
      return;
    }
  }

  /**
   * Destroy the current Swiper instance
   */
  destroy(): void {
    if (this.swiperInstance && !this.isDestroyed) {
      try {
        this.swiperInstance.destroy(true, true);
      } catch {
        // Silently handle destruction errors
      } finally {
        delete this.swiperInstance;
        this.isDestroyed = true;
      }
    }
  }

  /**
   * Check if Swiper instance is initialized and not destroyed
   */
  isInitialized(): boolean {
    return this.swiperInstance !== null && !this.isDestroyed;
  }

  /**
   * Get the current Swiper instance
   */
  getInstance(): SwiperInstance | undefined {
    if (this.isInitialized()) {
      return this.swiperInstance;
    }
    return;
  }

  /**
   * Reinitialize Swiper with new configuration
   */
  reinitialize(config: SwiperFullConfig): SwiperInstance | null {
    this.destroy();
    return this.initialize(config);
  }

  /**
   * Check if the Swiper container exists in the DOM
   */
  isContainerAvailable(): boolean {
    try {
      const container = document.querySelector(`.${defaultConfig.slider.dom.swiperClass}`);
      return container !== null;
    } catch {
      return false;
    }
  }

  /**
   * Initialize with delay to wait for DOM readiness
   */
  initializeWithDelay(config: SwiperFullConfig, delay = TIMING.CHECK_INTERVAL): void {
    setTimeout(() => {
      if (this.isContainerAvailable()) {
        this.initialize(config);
      }
    }, delay);
  }

  /**
   * Get Swiper instance status information
   */
  getStatus(): {
    isInitialized: boolean;
    isDestroyed: boolean;
    hasInstance: boolean;
    containerAvailable: boolean;
  } {
    return {
      isInitialized: this.isInitialized(),
      isDestroyed: this.isDestroyed,
      hasInstance: this.swiperInstance !== null,
      containerAvailable: this.isContainerAvailable()
    };
  }
}
