(function(We,q,Xe){"use strict";const be=s=>{try{return document.querySelector(s)||!1}catch{return!1}},qe=s=>{try{return document.querySelectorAll(s)}catch{return document.querySelectorAll("")}},j=(s,e={},t="")=>{try{const i=document.createElement(s);for(const[n,r]of Object.entries(e))n==="className"?i.className=String(r):n==="id"?i.id=String(r):i.setAttribute(n,String(r));return t&&(i.innerHTML=t),i}catch{return document.createElement("div")}},K=(s,e)=>{try{s.appendChild(e)}catch{}},je=(s,e)=>{try{s.prepend(e)}catch{}},ue=s=>{try{s.remove()}catch{}},xe={USER_AGENT_SUBSTR_START:0,USER_AGENT_SUBSTR_LENGTH:4},Ye={MAX_ERROR_LOG_ENTRIES:50,DOM_READY_TIMEOUT:5e3,SLIDE_NUMBER_MIN:1,SLIDE_NUMBER_MAX:30,TRANSITION_TIME_MIN:1e3,TRANSITION_TIME_MAX:3e4,CONFIG_MAX_SLIDES_MIN:1,CONFIG_MAX_SLIDES_MAX:50},Z={SLIDE_INCREMENT:1,INITIAL_SLIDE_INDEX:1},V={EMPTY_LENGTH:0,FIRST_INDEX:0,NOT_FOUND_INDEX:-1,NEXT_ITEM_OFFSET:1,LAST_ITEM_OFFSET:-1},ee={CHECK_INTERVAL:10,DATA_CHECK_INTERVAL:100,DEFAULT_TRANSITION_TIME:5e3},Ie={SWIPER_AD_CONTAINER_ID:"swiperAdContainer",HEADER_ICON_ID:"wusong8899HeaderAdvIcon"},Ue={AD_SWIPER:"adSwiper"},fe={SWIPER_PAGINATION_EL:".swiper-pagination",SWIPER_BUTTON_NEXT_EL:".swiper-button-next",SWIPER_BUTTON_PREV_EL:".swiper-button-prev"},te={ID:"wusong8899-header-advertisement",TRANSLATION_PREFIX:"wusong8899-header-advertisement",MAX_SLIDES:30,HEADER_ICON_URL:"https://ex.cc/assets/files/date/test.png"},N={env:"production",app:{extensionId:te.ID,translationPrefix:te.TRANSLATION_PREFIX},slider:{maxSlides:te.MAX_SLIDES,defaultTransitionTime:ee.DEFAULT_TRANSITION_TIME,checkTime:ee.CHECK_INTERVAL,dataCheckInterval:ee.DATA_CHECK_INTERVAL,dom:{containerId:Ie.SWIPER_AD_CONTAINER_ID,swiperClass:Ue.AD_SWIPER},swiper:{spaceBetween:15,effect:"slide",centeredSlides:!0,slidesPerView:1.2,pagination:{el:fe.SWIPER_PAGINATION_EL,type:"bullets"},navigation:{nextEl:fe.SWIPER_BUTTON_NEXT_EL,prevEl:fe.SWIPER_BUTTON_PREV_EL}}},ui:{headerIconId:Ie.HEADER_ICON_ID,headerIconUrl:te.HEADER_ICON_URL}};function Ce(s){return s!==null&&typeof s=="object"&&"constructor"in s&&s.constructor===Object}function pe(s,e){s===void 0&&(s={}),e===void 0&&(e={});const t=["__proto__","constructor","prototype"];Object.keys(e).filter(i=>t.indexOf(i)<0).forEach(i=>{typeof s[i]>"u"?s[i]=e[i]:Ce(e[i])&&Ce(s[i])&&Object.keys(e[i]).length>0&&pe(s[i],e[i])})}const Le={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function F(){const s=typeof document<"u"?document:{};return pe(s,Le),s}const Ke={document:Le,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(s){return typeof setTimeout>"u"?(s(),null):setTimeout(s,0)},cancelAnimationFrame(s){typeof setTimeout>"u"||clearTimeout(s)}};function G(){const s=typeof window<"u"?window:{};return pe(s,Ke),s}function Qe(s){return s===void 0&&(s=""),s.trim().split(" ").filter(e=>!!e.trim())}function Je(s){const e=s;Object.keys(e).forEach(t=>{try{e[t]=null}catch{}try{delete e[t]}catch{}})}function Me(s,e){return e===void 0&&(e=0),setTimeout(s,e)}function ie(){return Date.now()}function Ze(s){const e=G();let t;return e.getComputedStyle&&(t=e.getComputedStyle(s,null)),!t&&s.currentStyle&&(t=s.currentStyle),t||(t=s.style),t}function et(s,e){e===void 0&&(e="x");const t=G();let i,n,r;const l=Ze(s);return t.WebKitCSSMatrix?(n=l.transform||l.webkitTransform,n.split(",").length>6&&(n=n.split(", ").map(a=>a.replace(",",".")).join(", ")),r=new t.WebKitCSSMatrix(n==="none"?"":n)):(r=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),i=r.toString().split(",")),e==="x"&&(t.WebKitCSSMatrix?n=r.m41:i.length===16?n=parseFloat(i[12]):n=parseFloat(i[4])),e==="y"&&(t.WebKitCSSMatrix?n=r.m42:i.length===16?n=parseFloat(i[13]):n=parseFloat(i[5])),n||0}function se(s){return typeof s=="object"&&s!==null&&s.constructor&&Object.prototype.toString.call(s).slice(8,-1)==="Object"}function tt(s){return typeof window<"u"&&typeof window.HTMLElement<"u"?s instanceof HTMLElement:s&&(s.nodeType===1||s.nodeType===11)}function $(){const s=Object(arguments.length<=0?void 0:arguments[0]),e=["__proto__","constructor","prototype"];for(let t=1;t<arguments.length;t+=1){const i=t<0||arguments.length<=t?void 0:arguments[t];if(i!=null&&!tt(i)){const n=Object.keys(Object(i)).filter(r=>e.indexOf(r)<0);for(let r=0,l=n.length;r<l;r+=1){const a=n[r],d=Object.getOwnPropertyDescriptor(i,a);d!==void 0&&d.enumerable&&(se(s[a])&&se(i[a])?i[a].__swiper__?s[a]=i[a]:$(s[a],i[a]):!se(s[a])&&se(i[a])?(s[a]={},i[a].__swiper__?s[a]=i[a]:$(s[a],i[a])):s[a]=i[a])}}}return s}function ne(s,e,t){s.style.setProperty(e,t)}function Pe(s){let{swiper:e,targetPosition:t,side:i}=s;const n=G(),r=-e.translate;let l=null,a;const d=e.params.speed;e.wrapperEl.style.scrollSnapType="none",n.cancelAnimationFrame(e.cssModeFrameID);const o=t>r?"next":"prev",u=(h,g)=>o==="next"&&h>=g||o==="prev"&&h<=g,m=()=>{a=new Date().getTime(),l===null&&(l=a);const h=Math.max(Math.min((a-l)/d,1),0),g=.5-Math.cos(h*Math.PI)/2;let p=r+g*(t-r);if(u(p,t)&&(p=t),e.wrapperEl.scrollTo({[i]:p}),u(p,t)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[i]:p})}),n.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=n.requestAnimationFrame(m)};m()}function W(s,e){e===void 0&&(e="");const t=G(),i=[...s.children];return t.HTMLSlotElement&&s instanceof HTMLSlotElement&&i.push(...s.assignedElements()),e?i.filter(n=>n.matches(e)):i}function it(s,e){const t=[e];for(;t.length>0;){const i=t.shift();if(s===i)return!0;t.push(...i.children,...i.shadowRoot?i.shadowRoot.children:[],...i.assignedElements?i.assignedElements():[])}}function st(s,e){const t=G();let i=e.contains(s);return!i&&t.HTMLSlotElement&&e instanceof HTMLSlotElement&&(i=[...e.assignedElements()].includes(s),i||(i=it(s,e))),i}function re(s){try{console.warn(s);return}catch{}}function ae(s,e){e===void 0&&(e=[]);const t=document.createElement(s);return t.classList.add(...Array.isArray(e)?e:Qe(e)),t}function nt(s,e){const t=[];for(;s.previousElementSibling;){const i=s.previousElementSibling;e?i.matches(e)&&t.push(i):t.push(i),s=i}return t}function rt(s,e){const t=[];for(;s.nextElementSibling;){const i=s.nextElementSibling;e?i.matches(e)&&t.push(i):t.push(i),s=i}return t}function X(s,e){return G().getComputedStyle(s,null).getPropertyValue(e)}function le(s){let e=s,t;if(e){for(t=0;(e=e.previousSibling)!==null;)e.nodeType===1&&(t+=1);return t}}function Ae(s,e){const t=[];let i=s.parentElement;for(;i;)e?i.matches(e)&&t.push(i):t.push(i),i=i.parentElement;return t}function me(s,e,t){const i=G();return s[e==="width"?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(s,null).getPropertyValue(e==="width"?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(s,null).getPropertyValue(e==="width"?"margin-left":"margin-bottom"))}function k(s){return(Array.isArray(s)?s:[s]).filter(e=>!!e)}function Oe(s,e){e===void 0&&(e=""),typeof trustedTypes<"u"?s.innerHTML=trustedTypes.createPolicy("html",{createHTML:t=>t}).createHTML(e):s.innerHTML=e}function _e(s,e,t,i){return s.params.createElements&&Object.keys(i).forEach(n=>{if(!t[n]&&t.auto===!0){let r=W(s.el,`.${i[n]}`)[0];r||(r=ae("div",i[n]),r.className=i[n],s.el.append(r)),t[n]=r,e[n]=r}}),t}function at(s){let{swiper:e,extendParams:t,on:i,emit:n}=s;t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,prevEl:null};function r(p){let v;return p&&typeof p=="string"&&e.isElement&&(v=e.el.querySelector(p)||e.hostEl.querySelector(p),v)?v:(p&&(typeof p=="string"&&(v=[...document.querySelectorAll(p)]),e.params.uniqueNavElements&&typeof p=="string"&&v&&v.length>1&&e.el.querySelectorAll(p).length===1?v=e.el.querySelector(p):v&&v.length===1&&(v=v[0])),p&&!v?p:v)}function l(p,v){const E=e.params.navigation;p=k(p),p.forEach(S=>{S&&(S.classList[v?"add":"remove"](...E.disabledClass.split(" ")),S.tagName==="BUTTON"&&(S.disabled=v),e.params.watchOverflow&&e.enabled&&S.classList[e.isLocked?"add":"remove"](E.lockClass))})}function a(){const{nextEl:p,prevEl:v}=e.navigation;if(e.params.loop){l(v,!1),l(p,!1);return}l(v,e.isBeginning&&!e.params.rewind),l(p,e.isEnd&&!e.params.rewind)}function d(p){p.preventDefault(),!(e.isBeginning&&!e.params.loop&&!e.params.rewind)&&(e.slidePrev(),n("navigationPrev"))}function o(p){p.preventDefault(),!(e.isEnd&&!e.params.loop&&!e.params.rewind)&&(e.slideNext(),n("navigationNext"))}function u(){const p=e.params.navigation;if(e.params.navigation=_e(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(p.nextEl||p.prevEl))return;let v=r(p.nextEl),E=r(p.prevEl);Object.assign(e.navigation,{nextEl:v,prevEl:E}),v=k(v),E=k(E);const S=(c,f)=>{c&&c.addEventListener("click",f==="next"?o:d),!e.enabled&&c&&c.classList.add(...p.lockClass.split(" "))};v.forEach(c=>S(c,"next")),E.forEach(c=>S(c,"prev"))}function m(){let{nextEl:p,prevEl:v}=e.navigation;p=k(p),v=k(v);const E=(S,c)=>{S.removeEventListener("click",c==="next"?o:d),S.classList.remove(...e.params.navigation.disabledClass.split(" "))};p.forEach(S=>E(S,"next")),v.forEach(S=>E(S,"prev"))}i("init",()=>{e.params.navigation.enabled===!1?g():(u(),a())}),i("toEdge fromEdge lock unlock",()=>{a()}),i("destroy",()=>{m()}),i("enable disable",()=>{let{nextEl:p,prevEl:v}=e.navigation;if(p=k(p),v=k(v),e.enabled){a();return}[...p,...v].filter(E=>!!E).forEach(E=>E.classList.add(e.params.navigation.lockClass))}),i("click",(p,v)=>{let{nextEl:E,prevEl:S}=e.navigation;E=k(E),S=k(S);const c=v.target;let f=S.includes(c)||E.includes(c);if(e.isElement&&!f){const w=v.path||v.composedPath&&v.composedPath();w&&(f=w.find(T=>E.includes(T)||S.includes(T)))}if(e.params.navigation.hideOnClick&&!f){if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===c||e.pagination.el.contains(c)))return;let w;E.length?w=E[0].classList.contains(e.params.navigation.hiddenClass):S.length&&(w=S[0].classList.contains(e.params.navigation.hiddenClass)),n(w===!0?"navigationShow":"navigationHide"),[...E,...S].filter(T=>!!T).forEach(T=>T.classList.toggle(e.params.navigation.hiddenClass))}});const h=()=>{e.el.classList.remove(...e.params.navigation.navigationDisabledClass.split(" ")),u(),a()},g=()=>{e.el.classList.add(...e.params.navigation.navigationDisabledClass.split(" ")),m()};Object.assign(e.navigation,{enable:h,disable:g,update:a,init:u,destroy:m})}function J(s){return s===void 0&&(s=""),`.${s.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function lt(s){let{swiper:e,extendParams:t,on:i,emit:n}=s;const r="swiper-pagination";t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:c=>c,formatFractionTotal:c=>c,bulletClass:`${r}-bullet`,bulletActiveClass:`${r}-bullet-active`,modifierClass:`${r}-`,currentClass:`${r}-current`,totalClass:`${r}-total`,hiddenClass:`${r}-hidden`,progressbarFillClass:`${r}-progressbar-fill`,progressbarOppositeClass:`${r}-progressbar-opposite`,clickableClass:`${r}-clickable`,lockClass:`${r}-lock`,horizontalClass:`${r}-horizontal`,verticalClass:`${r}-vertical`,paginationDisabledClass:`${r}-disabled`}}),e.pagination={el:null,bullets:[]};let l,a=0;function d(){return!e.params.pagination.el||!e.pagination.el||Array.isArray(e.pagination.el)&&e.pagination.el.length===0}function o(c,f){const{bulletActiveClass:w}=e.params.pagination;c&&(c=c[`${f==="prev"?"previous":"next"}ElementSibling`],c&&(c.classList.add(`${w}-${f}`),c=c[`${f==="prev"?"previous":"next"}ElementSibling`],c&&c.classList.add(`${w}-${f}-${f}`)))}function u(c,f,w){if(c=c%w,f=f%w,f===c+1)return"next";if(f===c-1)return"previous"}function m(c){const f=c.target.closest(J(e.params.pagination.bulletClass));if(!f)return;c.preventDefault();const w=le(f)*e.params.slidesPerGroup;if(e.params.loop){if(e.realIndex===w)return;const T=u(e.realIndex,w,e.slides.length);T==="next"?e.slideNext():T==="previous"?e.slidePrev():e.slideToLoop(w)}else e.slideTo(w)}function h(){const c=e.rtl,f=e.params.pagination;if(d())return;let w=e.pagination.el;w=k(w);let T,x;const C=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,I=e.params.loop?Math.ceil(C/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?(x=e.previousRealIndex||0,T=e.params.slidesPerGroup>1?Math.floor(e.realIndex/e.params.slidesPerGroup):e.realIndex):typeof e.snapIndex<"u"?(T=e.snapIndex,x=e.previousSnapIndex):(x=e.previousIndex||0,T=e.activeIndex||0),f.type==="bullets"&&e.pagination.bullets&&e.pagination.bullets.length>0){const L=e.pagination.bullets;let y,b,A;if(f.dynamicBullets&&(l=me(L[0],e.isHorizontal()?"width":"height"),w.forEach(O=>{O.style[e.isHorizontal()?"width":"height"]=`${l*(f.dynamicMainBullets+4)}px`}),f.dynamicMainBullets>1&&x!==void 0&&(a+=T-(x||0),a>f.dynamicMainBullets-1?a=f.dynamicMainBullets-1:a<0&&(a=0)),y=Math.max(T-a,0),b=y+(Math.min(L.length,f.dynamicMainBullets)-1),A=(b+y)/2),L.forEach(O=>{const _=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(D=>`${f.bulletActiveClass}${D}`)].map(D=>typeof D=="string"&&D.includes(" ")?D.split(" "):D).flat();O.classList.remove(..._)}),w.length>1)L.forEach(O=>{const _=le(O);_===T?O.classList.add(...f.bulletActiveClass.split(" ")):e.isElement&&O.setAttribute("part","bullet"),f.dynamicBullets&&(_>=y&&_<=b&&O.classList.add(...`${f.bulletActiveClass}-main`.split(" ")),_===y&&o(O,"prev"),_===b&&o(O,"next"))});else{const O=L[T];if(O&&O.classList.add(...f.bulletActiveClass.split(" ")),e.isElement&&L.forEach((_,D)=>{_.setAttribute("part",D===T?"bullet-active":"bullet")}),f.dynamicBullets){const _=L[y],D=L[b];for(let M=y;M<=b;M+=1)L[M]&&L[M].classList.add(...`${f.bulletActiveClass}-main`.split(" "));o(_,"prev"),o(D,"next")}}if(f.dynamicBullets){const O=Math.min(L.length,f.dynamicMainBullets+4),_=(l*O-l)/2-A*l,D=c?"right":"left";L.forEach(M=>{M.style[e.isHorizontal()?D:"top"]=`${_}px`})}}w.forEach((L,y)=>{if(f.type==="fraction"&&(L.querySelectorAll(J(f.currentClass)).forEach(b=>{b.textContent=f.formatFractionCurrent(T+1)}),L.querySelectorAll(J(f.totalClass)).forEach(b=>{b.textContent=f.formatFractionTotal(I)})),f.type==="progressbar"){let b;f.progressbarOpposite?b=e.isHorizontal()?"vertical":"horizontal":b=e.isHorizontal()?"horizontal":"vertical";const A=(T+1)/I;let O=1,_=1;b==="horizontal"?O=A:_=A,L.querySelectorAll(J(f.progressbarFillClass)).forEach(D=>{D.style.transform=`translate3d(0,0,0) scaleX(${O}) scaleY(${_})`,D.style.transitionDuration=`${e.params.speed}ms`})}f.type==="custom"&&f.renderCustom?(Oe(L,f.renderCustom(e,T+1,I)),y===0&&n("paginationRender",L)):(y===0&&n("paginationRender",L),n("paginationUpdate",L)),e.params.watchOverflow&&e.enabled&&L.classList[e.isLocked?"add":"remove"](f.lockClass)})}function g(){const c=e.params.pagination;if(d())return;const f=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.grid&&e.params.grid.rows>1?e.slides.length/Math.ceil(e.params.grid.rows):e.slides.length;let w=e.pagination.el;w=k(w);let T="";if(c.type==="bullets"){let x=e.params.loop?Math.ceil(f/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&x>f&&(x=f);for(let C=0;C<x;C+=1)c.renderBullet?T+=c.renderBullet.call(e,C,c.bulletClass):T+=`<${c.bulletElement} ${e.isElement?'part="bullet"':""} class="${c.bulletClass}"></${c.bulletElement}>`}c.type==="fraction"&&(c.renderFraction?T=c.renderFraction.call(e,c.currentClass,c.totalClass):T=`<span class="${c.currentClass}"></span> / <span class="${c.totalClass}"></span>`),c.type==="progressbar"&&(c.renderProgressbar?T=c.renderProgressbar.call(e,c.progressbarFillClass):T=`<span class="${c.progressbarFillClass}"></span>`),e.pagination.bullets=[],w.forEach(x=>{c.type!=="custom"&&Oe(x,T||""),c.type==="bullets"&&e.pagination.bullets.push(...x.querySelectorAll(J(c.bulletClass)))}),c.type!=="custom"&&n("paginationRender",w[0])}function p(){e.params.pagination=_e(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});const c=e.params.pagination;if(!c.el)return;let f;typeof c.el=="string"&&e.isElement&&(f=e.el.querySelector(c.el)),!f&&typeof c.el=="string"&&(f=[...document.querySelectorAll(c.el)]),f||(f=c.el),!(!f||f.length===0)&&(e.params.uniqueNavElements&&typeof c.el=="string"&&Array.isArray(f)&&f.length>1&&(f=[...e.el.querySelectorAll(c.el)],f.length>1&&(f=f.find(w=>Ae(w,".swiper")[0]===e.el))),Array.isArray(f)&&f.length===1&&(f=f[0]),Object.assign(e.pagination,{el:f}),f=k(f),f.forEach(w=>{c.type==="bullets"&&c.clickable&&w.classList.add(...(c.clickableClass||"").split(" ")),w.classList.add(c.modifierClass+c.type),w.classList.add(e.isHorizontal()?c.horizontalClass:c.verticalClass),c.type==="bullets"&&c.dynamicBullets&&(w.classList.add(`${c.modifierClass}${c.type}-dynamic`),a=0,c.dynamicMainBullets<1&&(c.dynamicMainBullets=1)),c.type==="progressbar"&&c.progressbarOpposite&&w.classList.add(c.progressbarOppositeClass),c.clickable&&w.addEventListener("click",m),e.enabled||w.classList.add(c.lockClass)}))}function v(){const c=e.params.pagination;if(d())return;let f=e.pagination.el;f&&(f=k(f),f.forEach(w=>{w.classList.remove(c.hiddenClass),w.classList.remove(c.modifierClass+c.type),w.classList.remove(e.isHorizontal()?c.horizontalClass:c.verticalClass),c.clickable&&(w.classList.remove(...(c.clickableClass||"").split(" ")),w.removeEventListener("click",m))})),e.pagination.bullets&&e.pagination.bullets.forEach(w=>w.classList.remove(...c.bulletActiveClass.split(" ")))}i("changeDirection",()=>{if(!e.pagination||!e.pagination.el)return;const c=e.params.pagination;let{el:f}=e.pagination;f=k(f),f.forEach(w=>{w.classList.remove(c.horizontalClass,c.verticalClass),w.classList.add(e.isHorizontal()?c.horizontalClass:c.verticalClass)})}),i("init",()=>{e.params.pagination.enabled===!1?S():(p(),g(),h())}),i("activeIndexChange",()=>{typeof e.snapIndex>"u"&&h()}),i("snapIndexChange",()=>{h()}),i("snapGridLengthChange",()=>{g(),h()}),i("destroy",()=>{v()}),i("enable disable",()=>{let{el:c}=e.pagination;c&&(c=k(c),c.forEach(f=>f.classList[e.enabled?"remove":"add"](e.params.pagination.lockClass)))}),i("lock unlock",()=>{h()}),i("click",(c,f)=>{const w=f.target,T=k(e.pagination.el);if(e.params.pagination.el&&e.params.pagination.hideOnClick&&T&&T.length>0&&!w.classList.contains(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&w===e.navigation.nextEl||e.navigation.prevEl&&w===e.navigation.prevEl))return;const x=T[0].classList.contains(e.params.pagination.hiddenClass);n(x===!0?"paginationShow":"paginationHide"),T.forEach(C=>C.classList.toggle(e.params.pagination.hiddenClass))}});const E=()=>{e.el.classList.remove(e.params.pagination.paginationDisabledClass);let{el:c}=e.pagination;c&&(c=k(c),c.forEach(f=>f.classList.remove(e.params.pagination.paginationDisabledClass))),p(),g(),h()},S=()=>{e.el.classList.add(e.params.pagination.paginationDisabledClass);let{el:c}=e.pagination;c&&(c=k(c),c.forEach(f=>f.classList.add(e.params.pagination.paginationDisabledClass))),v()};Object.assign(e.pagination,{enable:E,disable:S,render:g,update:h,init:p,destroy:v})}function ot(s){let{swiper:e,extendParams:t,on:i,emit:n,params:r}=s;e.autoplay={running:!1,paused:!1,timeLeft:0},t({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});let l,a,d=r&&r.autoplay?r.autoplay.delay:3e3,o=r&&r.autoplay?r.autoplay.delay:3e3,u,m=new Date().getTime(),h,g,p,v,E,S,c;function f(P){!e||e.destroyed||!e.wrapperEl||P.target===e.wrapperEl&&(e.wrapperEl.removeEventListener("transitionend",f),!(c||P.detail&&P.detail.bySwiperTouchMove)&&y())}const w=()=>{if(e.destroyed||!e.autoplay.running)return;e.autoplay.paused?h=!0:h&&(o=u,h=!1);const P=e.autoplay.paused?u:m+o-new Date().getTime();e.autoplay.timeLeft=P,n("autoplayTimeLeft",P,P/d),a=requestAnimationFrame(()=>{w()})},T=()=>{let P;return e.virtual&&e.params.virtual.enabled?P=e.slides.find(R=>R.classList.contains("swiper-slide-active")):P=e.slides[e.activeIndex],P?parseInt(P.getAttribute("data-swiper-autoplay"),10):void 0},x=P=>{if(e.destroyed||!e.autoplay.running)return;cancelAnimationFrame(a),w();let z=typeof P>"u"?e.params.autoplay.delay:P;d=e.params.autoplay.delay,o=e.params.autoplay.delay;const R=T();!Number.isNaN(R)&&R>0&&typeof P>"u"&&(z=R,d=R,o=R),u=z;const U=e.params.speed,ce=()=>{!e||e.destroyed||(e.params.autoplay.reverseDirection?!e.isBeginning||e.params.loop||e.params.rewind?(e.slidePrev(U,!0,!0),n("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(e.slides.length-1,U,!0,!0),n("autoplay")):!e.isEnd||e.params.loop||e.params.rewind?(e.slideNext(U,!0,!0),n("autoplay")):e.params.autoplay.stopOnLastSlide||(e.slideTo(0,U,!0,!0),n("autoplay")),e.params.cssMode&&(m=new Date().getTime(),requestAnimationFrame(()=>{x()})))};return z>0?(clearTimeout(l),l=setTimeout(()=>{ce()},z)):requestAnimationFrame(()=>{ce()}),z},C=()=>{m=new Date().getTime(),e.autoplay.running=!0,x(),n("autoplayStart")},I=()=>{e.autoplay.running=!1,clearTimeout(l),cancelAnimationFrame(a),n("autoplayStop")},L=(P,z)=>{if(e.destroyed||!e.autoplay.running)return;clearTimeout(l),P||(S=!0);const R=()=>{n("autoplayPause"),e.params.autoplay.waitForTransition?e.wrapperEl.addEventListener("transitionend",f):y()};if(e.autoplay.paused=!0,z){E&&(u=e.params.autoplay.delay),E=!1,R();return}u=(u||e.params.autoplay.delay)-(new Date().getTime()-m),!(e.isEnd&&u<0&&!e.params.loop)&&(u<0&&(u=0),R())},y=()=>{e.isEnd&&u<0&&!e.params.loop||e.destroyed||!e.autoplay.running||(m=new Date().getTime(),S?(S=!1,x(u)):x(),e.autoplay.paused=!1,n("autoplayResume"))},b=()=>{if(e.destroyed||!e.autoplay.running)return;const P=F();P.visibilityState==="hidden"&&(S=!0,L(!0)),P.visibilityState==="visible"&&y()},A=P=>{P.pointerType==="mouse"&&(S=!0,c=!0,!(e.animating||e.autoplay.paused)&&L(!0))},O=P=>{P.pointerType==="mouse"&&(c=!1,e.autoplay.paused&&y())},_=()=>{e.params.autoplay.pauseOnMouseEnter&&(e.el.addEventListener("pointerenter",A),e.el.addEventListener("pointerleave",O))},D=()=>{e.el&&typeof e.el!="string"&&(e.el.removeEventListener("pointerenter",A),e.el.removeEventListener("pointerleave",O))},M=()=>{F().addEventListener("visibilitychange",b)},B=()=>{F().removeEventListener("visibilitychange",b)};i("init",()=>{e.params.autoplay.enabled&&(_(),M(),C())}),i("destroy",()=>{D(),B(),e.autoplay.running&&I()}),i("_freeModeStaticRelease",()=>{(p||S)&&y()}),i("_freeModeNoMomentumRelease",()=>{e.params.autoplay.disableOnInteraction?I():L(!0,!0)}),i("beforeTransitionStart",(P,z,R)=>{e.destroyed||!e.autoplay.running||(R||!e.params.autoplay.disableOnInteraction?L(!0,!0):I())}),i("sliderFirstMove",()=>{if(!(e.destroyed||!e.autoplay.running)){if(e.params.autoplay.disableOnInteraction){I();return}g=!0,p=!1,S=!1,v=setTimeout(()=>{S=!0,p=!0,L(!0)},200)}}),i("touchEnd",()=>{if(!(e.destroyed||!e.autoplay.running||!g)){if(clearTimeout(v),clearTimeout(l),e.params.autoplay.disableOnInteraction){p=!1,g=!1;return}p&&e.params.cssMode&&y(),p=!1,g=!1}}),i("slideChange",()=>{e.destroyed||!e.autoplay.running||(E=!0)}),Object.assign(e.autoplay,{start:C,stop:I,pause:L,resume:y})}const oe={MOBILE_SLIDES_PER_VIEW:1,TABLET_SLIDES_PER_VIEW_WITH_LOOP:1.1,TABLET_SLIDES_PER_VIEW_NO_LOOP:1,FALLBACK_SLIDES_PER_VIEW:1};class dt{calculateConfiguration(e,t){const i=this.shouldEnableLoop(e),n=this.calculateResponsiveConfig(i),r=this.buildFinalConfig(n,t,i);return{enableLoop:i,responsiveConfig:n,finalConfig:r}}validateConfiguration(e){const t=[];if((!e.autoplay||typeof e.autoplay.delay!="number"||e.autoplay.delay<=V.EMPTY_LENGTH)&&t.push("Invalid autoplay delay configuration"),(typeof e.spaceBetween!="number"||e.spaceBetween<V.EMPTY_LENGTH)&&t.push("Invalid spaceBetween configuration"),(typeof e.slidesPerView!="number"||e.slidesPerView<=V.EMPTY_LENGTH)&&t.push("Invalid slidesPerView configuration"),!e.breakpoints||typeof e.breakpoints!="object")t.push("Invalid breakpoints configuration");else for(const[i,n]of Object.entries(e.breakpoints)){const r=Number(i);(Number.isNaN(r)||r<=V.EMPTY_LENGTH)&&t.push(`Invalid breakpoint width: ${i}`),(typeof n.slidesPerView!="number"||n.slidesPerView<=V.EMPTY_LENGTH)&&t.push(`Invalid slidesPerView for breakpoint ${i}`),(typeof n.spaceBetween!="number"||n.spaceBetween<V.EMPTY_LENGTH)&&t.push(`Invalid spaceBetween for breakpoint ${i}`)}return(!e.pagination||!e.pagination.el||typeof e.pagination.el!="string")&&t.push("Invalid pagination element selector"),(!e.navigation||!e.navigation.nextEl||!e.navigation.prevEl)&&t.push("Invalid navigation element selectors"),{isValid:t.length===V.EMPTY_LENGTH,errors:t}}shouldEnableLoop(e){return!1}calculateResponsiveConfig(e){const t=N.slider.swiper.slidesPerView;let i=oe.TABLET_SLIDES_PER_VIEW_NO_LOOP,n=oe.FALLBACK_SLIDES_PER_VIEW;return e&&(i=oe.TABLET_SLIDES_PER_VIEW_WITH_LOOP,n=t),{mobile:{slidesPerView:oe.MOBILE_SLIDES_PER_VIEW,spaceBetween:10},tablet:{slidesPerView:i,spaceBetween:12},desktop:{slidesPerView:n,spaceBetween:N.slider.swiper.spaceBetween}}}buildFinalConfig(e,t,i){const n={320:e.mobile,768:e.tablet,1024:e.desktop};return{loop:i,autoplay:{delay:t,disableOnInteraction:!1},spaceBetween:N.slider.swiper.spaceBetween,effect:N.slider.swiper.effect,centeredSlides:N.slider.swiper.centeredSlides,slidesPerView:e.desktop.slidesPerView,breakpoints:n,pagination:{el:N.slider.swiper.pagination.el,type:N.slider.swiper.pagination.type,clickable:!0},navigation:{nextEl:N.slider.swiper.navigation.nextEl,prevEl:N.slider.swiper.navigation.prevEl},initialSlide:0,modules:[at,lt,ot]}}}const De=()=>{try{const{userAgent:s}=navigator;return s.substring(xe.USER_AGENT_SUBSTR_START,xe.USER_AGENT_SUBSTR_LENGTH)==="Mobi"}catch{return!1}};class ct{createContainer(){this.removeExistingNavigation();const e=j("div",{id:N.slider.dom.containerId,className:"adContainer adContainer--forced"});return this.container=e,e}createSwiperElement(e){let t=`swiper ${N.slider.dom.swiperClass} adSwiper--forced`;De()&&(t+=" adSwiper--mobile");const i=j("div",{className:t});return K(e,i),i}createSwiperWrapper(e){const t=j("div",{className:"swiper-wrapper swiper-wrapper--forced"});return K(e,t),t}createSlide(e,t){const i=j("div",{className:"swiper-slide swiper-slide--forced"});let n="";return t&&(n=`window.location.href="${t}"`),i.innerHTML=`<img onclick='${n}' src='${e}' class='swiper-slide__image' />`,i}createPagination(e){const t=j("div",{className:"swiper-pagination"});K(e,t)}createNavigation(e){const t=j("div",{className:"swiper-button-prev swiper-navigation-button swiper-navigation-button--prev"}),i=j("div",{className:"swiper-button-next swiper-navigation-button swiper-navigation-button--next"});K(e,t),K(e,i)}appendToDOM(e){const t=be("#content .container");t&&je(t,e)}cleanup(){this.container&&(ue(this.container),delete this.container)}getContainer(){return this.container}removeExistingNavigation(){const e=be(`#${N.slider.dom.containerId}`);e&&ue(e);const t=qe(".item-nav");for(const i of t)ue(i)}}class ut{constructor(){this.maxSlides=N.slider.maxSlides}fetchSlideData(){const e=[];for(let t=Z.INITIAL_SLIDE_INDEX;t<=this.maxSlides;t+=Z.SLIDE_INCREMENT){const i=this.getForumAttribute(`wusong8899-header-advertisement.Image${t}`),n=this.getForumAttribute(`wusong8899-header-advertisement.Link${t}`);i&&e.push({imageSrc:String(i),imageLink:String(n||""),slideIndex:t})}return this.processSlideData(e)}validateSlideData(e){const t=[];if(!Array.isArray(e))return t.push("Slide data must be an array"),{isValid:!1,errors:t};if(e.length===V.EMPTY_LENGTH)return t.push("No slide data provided"),{isValid:!1,errors:t};for(const i of e)(!i.imageSrc||typeof i.imageSrc!="string")&&t.push(`Invalid image source for slide ${i.slideIndex}`),i.imageLink&&typeof i.imageLink!="string"&&t.push(`Invalid image link for slide ${i.slideIndex}`),(typeof i.slideIndex!="number"||i.slideIndex<Z.INITIAL_SLIDE_INDEX)&&t.push(`Invalid slide index: ${i.slideIndex}`),i.imageSrc&&!this.isValidUrl(i.imageSrc)&&t.push(`Invalid image URL format for slide ${i.slideIndex}`),i.imageLink&&!this.isValidUrl(i.imageLink)&&t.push(`Invalid link URL format for slide ${i.slideIndex}`);return{isValid:t.length===V.EMPTY_LENGTH,errors:t}}getTransitionTime(){const e=this.getForumAttribute("wusong8899-header-advertisement.TransitionTime");if(e){const t=Number.parseInt(String(e),10);if(!Number.isNaN(t)&&t>V.EMPTY_LENGTH)return t}return N.slider.defaultTransitionTime}processSlideData(e){return e.map(t=>({imageSrc:t.imageSrc,imageLink:t.imageLink,slideIndex:t.slideIndex,isValid:this.isSlideValid(t)})).filter(t=>t.isValid)}isSlideValid(e){return!!(e.imageSrc&&typeof e.imageSrc=="string"&&this.isValidUrl(e.imageSrc)&&typeof e.slideIndex=="number"&&e.slideIndex>V.EMPTY_LENGTH)}isValidUrl(e){try{return!!new URL(e)}catch{return e.startsWith("/")||e.startsWith("./")||e.startsWith("data:")}}getForumAttribute(e){try{const t=q&&q.forum,i=t&&t.attribute;return typeof i=="function"?i.call(t,e):void 0}catch{return}}}let he;function ft(){const s=G(),e=F();return{smoothScroll:e.documentElement&&e.documentElement.style&&"scrollBehavior"in e.documentElement.style,touch:!!("ontouchstart"in s||s.DocumentTouch&&e instanceof s.DocumentTouch)}}function Ne(){return he||(he=ft()),he}let ge;function pt(s){let{userAgent:e}=s===void 0?{}:s;const t=Ne(),i=G(),n=i.navigator.platform,r=e||i.navigator.userAgent,l={ios:!1,android:!1},a=i.screen.width,d=i.screen.height,o=r.match(/(Android);?[\s\/]+([\d.]+)?/);let u=r.match(/(iPad).*OS\s([\d_]+)/);const m=r.match(/(iPod)(.*OS\s([\d_]+))?/),h=!u&&r.match(/(iPhone\sOS|iOS)\s([\d_]+)/),g=n==="Win32";let p=n==="MacIntel";const v=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!u&&p&&t.touch&&v.indexOf(`${a}x${d}`)>=0&&(u=r.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),p=!1),o&&!g&&(l.os="android",l.android=!0),(u||h||m)&&(l.os="ios",l.ios=!0),l}function ze(s){return s===void 0&&(s={}),ge||(ge=pt(s)),ge}let ve;function mt(){const s=G(),e=ze();let t=!1;function i(){const a=s.navigator.userAgent.toLowerCase();return a.indexOf("safari")>=0&&a.indexOf("chrome")<0&&a.indexOf("android")<0}if(i()){const a=String(s.navigator.userAgent);if(a.includes("Version/")){const[d,o]=a.split("Version/")[1].split(" ")[0].split(".").map(u=>Number(u));t=d<16||d===16&&o<2}}const n=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(s.navigator.userAgent),r=i(),l=r||n&&e.ios;return{isSafari:t||r,needPerspectiveFix:t,need3dFix:l,isWebView:n}}function ke(){return ve||(ve=mt()),ve}function ht(s){let{swiper:e,on:t,emit:i}=s;const n=G();let r=null,l=null;const a=()=>{!e||e.destroyed||!e.initialized||(i("beforeResize"),i("resize"))},d=()=>{!e||e.destroyed||!e.initialized||(r=new ResizeObserver(m=>{l=n.requestAnimationFrame(()=>{const{width:h,height:g}=e;let p=h,v=g;m.forEach(E=>{let{contentBoxSize:S,contentRect:c,target:f}=E;f&&f!==e.el||(p=c?c.width:(S[0]||S).inlineSize,v=c?c.height:(S[0]||S).blockSize)}),(p!==h||v!==g)&&a()})}),r.observe(e.el))},o=()=>{l&&n.cancelAnimationFrame(l),r&&r.unobserve&&e.el&&(r.unobserve(e.el),r=null)},u=()=>{!e||e.destroyed||!e.initialized||i("orientationchange")};t("init",()=>{if(e.params.resizeObserver&&typeof n.ResizeObserver<"u"){d();return}n.addEventListener("resize",a),n.addEventListener("orientationchange",u)}),t("destroy",()=>{o(),n.removeEventListener("resize",a),n.removeEventListener("orientationchange",u)})}function gt(s){let{swiper:e,extendParams:t,on:i,emit:n}=s;const r=[],l=G(),a=function(u,m){m===void 0&&(m={});const h=l.MutationObserver||l.WebkitMutationObserver,g=new h(p=>{if(e.__preventObserver__)return;if(p.length===1){n("observerUpdate",p[0]);return}const v=function(){n("observerUpdate",p[0])};l.requestAnimationFrame?l.requestAnimationFrame(v):l.setTimeout(v,0)});g.observe(u,{attributes:typeof m.attributes>"u"?!0:m.attributes,childList:e.isElement||(typeof m.childList>"u"?!0:m).childList,characterData:typeof m.characterData>"u"?!0:m.characterData}),r.push(g)},d=()=>{if(e.params.observer){if(e.params.observeParents){const u=Ae(e.hostEl);for(let m=0;m<u.length;m+=1)a(u[m])}a(e.hostEl,{childList:e.params.observeSlideChildren}),a(e.wrapperEl,{attributes:!1})}},o=()=>{r.forEach(u=>{u.disconnect()}),r.splice(0,r.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",d),i("destroy",o)}var vt={on(s,e,t){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;const n=t?"unshift":"push";return s.split(" ").forEach(r=>{i.eventsListeners[r]||(i.eventsListeners[r]=[]),i.eventsListeners[r][n](e)}),i},once(s,e,t){const i=this;if(!i.eventsListeners||i.destroyed||typeof e!="function")return i;function n(){i.off(s,n),n.__emitterProxy&&delete n.__emitterProxy;for(var r=arguments.length,l=new Array(r),a=0;a<r;a++)l[a]=arguments[a];e.apply(i,l)}return n.__emitterProxy=e,i.on(s,n,t)},onAny(s,e){const t=this;if(!t.eventsListeners||t.destroyed||typeof s!="function")return t;const i=e?"unshift":"push";return t.eventsAnyListeners.indexOf(s)<0&&t.eventsAnyListeners[i](s),t},offAny(s){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsAnyListeners)return e;const t=e.eventsAnyListeners.indexOf(s);return t>=0&&e.eventsAnyListeners.splice(t,1),e},off(s,e){const t=this;return!t.eventsListeners||t.destroyed||!t.eventsListeners||s.split(" ").forEach(i=>{typeof e>"u"?t.eventsListeners[i]=[]:t.eventsListeners[i]&&t.eventsListeners[i].forEach((n,r)=>{(n===e||n.__emitterProxy&&n.__emitterProxy===e)&&t.eventsListeners[i].splice(r,1)})}),t},emit(){const s=this;if(!s.eventsListeners||s.destroyed||!s.eventsListeners)return s;let e,t,i;for(var n=arguments.length,r=new Array(n),l=0;l<n;l++)r[l]=arguments[l];return typeof r[0]=="string"||Array.isArray(r[0])?(e=r[0],t=r.slice(1,r.length),i=s):(e=r[0].events,t=r[0].data,i=r[0].context||s),t.unshift(i),(Array.isArray(e)?e:e.split(" ")).forEach(d=>{s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach(o=>{o.apply(i,[d,...t])}),s.eventsListeners&&s.eventsListeners[d]&&s.eventsListeners[d].forEach(o=>{o.apply(i,t)})}),s}};function wt(){const s=this;let e,t;const i=s.el;typeof s.params.width<"u"&&s.params.width!==null?e=s.params.width:e=i.clientWidth,typeof s.params.height<"u"&&s.params.height!==null?t=s.params.height:t=i.clientHeight,!(e===0&&s.isHorizontal()||t===0&&s.isVertical())&&(e=e-parseInt(X(i,"padding-left")||0,10)-parseInt(X(i,"padding-right")||0,10),t=t-parseInt(X(i,"padding-top")||0,10)-parseInt(X(i,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(s,{width:e,height:t,size:s.isHorizontal()?e:t}))}function St(){const s=this;function e(y,b){return parseFloat(y.getPropertyValue(s.getDirectionLabel(b))||0)}const t=s.params,{wrapperEl:i,slidesEl:n,size:r,rtlTranslate:l,wrongRTL:a}=s,d=s.virtual&&t.virtual.enabled,o=d?s.virtual.slides.length:s.slides.length,u=W(n,`.${s.params.slideClass}, swiper-slide`),m=d?s.virtual.slides.length:u.length;let h=[];const g=[],p=[];let v=t.slidesOffsetBefore;typeof v=="function"&&(v=t.slidesOffsetBefore.call(s));let E=t.slidesOffsetAfter;typeof E=="function"&&(E=t.slidesOffsetAfter.call(s));const S=s.snapGrid.length,c=s.slidesGrid.length;let f=t.spaceBetween,w=-v,T=0,x=0;if(typeof r>"u")return;typeof f=="string"&&f.indexOf("%")>=0?f=parseFloat(f.replace("%",""))/100*r:typeof f=="string"&&(f=parseFloat(f)),s.virtualSize=-f,u.forEach(y=>{l?y.style.marginLeft="":y.style.marginRight="",y.style.marginBottom="",y.style.marginTop=""}),t.centeredSlides&&t.cssMode&&(ne(i,"--swiper-centered-offset-before",""),ne(i,"--swiper-centered-offset-after",""));const C=t.grid&&t.grid.rows>1&&s.grid;C?s.grid.initSlides(u):s.grid&&s.grid.unsetSlides();let I;const L=t.slidesPerView==="auto"&&t.breakpoints&&Object.keys(t.breakpoints).filter(y=>typeof t.breakpoints[y].slidesPerView<"u").length>0;for(let y=0;y<m;y+=1){I=0;let b;if(u[y]&&(b=u[y]),C&&s.grid.updateSlide(y,b,u),!(u[y]&&X(b,"display")==="none")){if(t.slidesPerView==="auto"){L&&(u[y].style[s.getDirectionLabel("width")]="");const A=getComputedStyle(b),O=b.style.transform,_=b.style.webkitTransform;if(O&&(b.style.transform="none"),_&&(b.style.webkitTransform="none"),t.roundLengths)I=s.isHorizontal()?me(b,"width"):me(b,"height");else{const D=e(A,"width"),M=e(A,"padding-left"),B=e(A,"padding-right"),P=e(A,"margin-left"),z=e(A,"margin-right"),R=A.getPropertyValue("box-sizing");if(R&&R==="border-box")I=D+P+z;else{const{clientWidth:U,offsetWidth:ce}=b;I=D+M+B+P+z+(ce-U)}}O&&(b.style.transform=O),_&&(b.style.webkitTransform=_),t.roundLengths&&(I=Math.floor(I))}else I=(r-(t.slidesPerView-1)*f)/t.slidesPerView,t.roundLengths&&(I=Math.floor(I)),u[y]&&(u[y].style[s.getDirectionLabel("width")]=`${I}px`);u[y]&&(u[y].swiperSlideSize=I),p.push(I),t.centeredSlides?(w=w+I/2+T/2+f,T===0&&y!==0&&(w=w-r/2-f),y===0&&(w=w-r/2-f),Math.abs(w)<1/1e3&&(w=0),t.roundLengths&&(w=Math.floor(w)),x%t.slidesPerGroup===0&&h.push(w),g.push(w)):(t.roundLengths&&(w=Math.floor(w)),(x-Math.min(s.params.slidesPerGroupSkip,x))%s.params.slidesPerGroup===0&&h.push(w),g.push(w),w=w+I+f),s.virtualSize+=I+f,T=I,x+=1}}if(s.virtualSize=Math.max(s.virtualSize,r)+E,l&&a&&(t.effect==="slide"||t.effect==="coverflow")&&(i.style.width=`${s.virtualSize+f}px`),t.setWrapperSize&&(i.style[s.getDirectionLabel("width")]=`${s.virtualSize+f}px`),C&&s.grid.updateWrapperSize(I,h),!t.centeredSlides){const y=[];for(let b=0;b<h.length;b+=1){let A=h[b];t.roundLengths&&(A=Math.floor(A)),h[b]<=s.virtualSize-r&&y.push(A)}h=y,Math.floor(s.virtualSize-r)-Math.floor(h[h.length-1])>1&&h.push(s.virtualSize-r)}if(d&&t.loop){const y=p[0]+f;if(t.slidesPerGroup>1){const b=Math.ceil((s.virtual.slidesBefore+s.virtual.slidesAfter)/t.slidesPerGroup),A=y*t.slidesPerGroup;for(let O=0;O<b;O+=1)h.push(h[h.length-1]+A)}for(let b=0;b<s.virtual.slidesBefore+s.virtual.slidesAfter;b+=1)t.slidesPerGroup===1&&h.push(h[h.length-1]+y),g.push(g[g.length-1]+y),s.virtualSize+=y}if(h.length===0&&(h=[0]),f!==0){const y=s.isHorizontal()&&l?"marginLeft":s.getDirectionLabel("marginRight");u.filter((b,A)=>!t.cssMode||t.loop?!0:A!==u.length-1).forEach(b=>{b.style[y]=`${f}px`})}if(t.centeredSlides&&t.centeredSlidesBounds){let y=0;p.forEach(A=>{y+=A+(f||0)}),y-=f;const b=y>r?y-r:0;h=h.map(A=>A<=0?-v:A>b?b+E:A)}if(t.centerInsufficientSlides){let y=0;p.forEach(A=>{y+=A+(f||0)}),y-=f;const b=(t.slidesOffsetBefore||0)+(t.slidesOffsetAfter||0);if(y+b<r){const A=(r-y-b)/2;h.forEach((O,_)=>{h[_]=O-A}),g.forEach((O,_)=>{g[_]=O+A})}}if(Object.assign(s,{slides:u,snapGrid:h,slidesGrid:g,slidesSizesGrid:p}),t.centeredSlides&&t.cssMode&&!t.centeredSlidesBounds){ne(i,"--swiper-centered-offset-before",`${-h[0]}px`),ne(i,"--swiper-centered-offset-after",`${s.size/2-p[p.length-1]/2}px`);const y=-s.snapGrid[0],b=-s.slidesGrid[0];s.snapGrid=s.snapGrid.map(A=>A+y),s.slidesGrid=s.slidesGrid.map(A=>A+b)}if(m!==o&&s.emit("slidesLengthChange"),h.length!==S&&(s.params.watchOverflow&&s.checkOverflow(),s.emit("snapGridLengthChange")),g.length!==c&&s.emit("slidesGridLengthChange"),t.watchSlidesProgress&&s.updateSlidesOffset(),s.emit("slidesUpdated"),!d&&!t.cssMode&&(t.effect==="slide"||t.effect==="fade")){const y=`${t.containerModifierClass}backface-hidden`,b=s.el.classList.contains(y);m<=t.maxBackfaceHiddenSlides?b||s.el.classList.add(y):b&&s.el.classList.remove(y)}}function Tt(s){const e=this,t=[],i=e.virtual&&e.params.virtual.enabled;let n=0,r;typeof s=="number"?e.setTransition(s):s===!0&&e.setTransition(e.params.speed);const l=a=>i?e.slides[e.getSlideIndexByData(a)]:e.slides[a];if(e.params.slidesPerView!=="auto"&&e.params.slidesPerView>1)if(e.params.centeredSlides)(e.visibleSlides||[]).forEach(a=>{t.push(a)});else for(r=0;r<Math.ceil(e.params.slidesPerView);r+=1){const a=e.activeIndex+r;if(a>e.slides.length&&!i)break;t.push(l(a))}else t.push(l(e.activeIndex));for(r=0;r<t.length;r+=1)if(typeof t[r]<"u"){const a=t[r].offsetHeight;n=a>n?a:n}(n||n===0)&&(e.wrapperEl.style.height=`${n}px`)}function yt(){const s=this,e=s.slides,t=s.isElement?s.isHorizontal()?s.wrapperEl.offsetLeft:s.wrapperEl.offsetTop:0;for(let i=0;i<e.length;i+=1)e[i].swiperSlideOffset=(s.isHorizontal()?e[i].offsetLeft:e[i].offsetTop)-t-s.cssOverflowAdjustment()}const Be=(s,e,t)=>{e&&!s.classList.contains(t)?s.classList.add(t):!e&&s.classList.contains(t)&&s.classList.remove(t)};function Et(s){s===void 0&&(s=this&&this.translate||0);const e=this,t=e.params,{slides:i,rtlTranslate:n,snapGrid:r}=e;if(i.length===0)return;typeof i[0].swiperSlideOffset>"u"&&e.updateSlidesOffset();let l=-s;n&&(l=s),e.visibleSlidesIndexes=[],e.visibleSlides=[];let a=t.spaceBetween;typeof a=="string"&&a.indexOf("%")>=0?a=parseFloat(a.replace("%",""))/100*e.size:typeof a=="string"&&(a=parseFloat(a));for(let d=0;d<i.length;d+=1){const o=i[d];let u=o.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(u-=i[0].swiperSlideOffset);const m=(l+(t.centeredSlides?e.minTranslate():0)-u)/(o.swiperSlideSize+a),h=(l-r[0]+(t.centeredSlides?e.minTranslate():0)-u)/(o.swiperSlideSize+a),g=-(l-u),p=g+e.slidesSizesGrid[d],v=g>=0&&g<=e.size-e.slidesSizesGrid[d],E=g>=0&&g<e.size-1||p>1&&p<=e.size||g<=0&&p>=e.size;E&&(e.visibleSlides.push(o),e.visibleSlidesIndexes.push(d)),Be(o,E,t.slideVisibleClass),Be(o,v,t.slideFullyVisibleClass),o.progress=n?-m:m,o.originalProgress=n?-h:h}}function bt(s){const e=this;if(typeof s>"u"){const u=e.rtlTranslate?-1:1;s=e&&e.translate&&e.translate*u||0}const t=e.params,i=e.maxTranslate()-e.minTranslate();let{progress:n,isBeginning:r,isEnd:l,progressLoop:a}=e;const d=r,o=l;if(i===0)n=0,r=!0,l=!0;else{n=(s-e.minTranslate())/i;const u=Math.abs(s-e.minTranslate())<1,m=Math.abs(s-e.maxTranslate())<1;r=u||n<=0,l=m||n>=1,u&&(n=0),m&&(n=1)}if(t.loop){const u=e.getSlideIndexByData(0),m=e.getSlideIndexByData(e.slides.length-1),h=e.slidesGrid[u],g=e.slidesGrid[m],p=e.slidesGrid[e.slidesGrid.length-1],v=Math.abs(s);v>=h?a=(v-h)/p:a=(v+p-g)/p,a>1&&(a-=1)}Object.assign(e,{progress:n,progressLoop:a,isBeginning:r,isEnd:l}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&e.updateSlidesProgress(s),r&&!d&&e.emit("reachBeginning toEdge"),l&&!o&&e.emit("reachEnd toEdge"),(d&&!r||o&&!l)&&e.emit("fromEdge"),e.emit("progress",n)}const we=(s,e,t)=>{e&&!s.classList.contains(t)?s.classList.add(t):!e&&s.classList.contains(t)&&s.classList.remove(t)};function xt(){const s=this,{slides:e,params:t,slidesEl:i,activeIndex:n}=s,r=s.virtual&&t.virtual.enabled,l=s.grid&&t.grid&&t.grid.rows>1,a=m=>W(i,`.${t.slideClass}${m}, swiper-slide${m}`)[0];let d,o,u;if(r)if(t.loop){let m=n-s.virtual.slidesBefore;m<0&&(m=s.virtual.slides.length+m),m>=s.virtual.slides.length&&(m-=s.virtual.slides.length),d=a(`[data-swiper-slide-index="${m}"]`)}else d=a(`[data-swiper-slide-index="${n}"]`);else l?(d=e.find(m=>m.column===n),u=e.find(m=>m.column===n+1),o=e.find(m=>m.column===n-1)):d=e[n];d&&(l||(u=rt(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!u&&(u=e[0]),o=nt(d,`.${t.slideClass}, swiper-slide`)[0],t.loop&&!o===0&&(o=e[e.length-1]))),e.forEach(m=>{we(m,m===d,t.slideActiveClass),we(m,m===u,t.slideNextClass),we(m,m===o,t.slidePrevClass)}),s.emitSlidesClasses()}const de=(s,e)=>{if(!s||s.destroyed||!s.params)return;const t=()=>s.isElement?"swiper-slide":`.${s.params.slideClass}`,i=e.closest(t());if(i){let n=i.querySelector(`.${s.params.lazyPreloaderClass}`);!n&&s.isElement&&(i.shadowRoot?n=i.shadowRoot.querySelector(`.${s.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{i.shadowRoot&&(n=i.shadowRoot.querySelector(`.${s.params.lazyPreloaderClass}`),n&&n.remove())})),n&&n.remove()}},Se=(s,e)=>{if(!s.slides[e])return;const t=s.slides[e].querySelector('[loading="lazy"]');t&&t.removeAttribute("loading")},Te=s=>{if(!s||s.destroyed||!s.params)return;let e=s.params.lazyPreloadPrevNext;const t=s.slides.length;if(!t||!e||e<0)return;e=Math.min(e,t);const i=s.params.slidesPerView==="auto"?s.slidesPerViewDynamic():Math.ceil(s.params.slidesPerView),n=s.activeIndex;if(s.params.grid&&s.params.grid.rows>1){const l=n,a=[l-e];a.push(...Array.from({length:e}).map((d,o)=>l+i+o)),s.slides.forEach((d,o)=>{a.includes(d.column)&&Se(s,o)});return}const r=n+i-1;if(s.params.rewind||s.params.loop)for(let l=n-e;l<=r+e;l+=1){const a=(l%t+t)%t;(a<n||a>r)&&Se(s,a)}else for(let l=Math.max(n-e,0);l<=Math.min(r+e,t-1);l+=1)l!==n&&(l>r||l<n)&&Se(s,l)};function It(s){const{slidesGrid:e,params:t}=s,i=s.rtlTranslate?s.translate:-s.translate;let n;for(let r=0;r<e.length;r+=1)typeof e[r+1]<"u"?i>=e[r]&&i<e[r+1]-(e[r+1]-e[r])/2?n=r:i>=e[r]&&i<e[r+1]&&(n=r+1):i>=e[r]&&(n=r);return t.normalizeSlideIndex&&(n<0||typeof n>"u")&&(n=0),n}function Ct(s){const e=this,t=e.rtlTranslate?e.translate:-e.translate,{snapGrid:i,params:n,activeIndex:r,realIndex:l,snapIndex:a}=e;let d=s,o;const u=g=>{let p=g-e.virtual.slidesBefore;return p<0&&(p=e.virtual.slides.length+p),p>=e.virtual.slides.length&&(p-=e.virtual.slides.length),p};if(typeof d>"u"&&(d=It(e)),i.indexOf(t)>=0)o=i.indexOf(t);else{const g=Math.min(n.slidesPerGroupSkip,d);o=g+Math.floor((d-g)/n.slidesPerGroup)}if(o>=i.length&&(o=i.length-1),d===r&&!e.params.loop){o!==a&&(e.snapIndex=o,e.emit("snapIndexChange"));return}if(d===r&&e.params.loop&&e.virtual&&e.params.virtual.enabled){e.realIndex=u(d);return}const m=e.grid&&n.grid&&n.grid.rows>1;let h;if(e.virtual&&n.virtual.enabled&&n.loop)h=u(d);else if(m){const g=e.slides.find(v=>v.column===d);let p=parseInt(g.getAttribute("data-swiper-slide-index"),10);Number.isNaN(p)&&(p=Math.max(e.slides.indexOf(g),0)),h=Math.floor(p/n.grid.rows)}else if(e.slides[d]){const g=e.slides[d].getAttribute("data-swiper-slide-index");g?h=parseInt(g,10):h=d}else h=d;Object.assign(e,{previousSnapIndex:a,snapIndex:o,previousRealIndex:l,realIndex:h,previousIndex:r,activeIndex:d}),e.initialized&&Te(e),e.emit("activeIndexChange"),e.emit("snapIndexChange"),(e.initialized||e.params.runCallbacksOnInit)&&(l!==h&&e.emit("realIndexChange"),e.emit("slideChange"))}function Lt(s,e){const t=this,i=t.params;let n=s.closest(`.${i.slideClass}, swiper-slide`);!n&&t.isElement&&e&&e.length>1&&e.includes(s)&&[...e.slice(e.indexOf(s)+1,e.length)].forEach(a=>{!n&&a.matches&&a.matches(`.${i.slideClass}, swiper-slide`)&&(n=a)});let r=!1,l;if(n){for(let a=0;a<t.slides.length;a+=1)if(t.slides[a]===n){r=!0,l=a;break}}if(n&&r)t.clickedSlide=n,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(n.getAttribute("data-swiper-slide-index"),10):t.clickedIndex=l;else{t.clickedSlide=void 0,t.clickedIndex=void 0;return}i.slideToClickedSlide&&t.clickedIndex!==void 0&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var Mt={updateSize:wt,updateSlides:St,updateAutoHeight:Tt,updateSlidesOffset:yt,updateSlidesProgress:Et,updateProgress:bt,updateSlidesClasses:xt,updateActiveIndex:Ct,updateClickedSlide:Lt};function Pt(s){s===void 0&&(s=this.isHorizontal()?"x":"y");const e=this,{params:t,rtlTranslate:i,translate:n,wrapperEl:r}=e;if(t.virtualTranslate)return i?-n:n;if(t.cssMode)return n;let l=et(r,s);return l+=e.cssOverflowAdjustment(),i&&(l=-l),l||0}function At(s,e){const t=this,{rtlTranslate:i,params:n,wrapperEl:r,progress:l}=t;let a=0,d=0;const o=0;t.isHorizontal()?a=i?-s:s:d=s,n.roundLengths&&(a=Math.floor(a),d=Math.floor(d)),t.previousTranslate=t.translate,t.translate=t.isHorizontal()?a:d,n.cssMode?r[t.isHorizontal()?"scrollLeft":"scrollTop"]=t.isHorizontal()?-a:-d:n.virtualTranslate||(t.isHorizontal()?a-=t.cssOverflowAdjustment():d-=t.cssOverflowAdjustment(),r.style.transform=`translate3d(${a}px, ${d}px, ${o}px)`);let u;const m=t.maxTranslate()-t.minTranslate();m===0?u=0:u=(s-t.minTranslate())/m,u!==l&&t.updateProgress(s),t.emit("setTranslate",t.translate,e)}function Ot(){return-this.snapGrid[0]}function _t(){return-this.snapGrid[this.snapGrid.length-1]}function Dt(s,e,t,i,n){s===void 0&&(s=0),e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),i===void 0&&(i=!0);const r=this,{params:l,wrapperEl:a}=r;if(r.animating&&l.preventInteractionOnTransition)return!1;const d=r.minTranslate(),o=r.maxTranslate();let u;if(i&&s>d?u=d:i&&s<o?u=o:u=s,r.updateProgress(u),l.cssMode){const m=r.isHorizontal();if(e===0)a[m?"scrollLeft":"scrollTop"]=-u;else{if(!r.support.smoothScroll)return Pe({swiper:r,targetPosition:-u,side:m?"left":"top"}),!0;a.scrollTo({[m?"left":"top"]:-u,behavior:"smooth"})}return!0}return e===0?(r.setTransition(0),r.setTranslate(u),t&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionEnd"))):(r.setTransition(e),r.setTranslate(u),t&&(r.emit("beforeTransitionStart",e,n),r.emit("transitionStart")),r.animating||(r.animating=!0,r.onTranslateToWrapperTransitionEnd||(r.onTranslateToWrapperTransitionEnd=function(h){!r||r.destroyed||h.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onTranslateToWrapperTransitionEnd),r.onTranslateToWrapperTransitionEnd=null,delete r.onTranslateToWrapperTransitionEnd,r.animating=!1,t&&r.emit("transitionEnd"))}),r.wrapperEl.addEventListener("transitionend",r.onTranslateToWrapperTransitionEnd))),!0}var Nt={getTranslate:Pt,setTranslate:At,minTranslate:Ot,maxTranslate:_t,translateTo:Dt};function zt(s,e){const t=this;t.params.cssMode||(t.wrapperEl.style.transitionDuration=`${s}ms`,t.wrapperEl.style.transitionDelay=s===0?"0ms":""),t.emit("setTransition",s,e)}function Ge(s){let{swiper:e,runCallbacks:t,direction:i,step:n}=s;const{activeIndex:r,previousIndex:l}=e;let a=i;a||(r>l?a="next":r<l?a="prev":a="reset"),e.emit(`transition${n}`),t&&a==="reset"?e.emit(`slideResetTransition${n}`):t&&r!==l&&(e.emit(`slideChangeTransition${n}`),a==="next"?e.emit(`slideNextTransition${n}`):e.emit(`slidePrevTransition${n}`))}function kt(s,e){s===void 0&&(s=!0);const t=this,{params:i}=t;i.cssMode||(i.autoHeight&&t.updateAutoHeight(),Ge({swiper:t,runCallbacks:s,direction:e,step:"Start"}))}function Bt(s,e){s===void 0&&(s=!0);const t=this,{params:i}=t;t.animating=!1,!i.cssMode&&(t.setTransition(0),Ge({swiper:t,runCallbacks:s,direction:e,step:"End"}))}var Gt={setTransition:zt,transitionStart:kt,transitionEnd:Bt};function Rt(s,e,t,i,n){s===void 0&&(s=0),t===void 0&&(t=!0),typeof s=="string"&&(s=parseInt(s,10));const r=this;let l=s;l<0&&(l=0);const{params:a,snapGrid:d,slidesGrid:o,previousIndex:u,activeIndex:m,rtlTranslate:h,wrapperEl:g,enabled:p}=r;if(!p&&!i&&!n||r.destroyed||r.animating&&a.preventInteractionOnTransition)return!1;typeof e>"u"&&(e=r.params.speed);const v=Math.min(r.params.slidesPerGroupSkip,l);let E=v+Math.floor((l-v)/r.params.slidesPerGroup);E>=d.length&&(E=d.length-1);const S=-d[E];if(a.normalizeSlideIndex)for(let C=0;C<o.length;C+=1){const I=-Math.floor(S*100),L=Math.floor(o[C]*100),y=Math.floor(o[C+1]*100);typeof o[C+1]<"u"?I>=L&&I<y-(y-L)/2?l=C:I>=L&&I<y&&(l=C+1):I>=L&&(l=C)}if(r.initialized&&l!==m&&(!r.allowSlideNext&&(h?S>r.translate&&S>r.minTranslate():S<r.translate&&S<r.minTranslate())||!r.allowSlidePrev&&S>r.translate&&S>r.maxTranslate()&&(m||0)!==l))return!1;l!==(u||0)&&t&&r.emit("beforeSlideChangeStart"),r.updateProgress(S);let c;l>m?c="next":l<m?c="prev":c="reset";const f=r.virtual&&r.params.virtual.enabled;if(!(f&&n)&&(h&&-S===r.translate||!h&&S===r.translate))return r.updateActiveIndex(l),a.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),a.effect!=="slide"&&r.setTranslate(S),c!=="reset"&&(r.transitionStart(t,c),r.transitionEnd(t,c)),!1;if(a.cssMode){const C=r.isHorizontal(),I=h?S:-S;if(e===0)f&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),f&&!r._cssModeVirtualInitialSet&&r.params.initialSlide>0?(r._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{g[C?"scrollLeft":"scrollTop"]=I})):g[C?"scrollLeft":"scrollTop"]=I,f&&requestAnimationFrame(()=>{r.wrapperEl.style.scrollSnapType="",r._immediateVirtual=!1});else{if(!r.support.smoothScroll)return Pe({swiper:r,targetPosition:I,side:C?"left":"top"}),!0;g.scrollTo({[C?"left":"top"]:I,behavior:"smooth"})}return!0}const x=ke().isSafari;return f&&!n&&x&&r.isElement&&r.virtual.update(!1,!1,l),r.setTransition(e),r.setTranslate(S),r.updateActiveIndex(l),r.updateSlidesClasses(),r.emit("beforeTransitionStart",e,i),r.transitionStart(t,c),e===0?r.transitionEnd(t,c):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(I){!r||r.destroyed||I.target===this&&(r.wrapperEl.removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(t,c))}),r.wrapperEl.addEventListener("transitionend",r.onSlideToWrapperTransitionEnd)),!0}function Vt(s,e,t,i){s===void 0&&(s=0),t===void 0&&(t=!0),typeof s=="string"&&(s=parseInt(s,10));const n=this;if(n.destroyed)return;typeof e>"u"&&(e=n.params.speed);const r=n.grid&&n.params.grid&&n.params.grid.rows>1;let l=s;if(n.params.loop)if(n.virtual&&n.params.virtual.enabled)l=l+n.virtual.slidesBefore;else{let a;if(r){const h=l*n.params.grid.rows;a=n.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===h).column}else a=n.getSlideIndexByData(l);const d=r?Math.ceil(n.slides.length/n.params.grid.rows):n.slides.length,{centeredSlides:o}=n.params;let u=n.params.slidesPerView;u==="auto"?u=n.slidesPerViewDynamic():(u=Math.ceil(parseFloat(n.params.slidesPerView,10)),o&&u%2===0&&(u=u+1));let m=d-a<u;if(o&&(m=m||a<Math.ceil(u/2)),i&&o&&n.params.slidesPerView!=="auto"&&!r&&(m=!1),m){const h=o?a<n.activeIndex?"prev":"next":a-n.activeIndex-1<n.params.slidesPerView?"next":"prev";n.loopFix({direction:h,slideTo:!0,activeSlideIndex:h==="next"?a+1:a-d+1,slideRealIndex:h==="next"?n.realIndex:void 0})}if(r){const h=l*n.params.grid.rows;l=n.slides.find(g=>g.getAttribute("data-swiper-slide-index")*1===h).column}else l=n.getSlideIndexByData(l)}return requestAnimationFrame(()=>{n.slideTo(l,e,t,i)}),n}function $t(s,e,t){e===void 0&&(e=!0);const i=this,{enabled:n,params:r,animating:l}=i;if(!n||i.destroyed)return i;typeof s>"u"&&(s=i.params.speed);let a=r.slidesPerGroup;r.slidesPerView==="auto"&&r.slidesPerGroup===1&&r.slidesPerGroupAuto&&(a=Math.max(i.slidesPerViewDynamic("current",!0),1));const d=i.activeIndex<r.slidesPerGroupSkip?1:a,o=i.virtual&&r.virtual.enabled;if(r.loop){if(l&&!o&&r.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&r.cssMode)return requestAnimationFrame(()=>{i.slideTo(i.activeIndex+d,s,e,t)}),!0}return r.rewind&&i.isEnd?i.slideTo(0,s,e,t):i.slideTo(i.activeIndex+d,s,e,t)}function Ht(s,e,t){e===void 0&&(e=!0);const i=this,{params:n,snapGrid:r,slidesGrid:l,rtlTranslate:a,enabled:d,animating:o}=i;if(!d||i.destroyed)return i;typeof s>"u"&&(s=i.params.speed);const u=i.virtual&&n.virtual.enabled;if(n.loop){if(o&&!u&&n.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}const m=a?i.translate:-i.translate;function h(c){return c<0?-Math.floor(Math.abs(c)):Math.floor(c)}const g=h(m),p=r.map(c=>h(c)),v=n.freeMode&&n.freeMode.enabled;let E=r[p.indexOf(g)-1];if(typeof E>"u"&&(n.cssMode||v)){let c;r.forEach((f,w)=>{g>=f&&(c=w)}),typeof c<"u"&&(E=v?r[c]:r[c>0?c-1:c])}let S=0;if(typeof E<"u"&&(S=l.indexOf(E),S<0&&(S=i.activeIndex-1),n.slidesPerView==="auto"&&n.slidesPerGroup===1&&n.slidesPerGroupAuto&&(S=S-i.slidesPerViewDynamic("previous",!0)+1,S=Math.max(S,0))),n.rewind&&i.isBeginning){const c=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(c,s,e,t)}else if(n.loop&&i.activeIndex===0&&n.cssMode)return requestAnimationFrame(()=>{i.slideTo(S,s,e,t)}),!0;return i.slideTo(S,s,e,t)}function Ft(s,e,t){e===void 0&&(e=!0);const i=this;if(!i.destroyed)return typeof s>"u"&&(s=i.params.speed),i.slideTo(i.activeIndex,s,e,t)}function Wt(s,e,t,i){e===void 0&&(e=!0),i===void 0&&(i=.5);const n=this;if(n.destroyed)return;typeof s>"u"&&(s=n.params.speed);let r=n.activeIndex;const l=Math.min(n.params.slidesPerGroupSkip,r),a=l+Math.floor((r-l)/n.params.slidesPerGroup),d=n.rtlTranslate?n.translate:-n.translate;if(d>=n.snapGrid[a]){const o=n.snapGrid[a],u=n.snapGrid[a+1];d-o>(u-o)*i&&(r+=n.params.slidesPerGroup)}else{const o=n.snapGrid[a-1],u=n.snapGrid[a];d-o<=(u-o)*i&&(r-=n.params.slidesPerGroup)}return r=Math.max(r,0),r=Math.min(r,n.slidesGrid.length-1),n.slideTo(r,s,e,t)}function Xt(){const s=this;if(s.destroyed)return;const{params:e,slidesEl:t}=s,i=e.slidesPerView==="auto"?s.slidesPerViewDynamic():e.slidesPerView;let n=s.getSlideIndexWhenGrid(s.clickedIndex),r;const l=s.isElement?"swiper-slide":`.${e.slideClass}`,a=s.grid&&s.params.grid&&s.params.grid.rows>1;if(e.loop){if(s.animating)return;r=parseInt(s.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?s.slideToLoop(r):n>(a?(s.slides.length-i)/2-(s.params.grid.rows-1):s.slides.length-i)?(s.loopFix(),n=s.getSlideIndex(W(t,`${l}[data-swiper-slide-index="${r}"]`)[0]),Me(()=>{s.slideTo(n)})):s.slideTo(n)}else s.slideTo(n)}var qt={slideTo:Rt,slideToLoop:Vt,slideNext:$t,slidePrev:Ht,slideReset:Ft,slideToClosest:Wt,slideToClickedSlide:Xt};function jt(s,e){const t=this,{params:i,slidesEl:n}=t;if(!i.loop||t.virtual&&t.params.virtual.enabled)return;const r=()=>{W(n,`.${i.slideClass}, swiper-slide`).forEach((g,p)=>{g.setAttribute("data-swiper-slide-index",p)})},l=()=>{const h=W(n,`.${i.slideBlankClass}`);h.forEach(g=>{g.remove()}),h.length>0&&(t.recalcSlides(),t.updateSlides())},a=t.grid&&i.grid&&i.grid.rows>1;i.loopAddBlankSlides&&(i.slidesPerGroup>1||a)&&l();const d=i.slidesPerGroup*(a?i.grid.rows:1),o=t.slides.length%d!==0,u=a&&t.slides.length%i.grid.rows!==0,m=h=>{for(let g=0;g<h;g+=1){const p=t.isElement?ae("swiper-slide",[i.slideBlankClass]):ae("div",[i.slideClass,i.slideBlankClass]);t.slidesEl.append(p)}};if(o){if(i.loopAddBlankSlides){const h=d-t.slides.length%d;m(h),t.recalcSlides(),t.updateSlides()}else re("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else if(u){if(i.loopAddBlankSlides){const h=i.grid.rows-t.slides.length%i.grid.rows;m(h),t.recalcSlides(),t.updateSlides()}else re("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)");r()}else r();t.loopFix({slideRealIndex:s,direction:i.centeredSlides?void 0:"next",initial:e})}function Yt(s){let{slideRealIndex:e,slideTo:t=!0,direction:i,setTranslate:n,activeSlideIndex:r,initial:l,byController:a,byMousewheel:d}=s===void 0?{}:s;const o=this;if(!o.params.loop)return;o.emit("beforeLoopFix");const{slides:u,allowSlidePrev:m,allowSlideNext:h,slidesEl:g,params:p}=o,{centeredSlides:v,initialSlide:E}=p;if(o.allowSlidePrev=!0,o.allowSlideNext=!0,o.virtual&&p.virtual.enabled){t&&(!p.centeredSlides&&o.snapIndex===0?o.slideTo(o.virtual.slides.length,0,!1,!0):p.centeredSlides&&o.snapIndex<p.slidesPerView?o.slideTo(o.virtual.slides.length+o.snapIndex,0,!1,!0):o.snapIndex===o.snapGrid.length-1&&o.slideTo(o.virtual.slidesBefore,0,!1,!0)),o.allowSlidePrev=m,o.allowSlideNext=h,o.emit("loopFix");return}let S=p.slidesPerView;S==="auto"?S=o.slidesPerViewDynamic():(S=Math.ceil(parseFloat(p.slidesPerView,10)),v&&S%2===0&&(S=S+1));const c=p.slidesPerGroupAuto?S:p.slidesPerGroup;let f=v?Math.max(c,Math.ceil(S/2)):c;f%c!==0&&(f+=c-f%c),f+=p.loopAdditionalSlides,o.loopedSlides=f;const w=o.grid&&p.grid&&p.grid.rows>1;u.length<S+f||o.params.effect==="cards"&&u.length<S+f*2?re("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):w&&p.grid.fill==="row"&&re("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");const T=[],x=[],C=w?Math.ceil(u.length/p.grid.rows):u.length,I=l&&C-E<S&&!v;let L=I?E:o.activeIndex;typeof r>"u"?r=o.getSlideIndex(u.find(M=>M.classList.contains(p.slideActiveClass))):L=r;const y=i==="next"||!i,b=i==="prev"||!i;let A=0,O=0;const D=(w?u[r].column:r)+(v&&typeof n>"u"?-S/2+.5:0);if(D<f){A=Math.max(f-D,c);for(let M=0;M<f-D;M+=1){const B=M-Math.floor(M/C)*C;if(w){const P=C-B-1;for(let z=u.length-1;z>=0;z-=1)u[z].column===P&&T.push(z)}else T.push(C-B-1)}}else if(D+S>C-f){O=Math.max(D-(C-f*2),c),I&&(O=Math.max(O,S-C+E+1));for(let M=0;M<O;M+=1){const B=M-Math.floor(M/C)*C;w?u.forEach((P,z)=>{P.column===B&&x.push(z)}):x.push(B)}}if(o.__preventObserver__=!0,requestAnimationFrame(()=>{o.__preventObserver__=!1}),o.params.effect==="cards"&&u.length<S+f*2&&(x.includes(r)&&x.splice(x.indexOf(r),1),T.includes(r)&&T.splice(T.indexOf(r),1)),b&&T.forEach(M=>{u[M].swiperLoopMoveDOM=!0,g.prepend(u[M]),u[M].swiperLoopMoveDOM=!1}),y&&x.forEach(M=>{u[M].swiperLoopMoveDOM=!0,g.append(u[M]),u[M].swiperLoopMoveDOM=!1}),o.recalcSlides(),p.slidesPerView==="auto"?o.updateSlides():w&&(T.length>0&&b||x.length>0&&y)&&o.slides.forEach((M,B)=>{o.grid.updateSlide(B,M,o.slides)}),p.watchSlidesProgress&&o.updateSlidesOffset(),t){if(T.length>0&&b){if(typeof e>"u"){const M=o.slidesGrid[L],P=o.slidesGrid[L+A]-M;d?o.setTranslate(o.translate-P):(o.slideTo(L+Math.ceil(A),0,!1,!0),n&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-P,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-P))}else if(n){const M=w?T.length/p.grid.rows:T.length;o.slideTo(o.activeIndex+M,0,!1,!0),o.touchEventsData.currentTranslate=o.translate}}else if(x.length>0&&y)if(typeof e>"u"){const M=o.slidesGrid[L],P=o.slidesGrid[L-O]-M;d?o.setTranslate(o.translate-P):(o.slideTo(L-O,0,!1,!0),n&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-P,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-P))}else{const M=w?x.length/p.grid.rows:x.length;o.slideTo(o.activeIndex-M,0,!1,!0)}}if(o.allowSlidePrev=m,o.allowSlideNext=h,o.controller&&o.controller.control&&!a){const M={slideRealIndex:e,direction:i,setTranslate:n,activeSlideIndex:r,byController:!0};Array.isArray(o.controller.control)?o.controller.control.forEach(B=>{!B.destroyed&&B.params.loop&&B.loopFix({...M,slideTo:B.params.slidesPerView===p.slidesPerView?t:!1})}):o.controller.control instanceof o.constructor&&o.controller.control.params.loop&&o.controller.control.loopFix({...M,slideTo:o.controller.control.params.slidesPerView===p.slidesPerView?t:!1})}o.emit("loopFix")}function Ut(){const s=this,{params:e,slidesEl:t}=s;if(!e.loop||!t||s.virtual&&s.params.virtual.enabled)return;s.recalcSlides();const i=[];s.slides.forEach(n=>{const r=typeof n.swiperSlideIndex>"u"?n.getAttribute("data-swiper-slide-index")*1:n.swiperSlideIndex;i[r]=n}),s.slides.forEach(n=>{n.removeAttribute("data-swiper-slide-index")}),i.forEach(n=>{t.append(n)}),s.recalcSlides(),s.slideTo(s.realIndex,0)}var Kt={loopCreate:jt,loopFix:Yt,loopDestroy:Ut};function Qt(s){const e=this;if(!e.params.simulateTouch||e.params.watchOverflow&&e.isLocked||e.params.cssMode)return;const t=e.params.touchEventsTarget==="container"?e.el:e.wrapperEl;e.isElement&&(e.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=s?"grabbing":"grab",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1})}function Jt(){const s=this;s.params.watchOverflow&&s.isLocked||s.params.cssMode||(s.isElement&&(s.__preventObserver__=!0),s[s.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",s.isElement&&requestAnimationFrame(()=>{s.__preventObserver__=!1}))}var Zt={setGrabCursor:Qt,unsetGrabCursor:Jt};function ei(s,e){e===void 0&&(e=this);function t(i){if(!i||i===F()||i===G())return null;i.assignedSlot&&(i=i.assignedSlot);const n=i.closest(s);return!n&&!i.getRootNode?null:n||t(i.getRootNode().host)}return t(e)}function Re(s,e,t){const i=G(),{params:n}=s,r=n.edgeSwipeDetection,l=n.edgeSwipeThreshold;return r&&(t<=l||t>=i.innerWidth-l)?r==="prevent"?(e.preventDefault(),!0):!1:!0}function ti(s){const e=this,t=F();let i=s;i.originalEvent&&(i=i.originalEvent);const n=e.touchEventsData;if(i.type==="pointerdown"){if(n.pointerId!==null&&n.pointerId!==i.pointerId)return;n.pointerId=i.pointerId}else i.type==="touchstart"&&i.targetTouches.length===1&&(n.touchId=i.targetTouches[0].identifier);if(i.type==="touchstart"){Re(e,i,i.targetTouches[0].pageX);return}const{params:r,touches:l,enabled:a}=e;if(!a||!r.simulateTouch&&i.pointerType==="mouse"||e.animating&&r.preventInteractionOnTransition)return;!e.animating&&r.cssMode&&r.loop&&e.loopFix();let d=i.target;if(r.touchEventsTarget==="wrapper"&&!st(d,e.wrapperEl)||"which"in i&&i.which===3||"button"in i&&i.button>0||n.isTouched&&n.isMoved)return;const o=!!r.noSwipingClass&&r.noSwipingClass!=="",u=i.composedPath?i.composedPath():i.path;o&&i.target&&i.target.shadowRoot&&u&&(d=u[0]);const m=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,h=!!(i.target&&i.target.shadowRoot);if(r.noSwiping&&(h?ei(m,d):d.closest(m))){e.allowClick=!0;return}if(r.swipeHandler&&!d.closest(r.swipeHandler))return;l.currentX=i.pageX,l.currentY=i.pageY;const g=l.currentX,p=l.currentY;if(!Re(e,i,g))return;Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=g,l.startY=p,n.touchStartTime=ie(),e.allowClick=!0,e.updateSize(),e.swipeDirection=void 0,r.threshold>0&&(n.allowThresholdMove=!1);let v=!0;d.matches(n.focusableElements)&&(v=!1,d.nodeName==="SELECT"&&(n.isTouched=!1)),t.activeElement&&t.activeElement.matches(n.focusableElements)&&t.activeElement!==d&&(i.pointerType==="mouse"||i.pointerType!=="mouse"&&!d.matches(n.focusableElements))&&t.activeElement.blur();const E=v&&e.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||E)&&!d.isContentEditable&&i.preventDefault(),r.freeMode&&r.freeMode.enabled&&e.freeMode&&e.animating&&!r.cssMode&&e.freeMode.onTouchStart(),e.emit("touchStart",i)}function ii(s){const e=F(),t=this,i=t.touchEventsData,{params:n,touches:r,rtlTranslate:l,enabled:a}=t;if(!a||!n.simulateTouch&&s.pointerType==="mouse")return;let d=s;if(d.originalEvent&&(d=d.originalEvent),d.type==="pointermove"&&(i.touchId!==null||d.pointerId!==i.pointerId))return;let o;if(d.type==="touchmove"){if(o=[...d.changedTouches].find(T=>T.identifier===i.touchId),!o||o.identifier!==i.touchId)return}else o=d;if(!i.isTouched){i.startMoving&&i.isScrolling&&t.emit("touchMoveOpposite",d);return}const u=o.pageX,m=o.pageY;if(d.preventedByNestedSwiper){r.startX=u,r.startY=m;return}if(!t.allowTouchMove){d.target.matches(i.focusableElements)||(t.allowClick=!1),i.isTouched&&(Object.assign(r,{startX:u,startY:m,currentX:u,currentY:m}),i.touchStartTime=ie());return}if(n.touchReleaseOnEdges&&!n.loop)if(t.isVertical()){if(m<r.startY&&t.translate<=t.maxTranslate()||m>r.startY&&t.translate>=t.minTranslate()){i.isTouched=!1,i.isMoved=!1;return}}else{if(l&&(u>r.startX&&-t.translate<=t.maxTranslate()||u<r.startX&&-t.translate>=t.minTranslate()))return;if(!l&&(u<r.startX&&t.translate<=t.maxTranslate()||u>r.startX&&t.translate>=t.minTranslate()))return}if(e.activeElement&&e.activeElement.matches(i.focusableElements)&&e.activeElement!==d.target&&d.pointerType!=="mouse"&&e.activeElement.blur(),e.activeElement&&d.target===e.activeElement&&d.target.matches(i.focusableElements)){i.isMoved=!0,t.allowClick=!1;return}i.allowTouchCallbacks&&t.emit("touchMove",d),r.previousX=r.currentX,r.previousY=r.currentY,r.currentX=u,r.currentY=m;const h=r.currentX-r.startX,g=r.currentY-r.startY;if(t.params.threshold&&Math.sqrt(h**2+g**2)<t.params.threshold)return;if(typeof i.isScrolling>"u"){let T;t.isHorizontal()&&r.currentY===r.startY||t.isVertical()&&r.currentX===r.startX?i.isScrolling=!1:h*h+g*g>=25&&(T=Math.atan2(Math.abs(g),Math.abs(h))*180/Math.PI,i.isScrolling=t.isHorizontal()?T>n.touchAngle:90-T>n.touchAngle)}if(i.isScrolling&&t.emit("touchMoveOpposite",d),typeof i.startMoving>"u"&&(r.currentX!==r.startX||r.currentY!==r.startY)&&(i.startMoving=!0),i.isScrolling||d.type==="touchmove"&&i.preventTouchMoveFromPointerMove){i.isTouched=!1;return}if(!i.startMoving)return;t.allowClick=!1,!n.cssMode&&d.cancelable&&d.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&d.stopPropagation();let p=t.isHorizontal()?h:g,v=t.isHorizontal()?r.currentX-r.previousX:r.currentY-r.previousY;n.oneWayMovement&&(p=Math.abs(p)*(l?1:-1),v=Math.abs(v)*(l?1:-1)),r.diff=p,p*=n.touchRatio,l&&(p=-p,v=-v);const E=t.touchesDirection;t.swipeDirection=p>0?"prev":"next",t.touchesDirection=v>0?"prev":"next";const S=t.params.loop&&!n.cssMode,c=t.touchesDirection==="next"&&t.allowSlideNext||t.touchesDirection==="prev"&&t.allowSlidePrev;if(!i.isMoved){if(S&&c&&t.loopFix({direction:t.swipeDirection}),i.startTranslate=t.getTranslate(),t.setTransition(0),t.animating){const T=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});t.wrapperEl.dispatchEvent(T)}i.allowMomentumBounce=!1,n.grabCursor&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!0),t.emit("sliderFirstMove",d)}if(new Date().getTime(),n._loopSwapReset!==!1&&i.isMoved&&i.allowThresholdMove&&E!==t.touchesDirection&&S&&c&&Math.abs(p)>=1){Object.assign(r,{startX:u,startY:m,currentX:u,currentY:m,startTranslate:i.currentTranslate}),i.loopSwapReset=!0,i.startTranslate=i.currentTranslate;return}t.emit("sliderMove",d),i.isMoved=!0,i.currentTranslate=p+i.startTranslate;let f=!0,w=n.resistanceRatio;if(n.touchReleaseOnEdges&&(w=0),p>0?(S&&c&&i.allowThresholdMove&&i.currentTranslate>(n.centeredSlides?t.minTranslate()-t.slidesSizesGrid[t.activeIndex+1]-(n.slidesPerView!=="auto"&&t.slides.length-n.slidesPerView>=2?t.slidesSizesGrid[t.activeIndex+1]+t.params.spaceBetween:0)-t.params.spaceBetween:t.minTranslate())&&t.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),i.currentTranslate>t.minTranslate()&&(f=!1,n.resistance&&(i.currentTranslate=t.minTranslate()-1+(-t.minTranslate()+i.startTranslate+p)**w))):p<0&&(S&&c&&i.allowThresholdMove&&i.currentTranslate<(n.centeredSlides?t.maxTranslate()+t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween+(n.slidesPerView!=="auto"&&t.slides.length-n.slidesPerView>=2?t.slidesSizesGrid[t.slidesSizesGrid.length-1]+t.params.spaceBetween:0):t.maxTranslate())&&t.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:t.slides.length-(n.slidesPerView==="auto"?t.slidesPerViewDynamic():Math.ceil(parseFloat(n.slidesPerView,10)))}),i.currentTranslate<t.maxTranslate()&&(f=!1,n.resistance&&(i.currentTranslate=t.maxTranslate()+1-(t.maxTranslate()-i.startTranslate-p)**w))),f&&(d.preventedByNestedSwiper=!0),!t.allowSlideNext&&t.swipeDirection==="next"&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!t.allowSlidePrev&&t.swipeDirection==="prev"&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),!t.allowSlidePrev&&!t.allowSlideNext&&(i.currentTranslate=i.startTranslate),n.threshold>0)if(Math.abs(p)>n.threshold||i.allowThresholdMove){if(!i.allowThresholdMove){i.allowThresholdMove=!0,r.startX=r.currentX,r.startY=r.currentY,i.currentTranslate=i.startTranslate,r.diff=t.isHorizontal()?r.currentX-r.startX:r.currentY-r.startY;return}}else{i.currentTranslate=i.startTranslate;return}!n.followFinger||n.cssMode||((n.freeMode&&n.freeMode.enabled&&t.freeMode||n.watchSlidesProgress)&&(t.updateActiveIndex(),t.updateSlidesClasses()),n.freeMode&&n.freeMode.enabled&&t.freeMode&&t.freeMode.onTouchMove(),t.updateProgress(i.currentTranslate),t.setTranslate(i.currentTranslate))}function si(s){const e=this,t=e.touchEventsData;let i=s;i.originalEvent&&(i=i.originalEvent);let n;if(i.type==="touchend"||i.type==="touchcancel"){if(n=[...i.changedTouches].find(T=>T.identifier===t.touchId),!n||n.identifier!==t.touchId)return}else{if(t.touchId!==null||i.pointerId!==t.pointerId)return;n=i}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(i.type)&&!(["pointercancel","contextmenu"].includes(i.type)&&(e.browser.isSafari||e.browser.isWebView)))return;t.pointerId=null,t.touchId=null;const{params:l,touches:a,rtlTranslate:d,slidesGrid:o,enabled:u}=e;if(!u||!l.simulateTouch&&i.pointerType==="mouse")return;if(t.allowTouchCallbacks&&e.emit("touchEnd",i),t.allowTouchCallbacks=!1,!t.isTouched){t.isMoved&&l.grabCursor&&e.setGrabCursor(!1),t.isMoved=!1,t.startMoving=!1;return}l.grabCursor&&t.isMoved&&t.isTouched&&(e.allowSlideNext===!0||e.allowSlidePrev===!0)&&e.setGrabCursor(!1);const m=ie(),h=m-t.touchStartTime;if(e.allowClick){const T=i.path||i.composedPath&&i.composedPath();e.updateClickedSlide(T&&T[0]||i.target,T),e.emit("tap click",i),h<300&&m-t.lastClickTime<300&&e.emit("doubleTap doubleClick",i)}if(t.lastClickTime=ie(),Me(()=>{e.destroyed||(e.allowClick=!0)}),!t.isTouched||!t.isMoved||!e.swipeDirection||a.diff===0&&!t.loopSwapReset||t.currentTranslate===t.startTranslate&&!t.loopSwapReset){t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;return}t.isTouched=!1,t.isMoved=!1,t.startMoving=!1;let g;if(l.followFinger?g=d?e.translate:-e.translate:g=-t.currentTranslate,l.cssMode)return;if(l.freeMode&&l.freeMode.enabled){e.freeMode.onTouchEnd({currentPos:g});return}const p=g>=-e.maxTranslate()&&!e.params.loop;let v=0,E=e.slidesSizesGrid[0];for(let T=0;T<o.length;T+=T<l.slidesPerGroupSkip?1:l.slidesPerGroup){const x=T<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;typeof o[T+x]<"u"?(p||g>=o[T]&&g<o[T+x])&&(v=T,E=o[T+x]-o[T]):(p||g>=o[T])&&(v=T,E=o[o.length-1]-o[o.length-2])}let S=null,c=null;l.rewind&&(e.isBeginning?c=l.virtual&&l.virtual.enabled&&e.virtual?e.virtual.slides.length-1:e.slides.length-1:e.isEnd&&(S=0));const f=(g-o[v])/E,w=v<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;if(h>l.longSwipesMs){if(!l.longSwipes){e.slideTo(e.activeIndex);return}e.swipeDirection==="next"&&(f>=l.longSwipesRatio?e.slideTo(l.rewind&&e.isEnd?S:v+w):e.slideTo(v)),e.swipeDirection==="prev"&&(f>1-l.longSwipesRatio?e.slideTo(v+w):c!==null&&f<0&&Math.abs(f)>l.longSwipesRatio?e.slideTo(c):e.slideTo(v))}else{if(!l.shortSwipes){e.slideTo(e.activeIndex);return}e.navigation&&(i.target===e.navigation.nextEl||i.target===e.navigation.prevEl)?i.target===e.navigation.nextEl?e.slideTo(v+w):e.slideTo(v):(e.swipeDirection==="next"&&e.slideTo(S!==null?S:v+w),e.swipeDirection==="prev"&&e.slideTo(c!==null?c:v))}}function Ve(){const s=this,{params:e,el:t}=s;if(t&&t.offsetWidth===0)return;e.breakpoints&&s.setBreakpoint();const{allowSlideNext:i,allowSlidePrev:n,snapGrid:r}=s,l=s.virtual&&s.params.virtual.enabled;s.allowSlideNext=!0,s.allowSlidePrev=!0,s.updateSize(),s.updateSlides(),s.updateSlidesClasses();const a=l&&e.loop;(e.slidesPerView==="auto"||e.slidesPerView>1)&&s.isEnd&&!s.isBeginning&&!s.params.centeredSlides&&!a?s.slideTo(s.slides.length-1,0,!1,!0):s.params.loop&&!l?s.slideToLoop(s.realIndex,0,!1,!0):s.slideTo(s.activeIndex,0,!1,!0),s.autoplay&&s.autoplay.running&&s.autoplay.paused&&(clearTimeout(s.autoplay.resizeTimeout),s.autoplay.resizeTimeout=setTimeout(()=>{s.autoplay&&s.autoplay.running&&s.autoplay.paused&&s.autoplay.resume()},500)),s.allowSlidePrev=n,s.allowSlideNext=i,s.params.watchOverflow&&r!==s.snapGrid&&s.checkOverflow()}function ni(s){const e=this;e.enabled&&(e.allowClick||(e.params.preventClicks&&s.preventDefault(),e.params.preventClicksPropagation&&e.animating&&(s.stopPropagation(),s.stopImmediatePropagation())))}function ri(){const s=this,{wrapperEl:e,rtlTranslate:t,enabled:i}=s;if(!i)return;s.previousTranslate=s.translate,s.isHorizontal()?s.translate=-e.scrollLeft:s.translate=-e.scrollTop,s.translate===0&&(s.translate=0),s.updateActiveIndex(),s.updateSlidesClasses();let n;const r=s.maxTranslate()-s.minTranslate();r===0?n=0:n=(s.translate-s.minTranslate())/r,n!==s.progress&&s.updateProgress(t?-s.translate:s.translate),s.emit("setTranslate",s.translate,!1)}function ai(s){const e=this;de(e,s.target),!(e.params.cssMode||e.params.slidesPerView!=="auto"&&!e.params.autoHeight)&&e.update()}function li(){const s=this;s.documentTouchHandlerProceeded||(s.documentTouchHandlerProceeded=!0,s.params.touchReleaseOnEdges&&(s.el.style.touchAction="auto"))}const $e=(s,e)=>{const t=F(),{params:i,el:n,wrapperEl:r,device:l}=s,a=!!i.nested,d=e==="on"?"addEventListener":"removeEventListener",o=e;!n||typeof n=="string"||(t[d]("touchstart",s.onDocumentTouchStart,{passive:!1,capture:a}),n[d]("touchstart",s.onTouchStart,{passive:!1}),n[d]("pointerdown",s.onTouchStart,{passive:!1}),t[d]("touchmove",s.onTouchMove,{passive:!1,capture:a}),t[d]("pointermove",s.onTouchMove,{passive:!1,capture:a}),t[d]("touchend",s.onTouchEnd,{passive:!0}),t[d]("pointerup",s.onTouchEnd,{passive:!0}),t[d]("pointercancel",s.onTouchEnd,{passive:!0}),t[d]("touchcancel",s.onTouchEnd,{passive:!0}),t[d]("pointerout",s.onTouchEnd,{passive:!0}),t[d]("pointerleave",s.onTouchEnd,{passive:!0}),t[d]("contextmenu",s.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&n[d]("click",s.onClick,!0),i.cssMode&&r[d]("scroll",s.onScroll),i.updateOnWindowResize?s[o](l.ios||l.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ve,!0):s[o]("observerUpdate",Ve,!0),n[d]("load",s.onLoad,{capture:!0}))};function oi(){const s=this,{params:e}=s;s.onTouchStart=ti.bind(s),s.onTouchMove=ii.bind(s),s.onTouchEnd=si.bind(s),s.onDocumentTouchStart=li.bind(s),e.cssMode&&(s.onScroll=ri.bind(s)),s.onClick=ni.bind(s),s.onLoad=ai.bind(s),$e(s,"on")}function di(){$e(this,"off")}var ci={attachEvents:oi,detachEvents:di};const He=(s,e)=>s.grid&&e.grid&&e.grid.rows>1;function ui(){const s=this,{realIndex:e,initialized:t,params:i,el:n}=s,r=i.breakpoints;if(!r||r&&Object.keys(r).length===0)return;const l=F(),a=i.breakpointsBase==="window"||!i.breakpointsBase?i.breakpointsBase:"container",d=["window","container"].includes(i.breakpointsBase)||!i.breakpointsBase?s.el:l.querySelector(i.breakpointsBase),o=s.getBreakpoint(r,a,d);if(!o||s.currentBreakpoint===o)return;const m=(o in r?r[o]:void 0)||s.originalParams,h=He(s,i),g=He(s,m),p=s.params.grabCursor,v=m.grabCursor,E=i.enabled;h&&!g?(n.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),s.emitContainerClasses()):!h&&g&&(n.classList.add(`${i.containerModifierClass}grid`),(m.grid.fill&&m.grid.fill==="column"||!m.grid.fill&&i.grid.fill==="column")&&n.classList.add(`${i.containerModifierClass}grid-column`),s.emitContainerClasses()),p&&!v?s.unsetGrabCursor():!p&&v&&s.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(x=>{if(typeof m[x]>"u")return;const C=i[x]&&i[x].enabled,I=m[x]&&m[x].enabled;C&&!I&&s[x].disable(),!C&&I&&s[x].enable()});const S=m.direction&&m.direction!==i.direction,c=i.loop&&(m.slidesPerView!==i.slidesPerView||S),f=i.loop;S&&t&&s.changeDirection(),$(s.params,m);const w=s.params.enabled,T=s.params.loop;Object.assign(s,{allowTouchMove:s.params.allowTouchMove,allowSlideNext:s.params.allowSlideNext,allowSlidePrev:s.params.allowSlidePrev}),E&&!w?s.disable():!E&&w&&s.enable(),s.currentBreakpoint=o,s.emit("_beforeBreakpoint",m),t&&(c?(s.loopDestroy(),s.loopCreate(e),s.updateSlides()):!f&&T?(s.loopCreate(e),s.updateSlides()):f&&!T&&s.loopDestroy()),s.emit("breakpoint",m)}function fi(s,e,t){if(e===void 0&&(e="window"),!s||e==="container"&&!t)return;let i=!1;const n=G(),r=e==="window"?n.innerHeight:t.clientHeight,l=Object.keys(s).map(a=>{if(typeof a=="string"&&a.indexOf("@")===0){const d=parseFloat(a.substr(1));return{value:r*d,point:a}}return{value:a,point:a}});l.sort((a,d)=>parseInt(a.value,10)-parseInt(d.value,10));for(let a=0;a<l.length;a+=1){const{point:d,value:o}=l[a];e==="window"?n.matchMedia(`(min-width: ${o}px)`).matches&&(i=d):o<=t.clientWidth&&(i=d)}return i||"max"}var pi={setBreakpoint:ui,getBreakpoint:fi};function mi(s,e){const t=[];return s.forEach(i=>{typeof i=="object"?Object.keys(i).forEach(n=>{i[n]&&t.push(e+n)}):typeof i=="string"&&t.push(e+i)}),t}function hi(){const s=this,{classNames:e,params:t,rtl:i,el:n,device:r}=s,l=mi(["initialized",t.direction,{"free-mode":s.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&t.grid.fill==="column"},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...l),n.classList.add(...e),s.emitContainerClasses()}function gi(){const s=this,{el:e,classNames:t}=s;!e||typeof e=="string"||(e.classList.remove(...t),s.emitContainerClasses())}var vi={addClasses:hi,removeClasses:gi};function wi(){const s=this,{isLocked:e,params:t}=s,{slidesOffsetBefore:i}=t;if(i){const n=s.slides.length-1,r=s.slidesGrid[n]+s.slidesSizesGrid[n]+i*2;s.isLocked=s.size>r}else s.isLocked=s.snapGrid.length===1;t.allowSlideNext===!0&&(s.allowSlideNext=!s.isLocked),t.allowSlidePrev===!0&&(s.allowSlidePrev=!s.isLocked),e&&e!==s.isLocked&&(s.isEnd=!1),e!==s.isLocked&&s.emit(s.isLocked?"lock":"unlock")}var Si={checkOverflow:wi},Fe={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function Ti(s,e){return function(i){i===void 0&&(i={});const n=Object.keys(i)[0],r=i[n];if(typeof r!="object"||r===null){$(e,i);return}if(s[n]===!0&&(s[n]={enabled:!0}),n==="navigation"&&s[n]&&s[n].enabled&&!s[n].prevEl&&!s[n].nextEl&&(s[n].auto=!0),["pagination","scrollbar"].indexOf(n)>=0&&s[n]&&s[n].enabled&&!s[n].el&&(s[n].auto=!0),!(n in s&&"enabled"in r)){$(e,i);return}typeof s[n]=="object"&&!("enabled"in s[n])&&(s[n].enabled=!0),s[n]||(s[n]={enabled:!1}),$(e,i)}}const ye={eventsEmitter:vt,update:Mt,translate:Nt,transition:Gt,slide:qt,loop:Kt,grabCursor:Zt,events:ci,breakpoints:pi,checkOverflow:Si,classes:vi},Ee={};class H{constructor(){let e,t;for(var i=arguments.length,n=new Array(i),r=0;r<i;r++)n[r]=arguments[r];n.length===1&&n[0].constructor&&Object.prototype.toString.call(n[0]).slice(8,-1)==="Object"?t=n[0]:[e,t]=n,t||(t={}),t=$({},t),e&&!t.el&&(t.el=e);const l=F();if(t.el&&typeof t.el=="string"&&l.querySelectorAll(t.el).length>1){const u=[];return l.querySelectorAll(t.el).forEach(m=>{const h=$({},t,{el:m});u.push(new H(h))}),u}const a=this;a.__swiper__=!0,a.support=Ne(),a.device=ze({userAgent:t.userAgent}),a.browser=ke(),a.eventsListeners={},a.eventsAnyListeners=[],a.modules=[...a.__modules__],t.modules&&Array.isArray(t.modules)&&a.modules.push(...t.modules);const d={};a.modules.forEach(u=>{u({params:t,swiper:a,extendParams:Ti(t,d),on:a.on.bind(a),once:a.once.bind(a),off:a.off.bind(a),emit:a.emit.bind(a)})});const o=$({},Fe,d);return a.params=$({},o,Ee,t),a.originalParams=$({},a.params),a.passedParams=$({},t),a.params&&a.params.on&&Object.keys(a.params.on).forEach(u=>{a.on(u,a.params.on[u])}),a.params&&a.params.onAny&&a.onAny(a.params.onAny),Object.assign(a,{enabled:a.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return a.params.direction==="horizontal"},isVertical(){return a.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:a.params.allowSlideNext,allowSlidePrev:a.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:a.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:a.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),a.emit("_swiper"),a.params.init&&a.init(),a}getDirectionLabel(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}getSlideIndex(e){const{slidesEl:t,params:i}=this,n=W(t,`.${i.slideClass}, swiper-slide`),r=le(n[0]);return le(e)-r}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>t.getAttribute("data-swiper-slide-index")*1===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&(this.params.grid.fill==="column"?e=Math.floor(e/this.params.grid.rows):this.params.grid.fill==="row"&&(e=e%Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){const e=this,{slidesEl:t,params:i}=e;e.slides=W(t,`.${i.slideClass}, swiper-slide`)}enable(){const e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){const e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){const i=this;e=Math.min(Math.max(e,0),1);const n=i.minTranslate(),l=(i.maxTranslate()-n)*e+n;i.translateTo(l,typeof t>"u"?0:t),i.updateActiveIndex(),i.updateSlidesClasses()}emitContainerClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=e.el.className.split(" ").filter(i=>i.indexOf("swiper")===0||i.indexOf(e.params.containerModifierClass)===0);e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){const t=this;return t.destroyed?"":e.className.split(" ").filter(i=>i.indexOf("swiper-slide")===0||i.indexOf(t.params.slideClass)===0).join(" ")}emitSlidesClasses(){const e=this;if(!e.params._emitClasses||!e.el)return;const t=[];e.slides.forEach(i=>{const n=e.getSlideClasses(i);t.push({slideEl:i,classNames:n}),e.emit("_slideClass",i,n)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){e===void 0&&(e="current"),t===void 0&&(t=!1);const i=this,{params:n,slides:r,slidesGrid:l,slidesSizesGrid:a,size:d,activeIndex:o}=i;let u=1;if(typeof n.slidesPerView=="number")return n.slidesPerView;if(n.centeredSlides){let m=r[o]?Math.ceil(r[o].swiperSlideSize):0,h;for(let g=o+1;g<r.length;g+=1)r[g]&&!h&&(m+=Math.ceil(r[g].swiperSlideSize),u+=1,m>d&&(h=!0));for(let g=o-1;g>=0;g-=1)r[g]&&!h&&(m+=r[g].swiperSlideSize,u+=1,m>d&&(h=!0))}else if(e==="current")for(let m=o+1;m<r.length;m+=1)(t?l[m]+a[m]-l[o]<d:l[m]-l[o]<d)&&(u+=1);else for(let m=o-1;m>=0;m-=1)l[o]-l[m]<d&&(u+=1);return u}update(){const e=this;if(!e||e.destroyed)return;const{snapGrid:t,params:i}=e;i.breakpoints&&e.setBreakpoint(),[...e.el.querySelectorAll('[loading="lazy"]')].forEach(l=>{l.complete&&de(e,l)}),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses();function n(){const l=e.rtlTranslate?e.translate*-1:e.translate,a=Math.min(Math.max(l,e.maxTranslate()),e.minTranslate());e.setTranslate(a),e.updateActiveIndex(),e.updateSlidesClasses()}let r;if(i.freeMode&&i.freeMode.enabled&&!i.cssMode)n(),i.autoHeight&&e.updateAutoHeight();else{if((i.slidesPerView==="auto"||i.slidesPerView>1)&&e.isEnd&&!i.centeredSlides){const l=e.virtual&&i.virtual.enabled?e.virtual.slides:e.slides;r=e.slideTo(l.length-1,0,!1,!0)}else r=e.slideTo(e.activeIndex,0,!1,!0);r||n()}i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t){t===void 0&&(t=!0);const i=this,n=i.params.direction;return e||(e=n==="horizontal"?"vertical":"horizontal"),e===n||e!=="horizontal"&&e!=="vertical"||(i.el.classList.remove(`${i.params.containerModifierClass}${n}`),i.el.classList.add(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.forEach(r=>{e==="vertical"?r.style.width="":r.style.height=""}),i.emit("changeDirection"),t&&i.update()),i}changeLanguageDirection(e){const t=this;t.rtl&&e==="rtl"||!t.rtl&&e==="ltr"||(t.rtl=e==="rtl",t.rtlTranslate=t.params.direction==="horizontal"&&t.rtl,t.rtl?(t.el.classList.add(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.el.classList.remove(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){const t=this;if(t.mounted)return!0;let i=e||t.params.el;if(typeof i=="string"&&(i=document.querySelector(i)),!i)return!1;i.swiper=t,i.parentNode&&i.parentNode.host&&i.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);const n=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`;let l=i&&i.shadowRoot&&i.shadowRoot.querySelector?i.shadowRoot.querySelector(n()):W(i,n())[0];return!l&&t.params.createElements&&(l=ae("div",t.params.wrapperClass),i.append(l),W(i,`.${t.params.slideClass}`).forEach(a=>{l.append(a)})),Object.assign(t,{el:i,wrapperEl:l,slidesEl:t.isElement&&!i.parentNode.host.slideSlots?i.parentNode.host:l,hostEl:t.isElement?i.parentNode.host:i,mounted:!0,rtl:i.dir.toLowerCase()==="rtl"||X(i,"direction")==="rtl",rtlTranslate:t.params.direction==="horizontal"&&(i.dir.toLowerCase()==="rtl"||X(i,"direction")==="rtl"),wrongRTL:X(l,"display")==="-webkit-box"}),!0}init(e){const t=this;if(t.initialized||t.mount(e)===!1)return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();const n=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&n.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),n.forEach(r=>{r.complete?de(t,r):r.addEventListener("load",l=>{de(t,l.target)})}),Te(t),t.initialized=!0,Te(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){e===void 0&&(e=!0),t===void 0&&(t=!0);const i=this,{params:n,el:r,wrapperEl:l,slides:a}=i;return typeof i.params>"u"||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),n.loop&&i.loopDestroy(),t&&(i.removeClasses(),r&&typeof r!="string"&&r.removeAttribute("style"),l&&l.removeAttribute("style"),a&&a.length&&a.forEach(d=>{d.classList.remove(n.slideVisibleClass,n.slideFullyVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass),d.removeAttribute("style"),d.removeAttribute("data-swiper-slide-index")})),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(d=>{i.off(d)}),e!==!1&&(i.el&&typeof i.el!="string"&&(i.el.swiper=null),Je(i)),i.destroyed=!0),null}static extendDefaults(e){$(Ee,e)}static get extendedDefaults(){return Ee}static get defaults(){return Fe}static installModule(e){H.prototype.__modules__||(H.prototype.__modules__=[]);const t=H.prototype.__modules__;typeof e=="function"&&t.indexOf(e)<0&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(t=>H.installModule(t)),H):(H.installModule(e),H)}}Object.keys(ye).forEach(s=>{Object.keys(ye[s]).forEach(e=>{H.prototype[e]=ye[s][e]})}),H.use([ht,gt]);class yi{constructor(){this.isDestroyed=!1}initialize(e){try{return this.destroy(),this.swiperInstance=new H(`.${N.slider.dom.swiperClass}`,e),this.isDestroyed=!1,this.swiperInstance}catch{return}}destroy(){if(this.swiperInstance&&!this.isDestroyed)try{this.swiperInstance.destroy(!0,!0)}catch{}finally{delete this.swiperInstance,this.isDestroyed=!0}}isInitialized(){return this.swiperInstance!==null&&!this.isDestroyed}getInstance(){if(this.isInitialized())return this.swiperInstance}reinitialize(e){return this.destroy(),this.initialize(e)}isContainerAvailable(){try{return document.querySelector(`.${N.slider.dom.swiperClass}`)!==null}catch{return!1}}initializeWithDelay(e,t=ee.CHECK_INTERVAL){setTimeout(()=>{this.isContainerAvailable()&&this.initialize(e)},t)}getStatus(){return{isInitialized:this.isInitialized(),isDestroyed:this.isDestroyed,hasInstance:this.swiperInstance!==null,containerAvailable:this.isContainerAvailable()}}}class Ei{constructor(){this.checkTime=N.slider.checkTime,this.configManager=new dt,this.domBuilder=new ct,this.dataManager=new ut,this.lifecycleManager=new yi}attachAdvertiseHeader(e){try{this.destroy();const t=this.dataManager.fetchSlideData();if(t.length===V.EMPTY_LENGTH)return;const i=this.domBuilder.createContainer(),n=this.domBuilder.createSwiperElement(i),r=this.domBuilder.createSwiperWrapper(n),l=this.populateSlides(r,t);this.domBuilder.createPagination(n),this.domBuilder.createNavigation(n),this.domBuilder.appendToDOM(i),setTimeout(()=>{this.initializeSwiper(l)},this.checkTime)}catch{}}populateSlides(e,t){let i=V.EMPTY_LENGTH;for(const n of t)if(n.isValid){const r=this.domBuilder.createSlide(n.imageSrc,n.imageLink);K(e,r),i+=Z.SLIDE_INCREMENT}return i}initializeSwiper(e){try{const t=this.dataManager.getTransitionTime(),i=this.configManager.calculateConfiguration(e,t);if(!this.configManager.validateConfiguration(i.finalConfig).isValid)return;this.lifecycleManager.initialize(i.finalConfig)}catch{}}destroy(){this.lifecycleManager.destroy(),this.domBuilder.cleanup()}}class Q{constructor(){this.errorLog=[],this.isInitialized=!1}static getInstance(){return Q.instance||(Q.instance=new Q),Q.instance}initialize(){try{return this.isInitialized||(this.setupGlobalErrorHandling(),this.isInitialized=!0),!0}catch{return!1}}handleSync(e,t){try{return e()}catch(i){return this.logError(i,t),!1}}handleAsync(e,t){return e().catch(i=>(this.logError(i,t),!1))}logError(e,t){try{const i={timestamp:new Date,error:e,context:t};this.errorLog.push(i),this.errorLog.length>Ye.MAX_ERROR_LOG_ENTRIES&&this.errorLog.shift()}catch{}}setupGlobalErrorHandling(){try{globalThis.addEventListener("unhandledrejection",e=>{this.logError(new Error(String(e.reason)),"Unhandled Promise Rejection")})}catch{}}getErrorLog(){return[...this.errorLog]}clearErrorLog(){this.errorLog=[]}}class Y{constructor(){}static getInstance(){return Y.instance||(Y.instance=new Y),Y.instance}isTagsPage(){try{return q.current.get("routeName")==="tags"}catch{try{return globalThis.location.pathname.includes("/tags")}catch{return!1}}}getConfig(){return N}isSlideshowConfigured(){try{for(let i=1;i<=N.slider.maxSlides;i+=1)if(q.forum.attribute(`wusong8899-header-advertisement.Image${i}`))return!0;return!1}catch{return!1}}}q.initializers.add(N.app.extensionId,()=>{const s=Q.getInstance(),e=Y.getInstance();if(!s.initialize())return;const t=new Ei;We.extend(Xe.prototype,"view",function(n){s.handleSync(()=>{e.isTagsPage()&&bi(n,t)},"HeaderPrimary view extension")})});const bi=(s,e,t)=>{try{if(Y.getInstance().isSlideshowConfigured())try{e.attachAdvertiseHeader(s)}catch{}!q.session.user&&De()&&xi()}catch{}},xi=()=>{let s=document.getElementById(N.ui.headerIconId);if(s===null){const e=q.forum.attribute("wusong8899-header-advertisement.HeaderIconUrl")||N.ui.headerIconUrl;s=document.createElement("div"),s.id=N.ui.headerIconId,s.className="HeaderIcon-container mobile-only",s.innerHTML=`<img src="${e}" alt="Header Icon" class="HeaderIcon-image" />`;const t=document.querySelector("#app-navigation .Navigation.ButtonGroup.App-backControl");t&&t.appendChild(s)}}})(flarum.core.compat["common/extend"],flarum.core.compat["forum/app"],flarum.core.compat["forum/components/HeaderPrimary"]);
//# sourceMappingURL=forum.js.map

module.exports={};